!function(){"use strict";const e=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(n=r=e,(o=String).prototype.isPrototypeOf(n)||(null===(s=r.constructor)||void 0===s?void 0:s.name)===o.name)?"string":t;var n,r,o,s})(t)===e,t=e=>t=>typeof t===e,n=e=>t=>e===t,r=e("string"),o=e("object"),s=e("array"),i=n(null),l=t("boolean"),a=n(void 0),c=e=>!(e=>null==e)(e),u=t("function"),d=t("number"),m=()=>{},h=e=>()=>e;function g(e,...t){return(...n)=>{const r=t.concat(n);return e.apply(null,r)}}const p=h(!1),f=h(!0);class v{constructor(e,t){this.tag=e,this.value=t}static some(e){return new v(!0,e)}static none(){return v.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?v.some(e(this.value)):v.none()}bind(e){return this.tag?e(this.value):v.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:v.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return c(e)?v.some(e):v.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}v.singletonNone=new v(!1);const w=Array.prototype.push,y=(e,t)=>{const n=e.length,r=new Array(n);for(let o=0;o<n;o++){const n=e[o];r[o]=t(n,o)}return r},b=(e,t)=>{for(let n=0,r=e.length;n<r;n++)t(e[n],n)},S=(e,t)=>{const n=[];for(let r=0,o=e.length;r<o;r++){const o=e[r];t(o,r)&&n.push(o)}return n},x=(e,t)=>((e,t,n)=>{for(let r=0,o=e.length;r<o;r++){const o=e[r];if(t(o,r))return v.some(o);if(n(o,r))break}return v.none()})(e,t,p),E=Object.keys,F=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},O="undefined"!=typeof window?window:Function("return this;")(),k=(e,t)=>((e,t)=>{let n=null!=t?t:O;for(let t=0;t<e.length&&null!=n;++t)n=n[e[t]];return n})(e.split("."),t),T=e=>{const t=F(v.none()),n=()=>t.get().each(e);return{clear:()=>{n(),t.set(v.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{n(),t.set(v.some(e))}}},C=()=>T((e=>e.unbind())),A=(e,t,n=0,r)=>{const o=e.indexOf(t,n);return-1!==o&&(!!a(r)||o+t.length<=r)};var R=tinymce.util.Tools.resolve("tinymce.PluginManager");const L=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},M=L,P=(e,t)=>{const n=e.dom;if(1!==n.nodeType)return!1;{const e=n;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},D=()=>N(0,0),N=(e,t)=>({major:e,minor:t}),H={nu:N,detect:(e,t)=>{const n=String(t).toLowerCase();return 0===e.length?D():((e,t)=>{const n=((e,t)=>{for(let n=0;n<e.length;n++){const r=e[n];if(r.test(t))return r}})(e,t);if(!n)return{major:0,minor:0};const r=e=>Number(t.replace(n,"$"+e));return N(r(1),r(2))})(e,n)},unknown:D},V=(e,t)=>{const n=String(t).toLowerCase();return x(e,(e=>e.search(n)))},W=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,q=e=>t=>A(t,e),B=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>A(e,"edge/")&&A(e,"chrome")&&A(e,"safari")&&A(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,W],search:e=>A(e,"chrome")&&!A(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>A(e,"msie")||A(e,"trident")},{name:"Opera",versionRegexes:[W,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:q("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:q("firefox")},{name:"Safari",versionRegexes:[W,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(A(e,"safari")||A(e,"mobile/"))&&A(e,"applewebkit")}],I=[{name:"Windows",search:q("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>A(e,"iphone")||A(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:q("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:q("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:q("linux"),versionRegexes:[]},{name:"Solaris",search:q("sunos"),versionRegexes:[]},{name:"FreeBSD",search:q("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:q("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],j={browsers:h(B),oses:h(I)},_="Edge",z="Chromium",K="Opera",$="Firefox",U="Safari",X=e=>{const t=e.current,n=e.version,r=e=>()=>t===e;return{current:t,version:n,isEdge:r(_),isChromium:r(z),isIE:r("IE"),isOpera:r(K),isFirefox:r($),isSafari:r(U)}},Y=()=>X({current:void 0,version:H.unknown()}),G=X,J=(h(_),h(z),h("IE"),h(K),h($),h(U),"Windows"),Q="Android",Z="Linux",ee="macOS",te="Solaris",ne="FreeBSD",re="ChromeOS",oe=e=>{const t=e.current,n=e.version,r=e=>()=>t===e;return{current:t,version:n,isWindows:r(J),isiOS:r("iOS"),isAndroid:r(Q),isMacOS:r(ee),isLinux:r(Z),isSolaris:r(te),isFreeBSD:r(ne),isChromeOS:r(re)}},se=()=>oe({current:void 0,version:H.unknown()}),ie=oe,le=(h(J),h("iOS"),h(Q),h(Z),h(ee),h(te),h(ne),h(re),(e,t,n)=>{const r=j.browsers(),o=j.oses(),s=t.bind((e=>((e,t)=>((e,t)=>{for(let n=0;n<e.length;n++){const r=t(e[n]);if(r.isSome())return r}return v.none()})(t.brands,(t=>{const n=t.brand.toLowerCase();return x(e,(e=>{var t;return n===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:H.nu(parseInt(t.version,10),0)})))})))(r,e))).orThunk((()=>((e,t)=>V(e,t).map((e=>{const n=H.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(r,e))).fold(Y,G),i=((e,t)=>V(e,t).map((e=>{const n=H.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(o,e).fold(se,ie),l=((e,t,n,r)=>{const o=e.isiOS()&&!0===/ipad/i.test(n),s=e.isiOS()&&!o,i=e.isiOS()||e.isAndroid(),l=i||r("(pointer:coarse)"),a=o||!s&&i&&r("(min-device-width:768px)"),c=s||i&&!a,u=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(n),d=!c&&!a&&!u;return{isiPad:h(o),isiPhone:h(s),isTablet:h(a),isPhone:h(c),isTouch:h(l),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:h(u),isDesktop:h(d)}})(i,s,e,n);return{browser:s,os:i,deviceType:l}}),ae=e=>window.matchMedia(e).matches;let ce=(e=>{let t,n=!1;return(...r)=>(n||(n=!0,t=e.apply(null,r)),t)})((()=>le(window.navigator.userAgent,v.from(window.navigator.userAgentData),ae)));const ue=Object.getPrototypeOf,de=e=>{const t=k("ownerDocument.defaultView",e);return o(e)&&((e=>((e,t)=>{const n=((e,t)=>k(e,t))(e,t);if(null==n)throw new Error(e+" not available on this browser");return n})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(ue(e).constructor.name))},me=e=>t=>(e=>e.dom.nodeType)(t)===e,he=me(1),ge=me(3),pe=me(11),fe=e=>M(e.dom.ownerDocument),ve=e=>y(e.dom.childNodes,M),we=e=>{const t=(e=>M(e.dom.getRootNode()))(e);return pe(n=t)&&c(n.dom.host)?v.some(t):v.none();var n},ye=e=>M(e.dom.host),be=e=>{const t=M((e=>{if(c(e.target)){const t=M(e.target);if(he(t)&&c(t.dom.shadowRoot)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return(e=>0<e.length?v.some(e[0]):v.none())(t)}}return v.from(e.target)})(e).getOr(e.target)),n=()=>e.stopPropagation(),r=()=>e.preventDefault(),o=(s=r,i=n,(...e)=>s(i.apply(null,e)));var s,i;return((e,t,n,r,o,s,i)=>({target:e,x:t,y:n,stop:r,prevent:o,kill:s,raw:i}))(t,e.clientX,e.clientY,n,r,o,e)},Se=(e,t,n,r)=>{e.dom.removeEventListener(t,n,r)},xe=f,Ee=(e,t,n)=>((e,t,n,r)=>((e,t,n,r,o)=>{const s=((e,t)=>n=>{e(n)&&t(be(n))})(n,r);return e.dom.addEventListener(t,s,o),{unbind:g(Se,e,t,s,o)}})(e,t,n,r,!1))(e,t,xe,n),Fe=(e,t)=>{const n=e.dom.getAttribute(t);return null===n?void 0:n},Oe=(e,t)=>{e.dom.removeAttribute(t)},ke=e=>void 0!==e.style&&u(e.style.getPropertyValue),Te=e=>{const t=ge(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const n=t.ownerDocument;return we(M(t)).fold((()=>n.body.contains(t)),(r=Te,o=ye,e=>r(o(e))));var r,o},Ce=(e,t,n)=>{if(!r(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);ke(e)&&e.style.setProperty(t,n)},Ae=(e,t,n)=>{const r=e.dom;Ce(r,t,n)},Re=(e,t)=>{const n=e.dom;((e,t)=>{const n=E(e);for(let r=0,o=n.length;r<o;r++){const o=n[r];t(e[o],o)}})(t,((e,t)=>{Ce(n,t,e)}))},Le=(e,t)=>{const n=e.dom,r=window.getComputedStyle(n).getPropertyValue(t);return""!==r||Te(e)?r:Me(n,t)},Me=(e,t)=>ke(e)?e.style.getPropertyValue(t):"",Pe=(e,t)=>({left:e,top:t,translate:(n,r)=>Pe(e+n,t+r)}),De=Pe,Ne=(e,t,n)=>((e,t,n)=>S(((e,t)=>{const n=u(t)?t:p;let r=e.dom;const o=[];for(;null!==r.parentNode&&void 0!==r.parentNode;){const e=r.parentNode,t=M(e);if(o.push(t),!0===n(t))break;r=e}return o})(e,n),t))(e,(e=>P(e,t)),n),He=(e,t)=>(e=>{return S((e=>v.from(e.dom.parentNode).map(M))(n=e).map(ve).map((e=>S(e,(e=>{return t=e,!(n.dom===t.dom);var t})))).getOr([]),(e=>P(e,t)));var n})(e),Ve=e=>{const t=void 0===e?window:e;return ce().browser.isFirefox()?v.none():v.from(t.visualViewport)},We=(e,t,n,r)=>({x:e,y:t,width:n,height:r,right:e+n,bottom:t+r}),qe=e=>{const t=void 0===e?window:e,n=t.document,r=(e=>{const t=void 0!==e?e.dom:document,n=t.body.scrollLeft||t.documentElement.scrollLeft,r=t.body.scrollTop||t.documentElement.scrollTop;return De(n,r)})(M(n));return Ve(t).fold((()=>{const e=t.document.documentElement,n=e.clientWidth,o=e.clientHeight;return We(r.left,r.top,n,o)}),(e=>We(Math.max(e.pageLeft,r.left),Math.max(e.pageTop,r.top),e.width,e.height)))},Be=(e,t,n)=>Ve(n).map((n=>{const r=e=>t(be(e));return n.addEventListener(e,r),{unbind:()=>n.removeEventListener(e,r)}})).getOrThunk((()=>({unbind:m})));var Ie=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),je=tinymce.util.Tools.resolve("tinymce.Env");const _e=(e,t)=>{e.dispatch("FullscreenStateChanged",{state:t}),e.dispatch("ResizeEditor")},ze=e=>e.options.get("fullscreen_native");const Ke=e=>{return e.dom===(void 0!==(t=fe(e).dom).fullscreenElement?t.fullscreenElement:void 0!==t.msFullscreenElement?t.msFullscreenElement:void 0!==t.webkitFullscreenElement?t.webkitFullscreenElement:null);var t},$e="data-ephox-mobile-fullscreen-style",Ue="position:absolute!important;",Xe="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",Ye=je.os.isAndroid(),Ge=(e,t,n)=>{const o=t=>n=>{const o=Fe(n,"style"),s=void 0===o?"no-styles":o.trim();s!==t&&(((e,t,n)=>{((e,t,n)=>{if(!(r(n)||l(n)||d(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")})(e.dom,t,n)})(n,$e,s),Re(n,e.parseStyle(t)))},i=Ne(t,"*"),a=(e=>{const t=[];for(let n=0,r=e.length;n<r;++n){if(!s(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);w.apply(t,e[n])}return t})(y(i,(e=>He(e,"*:not(.tox-silver-sink)")))),c=(e=>{const t=Le(e,"background-color");return void 0!==t&&""!==t?"background-color:"+t+"!important":"background-color:rgb(255,255,255)!important;"})(n);b(a,o("display:none!important;")),b(i,o(Ue+Xe+c)),o((!0===Ye?"":Ue)+Xe+c)(t)},Je=Ie.DOM,Qe=Ve().fold((()=>({bind:m,unbind:m})),(e=>{const t=(()=>{const e=T(m);return{...e,on:t=>e.get().each(t)}})(),n=C(),r=C(),o=(e=>{let t=null;return{cancel:()=>{i(t)||(clearTimeout(t),t=null)},throttle:(...n)=>{i(t)&&(t=setTimeout((()=>{t=null,e.apply(null,n)}),50))}}})((()=>{document.body.scrollTop=0,document.documentElement.scrollTop=0,window.requestAnimationFrame((()=>{t.on((t=>Re(t,{top:e.offsetTop+"px",left:e.offsetLeft+"px",height:e.height+"px",width:e.width+"px"})))}))}));return{bind:e=>{t.set(e),o.throttle(),n.set(Be("resize",o.throttle)),r.set(Be("scroll",o.throttle))},unbind:()=>{t.on((()=>{n.clear(),r.clear()})),t.clear()}}})),Ze=(e,t)=>{const n=document.body,r=document.documentElement,o=e.getContainer(),s=M(o),i=(l=s,v.from(l.dom.nextSibling).map(M)).filter((e=>(e=>he(e)&&de(e.dom))(e)&&(e=>(e=>void 0!==e.dom.classList)(e)&&e.dom.classList.contains("tox-silver-sink"))(e)));var l;const a=(e=>{const t=M(e.getElement());return we(t).map(ye).getOrThunk((()=>(e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return M(t)})(fe(t))))})(e),c=t.get(),u=M(e.getBody()),d=je.deviceType.isTouch(),m=o.style,h=e.iframeElement,g=null==h?void 0:h.style,p=e=>{e(n,"tox-fullscreen"),e(r,"tox-fullscreen"),e(o,"tox-fullscreen"),we(s).map((e=>ye(e).dom)).each((t=>{e(t,"tox-fullscreen"),e(t,"tox-shadowhost")}))},f=()=>{d&&(e=>{const t=(e=>{const t=document;return 1!==(n=t).nodeType&&9!==n.nodeType&&11!==n.nodeType||0===n.childElementCount?[]:y(t.querySelectorAll(e),M);var n})("["+$e+"]");b(t,(t=>{const n=Fe(t,$e);n&&"no-styles"!==n?Re(t,e.parseStyle(n)):Oe(t,"style"),Oe(t,$e)}))})(e.dom),p(Je.removeClass),Qe.unbind(),v.from(t.get()).each((e=>e.fullscreenChangeHandler.unbind()))};if(c)c.fullscreenChangeHandler.unbind(),ze(e)&&Ke(a)&&(e=>{const t=e.dom;t.exitFullscreen?t.exitFullscreen():t.msExitFullscreen?t.msExitFullscreen():t.webkitCancelFullScreen&&t.webkitCancelFullScreen()})(fe(a)),g.width=c.iframeWidth,g.height=c.iframeHeight,m.width=c.containerWidth,m.height=c.containerHeight,m.top=c.containerTop,m.left=c.containerLeft,S=i,x=c.sinkCssPosition,E=(e,t)=>{Ae(e,"position",t)},S.isSome()&&x.isSome()?v.some(E(S.getOrDie(),x.getOrDie())):v.none(),f(),w=c.scrollPos,window.scrollTo(w.x,w.y),t.set(null),_e(e,!1),e.off("remove",f);else{const n=Ee(fe(a),void 0!==document.fullscreenElement?"fullscreenchange":void 0!==document.msFullscreenElement?"MSFullscreenChange":void 0!==document.webkitFullscreenElement?"webkitfullscreenchange":"fullscreenchange",(n=>{ze(e)&&(Ke(a)||null===t.get()||Ze(e,t))})),r={scrollPos:qe(window),containerWidth:m.width,containerHeight:m.height,containerTop:m.top,containerLeft:m.left,iframeWidth:g.width,iframeHeight:g.height,fullscreenChangeHandler:n,sinkCssPosition:i.map((e=>Le(e,"position")))};d&&Ge(e.dom,s,u),g.width=g.height="100%",m.width=m.height="",p(Je.addClass),i.each((e=>{Ae(e,"position","fixed")})),Qe.bind(s),e.on("remove",f),t.set(r),ze(e)&&(e=>{const t=e.dom;t.requestFullscreen?t.requestFullscreen():t.msRequestFullscreen?t.msRequestFullscreen():t.webkitRequestFullScreen&&t.webkitRequestFullScreen()})(a),_e(e,!0)}var w,S,x,E};var et=tinymce.util.Tools.resolve("tinymce.util.VK");const tt=(e,t)=>n=>{n.setActive(null!==t.get());const r=e=>n.setActive(e.state);return e.on("FullscreenStateChanged",r),()=>e.off("FullscreenStateChanged",r)};R.add("fullscreen",(e=>{const t=F(null);return e.inline||((e=>{(0,e.options.register)("fullscreen_native",{processor:"boolean",default:!1})})(e),((e,t)=>{e.addCommand("mceFullScreen",(()=>{Ze(e,t)}))})(e,t),((e,t)=>{const n=()=>e.execCommand("mceFullScreen");e.ui.registry.addToggleMenuItem("fullscreen",{text:"Fullscreen",icon:"fullscreen",shortcut:"Meta+Shift+F",onAction:n,onSetup:tt(e,t),context:"any"}),e.ui.registry.addToggleButton("fullscreen",{tooltip:"Fullscreen",icon:"fullscreen",onAction:n,onSetup:tt(e,t),shortcut:"Meta+Shift+F",context:"any"})})(e,t),((e,t)=>{e.on("init",(()=>{e.on("keydown",(e=>{e.keyCode!==et.TAB||e.metaKey||e.ctrlKey||!t.get()||e.preventDefault()}))}))})(e,t),e.addShortcut("Meta+Shift+F","","mceFullScreen")),(e=>({isFullscreen:()=>null!==e.get()}))(t)}))}();