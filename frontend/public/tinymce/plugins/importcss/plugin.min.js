!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(s=r=e,(o=String).prototype.isPrototypeOf(s)||(null===(n=r.constructor)||void 0===n?void 0:n.name)===o.name)?"string":t;var s,r,o,n})(t)===e,s=t("string"),r=t("object"),o=t("array"),n=e=>"function"==typeof e;Array.prototype.slice;const c=Array.prototype.push;n(Array.from)&&Array.from;var i=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),l=tinymce.util.Tools.resolve("tinymce.EditorManager"),a=tinymce.util.Tools.resolve("tinymce.Env"),p=tinymce.util.Tools.resolve("tinymce.util.Tools");const u=e=>t=>t.options.get(e),m=u("importcss_merge_classes"),f=u("importcss_exclusive"),y=u("importcss_selector_converter"),d=u("importcss_selector_filter"),h=u("importcss_groups"),g=u("importcss_append"),_=u("importcss_file_filter"),v=u("skin"),b=u("skin_url"),x=/^\.(?:ephox|tiny-pageembed|mce)(?:[.-]+\w+)+$/,T=e=>s(e)?t=>-1!==t.indexOf(e):e instanceof RegExp?t=>e.test(t):e,S=(e,t)=>{let s={};const r=/^(?:([a-z0-9\-_]+))?(\.[a-z0-9_\-\.]+)$/i.exec(t);if(!r)return;const o=r[1],n=r[2].substr(1).split(".").join(" "),c=p.makeMap("a,img");return r[1]?(s={title:t},e.schema.getTextBlockElements()[o]?s.block=o:e.schema.getBlockElements()[o]||c[o.toLowerCase()]?s.selector=o:s.inline=o):r[2]&&(s={inline:"span",title:t.substr(1),classes:n}),m(e)?s.classes=n:s.attributes={class:n},s},A=(e,t)=>null===t||f(e),k=e=>{e.on("init",(()=>{const t=(()=>{const e=[],t=[],s={};return{addItemToGroup:(e,r)=>{s[e]?s[e].push(r):(t.push(e),s[e]=[r])},addItem:t=>{e.push(t)},toFormats:()=>{return(r=t,n=e=>{const t=s[e];return 0===t.length?[]:[{title:e,items:t}]},(e=>{const t=[];for(let s=0,r=e.length;s<r;++s){if(!o(e[s]))throw new Error("Arr.flatten item "+s+" was not an array, input: "+e);c.apply(t,e[s])}return t})(((e,t)=>{const s=e.length,r=new Array(s);for(let o=0;o<s;o++){const s=e[o];r[o]=t(s,o)}return r})(r,n))).concat(e);var r,n}}})(),r={},n=T(d(e)),u=(e=>p.map(e,(e=>p.extend({},e,{original:e,selectors:{},filter:T(e.filter)}))))(h(e)),m=(t,s)=>{if(((e,t,s,r)=>!(A(e,s)?t in r:t in s.selectors))(e,t,s,r)){((e,t,s,r)=>{A(e,s)?r[t]=!0:s.selectors[t]=!0})(e,t,s,r);const o=((e,t,s,r)=>{let o;const n=y(e);return o=r&&r.selector_converter?r.selector_converter:n||(()=>S(e,s)),o.call(t,s,r)})(e,e.plugins.importcss,t,s);if(o){const t=o.name||i.DOM.uniqueId();return e.formatter.register(t,o),{title:o.title,format:t}}}return null};p.each(((e,t,r)=>{const o=[],n={},c=(t,n)=>{let i,u=t.href;if(u=(e=>{const t=a.cacheSuffix;return s(e)&&(e=e.replace("?"+t,"").replace("&"+t,"")),e})(u),u&&(!r||r(u,n))&&!((e,t)=>{const s=v(e);if(s){const r=b(e),o=r?e.documentBaseURI.toAbsolute(r):l.baseURL+"/skins/ui/"+s,n=l.baseURL+"/skins/content/",c=e.editorManager.suffix;return t===o+"/content"+(e.inline?".inline":"")+`${c}.css`||-1!==t.indexOf(n)}return!1})(e,u)){p.each(t.imports,(e=>{c(e,!0)}));try{i=t.cssRules||t.rules}catch(e){}p.each(i,(e=>{e.styleSheet&&e.styleSheet?c(e.styleSheet,!0):e.selectorText&&p.each(e.selectorText.split(","),(e=>{o.push(p.trim(e))}))}))}};p.each(e.contentCSS,(e=>{n[e]=!0})),r||(r=(e,t)=>t||n[e]);try{p.each(t.styleSheets,(e=>{c(e)}))}catch(e){}return o})(e,e.getDoc(),T(_(e))),(e=>{if(!x.test(e)&&(!n||n(e))){const s=((e,t)=>p.grep(e,(e=>!e.filter||e.filter(t))))(u,e);if(s.length>0)p.each(s,(s=>{const r=m(e,s);r&&t.addItemToGroup(s.title,r)}));else{const s=m(e,null);s&&t.addItem(s)}}}));const f=t.toFormats();e.dispatch("addStyleModifications",{items:f,replace:!g(e)})}))};e.add("importcss",(e=>((e=>{const t=e.options.register,o=e=>s(e)||n(e)||r(e);t("importcss_merge_classes",{processor:"boolean",default:!0}),t("importcss_exclusive",{processor:"boolean",default:!0}),t("importcss_selector_converter",{processor:"function"}),t("importcss_selector_filter",{processor:o}),t("importcss_file_filter",{processor:o}),t("importcss_groups",{processor:"object[]"}),t("importcss_append",{processor:"boolean",default:!1})})(e),k(e),(e=>({convertSelectorToFormat:t=>S(e,t)}))(e))))}();