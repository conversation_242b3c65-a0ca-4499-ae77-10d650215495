!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=Object.getPrototypeOf,a=(e,t,a)=>{var i;return!!a(e,t.prototype)||(null===(i=e.constructor)||void 0===i?void 0:i.name)===t.name},i=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&a(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":t})(t)===e,s=e=>t=>typeof t===e,r=i("string"),o=i("object"),n=e=>((e,i)=>o(e)&&a(e,i,((e,a)=>t(e)===a)))(e,Object),l=i("array"),c=e=>null===e;const m=s("boolean"),d=e=>!(e=>null==e)(e),g=s("function"),u=s("number"),p=()=>{};class h{constructor(e,t){this.tag=e,this.value=t}static some(e){return new h(!0,e)}static none(){return h.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?h.some(e(this.value)):h.none()}bind(e){return this.tag?e(this.value):h.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:h.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return d(e)?h.some(e):h.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}h.singletonNone=new h(!1),Array.prototype.slice;const b=Array.prototype.push,v=e=>{const t=[];for(let a=0,i=e.length;a<i;++a){if(!l(e[a]))throw new Error("Arr.flatten item "+a+" was not an array, input: "+e);b.apply(t,e[a])}return t};g(Array.from)&&Array.from;const y=Object.keys,f=Object.hasOwnProperty,A=(e,t)=>f.call(e,t),w=(D=(e,t)=>n(e)&&n(t)?w(e,t):t,(...e)=>{if(0===e.length)throw new Error("Can't merge zero objects");const t={};for(let a=0;a<e.length;a++){const i=e[a];for(const e in i)A(i,e)&&(t[e]=D(t[e],i[e]))}return t});var D;const _=e=>e.length>0,C=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},I=C,U=(e,t,a)=>{((e,t,a)=>{if(!(r(a)||m(a)||u(a)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",a,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,a+"")})(e.dom,t,a)};var S=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),x=tinymce.util.Tools.resolve("tinymce.util.URI");const N=e=>t=>t.options.get(e),T=N("image_dimensions"),E=N("image_advtab"),L=N("image_uploadtab"),O=N("image_prepend_url"),j=N("image_class_list"),M=N("image_description"),R=N("image_title"),k=N("image_caption"),z=N("image_list"),B=N("a11y_advanced_options"),P=N("automatic_uploads"),H=(e,t)=>Math.max(parseInt(e,10),parseInt(t,10)),F=e=>(e&&(e=e.replace(/px$/,"")),e),G=e=>(e.length>0&&/^[0-9]+$/.test(e)&&(e+="px"),e),W=e=>"IMG"===e.nodeName&&(e.hasAttribute("data-mce-object")||e.hasAttribute("data-mce-placeholder")),$=(e,t)=>{const a=e.options.get;return x.isDomSafe(t,"img",{allow_html_data_urls:a("allow_html_data_urls"),allow_script_urls:a("allow_script_urls"),allow_svg_data_urls:a("allow_svg_data_urls")})},V=S.DOM,K=e=>e.style.marginLeft&&e.style.marginRight&&e.style.marginLeft===e.style.marginRight?F(e.style.marginLeft):"",Z=e=>e.style.marginTop&&e.style.marginBottom&&e.style.marginTop===e.style.marginBottom?F(e.style.marginTop):"",q=e=>e.style.borderWidth?F(e.style.borderWidth):"",J=(e,t)=>{var a;return e.hasAttribute(t)&&null!==(a=e.getAttribute(t))&&void 0!==a?a:""},Q=e=>null!==e.parentNode&&"FIGURE"===e.parentNode.nodeName,X=(e,t,a)=>{""===a||null===a?e.removeAttribute(t):e.setAttribute(t,a)},Y=(e,t)=>{const a=e.getAttribute("style"),i=t(null!==a?a:"");i.length>0?(e.setAttribute("style",i),e.setAttribute("data-mce-style",i)):e.removeAttribute("style")},ee=(e,t)=>(e,a,i)=>{const s=e.style;s[a]?(s[a]=G(i),Y(e,t)):X(e,a,i)},te=(e,t)=>e.style[t]?F(e.style[t]):J(e,t),ae=(e,t)=>{const a=G(t);e.style.marginLeft=a,e.style.marginRight=a},ie=(e,t)=>{const a=G(t);e.style.marginTop=a,e.style.marginBottom=a},se=(e,t)=>{const a=G(t);e.style.borderWidth=a},re=(e,t)=>{e.style.borderStyle=t},oe=e=>{var t;return null!==(t=e.style.borderStyle)&&void 0!==t?t:""},ne=e=>d(e)&&"FIGURE"===e.nodeName,le=e=>0===V.getAttrib(e,"alt").length&&"presentation"===V.getAttrib(e,"role"),ce=e=>le(e)?"":J(e,"alt"),me=(e,t)=>{var a;const i=document.createElement("img");return X(i,"style",t.style),(K(i)||""!==t.hspace)&&ae(i,t.hspace),(Z(i)||""!==t.vspace)&&ie(i,t.vspace),(q(i)||""!==t.border)&&se(i,t.border),(oe(i)||""!==t.borderStyle)&&re(i,t.borderStyle),e(null!==(a=i.getAttribute("style"))&&void 0!==a?a:"")},de=(e,t)=>({src:J(t,"src"),alt:ce(t),title:J(t,"title"),width:te(t,"width"),height:te(t,"height"),class:J(t,"class"),style:e(J(t,"style")),caption:Q(t),hspace:K(t),vspace:Z(t),border:q(t),borderStyle:oe(t),isDecorative:le(t)}),ge=(e,t,a,i,s)=>{a[i]!==t[i]&&s(e,i,String(a[i]))},ue=(e,t,a)=>{if(a){V.setAttrib(e,"role","presentation");const t=I(e);U(t,"alt","")}else{if(c(t)){I(e).dom.removeAttribute("alt")}else{const a=I(e);U(a,"alt",t)}"presentation"===V.getAttrib(e,"role")&&V.setAttrib(e,"role","")}},pe=(e,t)=>(a,i,s)=>{e(a,s),Y(a,t)},he=(e,t,a)=>{const i=de(e,a);ge(a,i,t,"caption",((e,t,a)=>(e=>{Q(e)?(e=>{const t=e.parentNode;d(t)&&(V.insertAfter(e,t),V.remove(t))})(e):(e=>{const t=V.create("figure",{class:"image"});V.insertAfter(t,e),t.appendChild(e),t.appendChild(V.create("figcaption",{contentEditable:"true"},"Caption")),t.contentEditable="false"})(e)})(e))),ge(a,i,t,"src",X),ge(a,i,t,"title",X),ge(a,i,t,"width",ee(0,e)),ge(a,i,t,"height",ee(0,e)),ge(a,i,t,"class",X),ge(a,i,t,"style",pe(((e,t)=>X(e,"style",t)),e)),ge(a,i,t,"hspace",pe(ae,e)),ge(a,i,t,"vspace",pe(ie,e)),ge(a,i,t,"border",pe(se,e)),ge(a,i,t,"borderStyle",pe(re,e)),((e,t,a)=>{a.alt===t.alt&&a.isDecorative===t.isDecorative||ue(e,a.alt,a.isDecorative)})(a,i,t)},be=(e,t)=>{const a=(e=>{if(e.margin){const t=String(e.margin).split(" ");switch(t.length){case 1:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[0],e["margin-bottom"]=e["margin-bottom"]||t[0],e["margin-left"]=e["margin-left"]||t[0];break;case 2:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[1],e["margin-bottom"]=e["margin-bottom"]||t[0],e["margin-left"]=e["margin-left"]||t[1];break;case 3:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[1],e["margin-bottom"]=e["margin-bottom"]||t[2],e["margin-left"]=e["margin-left"]||t[1];break;case 4:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[1],e["margin-bottom"]=e["margin-bottom"]||t[2],e["margin-left"]=e["margin-left"]||t[3]}delete e.margin}return e})(e.dom.styles.parse(t)),i=e.dom.styles.parse(e.dom.styles.serialize(a));return e.dom.styles.serialize(i)},ve=e=>{const t=e.selection.getNode(),a=e.dom.getParent(t,"figure.image");return a?e.dom.select("img",a)[0]:t&&("IMG"!==t.nodeName||W(t))?null:t},ye=(e,t)=>{var a;const i=e.dom,s=(t=>{const a={};var i;return((e,t,a,i)=>{((e,t)=>{const a=y(e);for(let i=0,s=a.length;i<s;i++){const s=a[i];t(e[s],s)}})(e,((e,s)=>{(t(e,s)?a:i)(e,s)}))})(t,((t,a)=>!e.schema.isValidChild(a,"figure")),(i=a,(e,t)=>{i[t]=e}),p),a})(e.schema.getTextBlockElements()),r=i.getParent(t.parentNode,(e=>{return t=s,a=e.nodeName,A(t,a)&&void 0!==t[a]&&null!==t[a];var t,a}),e.getBody());return r&&null!==(a=i.split(r,t))&&void 0!==a?a:t},fe=(e,t)=>{const a=((t,a)=>{const i=document.createElement("img");if(he((t=>be(e,t)),{...a,caption:!1},i),ue(i,a.alt,a.isDecorative),a.caption){const e=V.create("figure",{class:"image"});return e.appendChild(i),e.appendChild(V.create("figcaption",{contentEditable:"true"},"Caption")),e.contentEditable="false",e}return i})(0,t);e.dom.setAttrib(a,"data-mce-id","__mcenew"),e.focus(),e.selection.setContent(a.outerHTML);const i=e.dom.select('*[data-mce-id="__mcenew"]')[0];if(e.dom.setAttrib(i,"data-mce-id",null),ne(i)){const t=ye(e,i);e.selection.select(t)}else e.selection.select(i)},Ae=(e,t)=>{const a=ve(e);if(a){const i={...de((t=>be(e,t)),a),...t},s=((e,t)=>{const a=t.src;return{...t,src:$(e,a)?a:""}})(e,i);i.src?((e,t)=>{const a=ve(e);if(a)if(he((t=>be(e,t)),t,a),((e,t)=>{e.dom.setAttrib(t,"src",t.getAttribute("src"))})(e,a),ne(a.parentNode)){e.dom.setStyle(a,"float","");const t=a.parentNode;ye(e,t),e.selection.select(a.parentNode)}else e.selection.select(a),((e,t,a)=>{const i=()=>{a.onload=a.onerror=null,e.selection&&(e.selection.select(a),e.nodeChanged())};a.onload=()=>{t.width||t.height||!T(e)||e.dom.setAttribs(a,{width:String(a.clientWidth),height:String(a.clientHeight)}),i()},a.onerror=i})(e,t,a)})(e,s):((e,t)=>{if(t){const a=e.dom.is(t.parentNode,"figure.image")?t.parentNode:t;e.dom.remove(a),e.focus(),e.nodeChanged(),e.dom.isEmpty(e.getBody())&&(e.setContent(""),e.selection.setCursorLocation())}})(e,a)}else t.src&&fe(e,{src:"",alt:"",title:"",width:"",height:"",class:"",style:"",caption:!1,hspace:"",vspace:"",border:"",borderStyle:"",isDecorative:!1,...t})};var we=tinymce.util.Tools.resolve("tinymce.util.ImageUploader"),De=tinymce.util.Tools.resolve("tinymce.util.Tools");const _e=e=>r(e.value)?e.value:"",Ce=(e,t)=>{const a=[];return De.each(e,(e=>{const i=(e=>r(e.text)?e.text:r(e.title)?e.title:"")(e);if(void 0!==e.menu){const s=Ce(e.menu,t);a.push({text:i,items:s})}else{const s=t(e);a.push({text:i,value:s})}})),a},Ie=(e=_e)=>t=>t?h.from(t).map((t=>Ce(t,e))):h.none(),Ue=(e,t)=>(e=>{for(let i=0;i<e.length;i++){const s=(e=>A(e,"items"))(a=e[i])?Ue(a.items,t):a.value===t?h.some(a):h.none();if(s.isSome())return s}var a;return h.none()})(e),Se=Ie,xe=(e,t)=>e.bind((e=>Ue(e,t))),Ne=e=>{const t=Se((t=>e.convertURL(t.value||t.url||"","src"))),a=new Promise((a=>{((e,t)=>{const a=z(e);r(a)?fetch(a).then((e=>{e.ok&&e.json().then(t)})):g(a)?a(t):t(a)})(e,(e=>{a(t(e).map((e=>v([[{text:"None",value:""}],e]))))}))})),i=(w=j(e),Ie(_e)(w)),s=E(e),o=L(e),n=(e=>_(e.options.get("images_upload_url")))(e),l=(e=>d(e.options.get("images_upload_handler")))(e),c=(e=>{const t=ve(e);return t?de((t=>be(e,t)),t):{src:"",alt:"",title:"",width:"",height:"",class:"",style:"",caption:!1,hspace:"",vspace:"",border:"",borderStyle:"",isDecorative:!1}})(e),m=M(e),u=R(e),p=T(e),b=k(e),y=B(e),f=P(e),A=h.some(O(e)).filter((e=>r(e)&&e.length>0));var w;return a.then((e=>({image:c,imageList:e,classList:i,hasAdvTab:s,hasUploadTab:o,hasUploadUrl:n,hasUploadHandler:l,hasDescription:m,hasImageTitle:u,hasDimensions:p,hasImageCaption:b,prependURL:A,hasAccessibilityOptions:y,automaticUploads:f})))},Te=e=>{const t=e.imageList.map((e=>({name:"images",type:"listbox",label:"Image list",items:e}))),a={name:"alt",type:"input",label:"Alternative description",enabled:!(e.hasAccessibilityOptions&&e.image.isDecorative)},i=e.classList.map((e=>({name:"classes",type:"listbox",label:"Class",items:e})));return v([[{name:"src",type:"urlinput",filetype:"image",label:"Source",picker_text:"Browse files"}],t.toArray(),e.hasAccessibilityOptions&&e.hasDescription?[{type:"label",label:"Accessibility",items:[{name:"isDecorative",type:"checkbox",label:"Image is decorative"}]}]:[],e.hasDescription?[a]:[],e.hasImageTitle?[{name:"title",type:"input",label:"Image title"}]:[],e.hasDimensions?[{name:"dimensions",type:"sizeinput"}]:[],[{...(s=e.classList.isSome()&&e.hasImageCaption,s?{type:"grid",columns:2}:{type:"panel"}),items:v([i.toArray(),e.hasImageCaption?[{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[]])}]]);var s},Ee=e=>({title:"General",name:"general",items:Te(e)}),Le=Te,Oe=e=>({src:{value:e.src,meta:{}},images:e.src,alt:e.alt,title:e.title,dimensions:{width:e.width,height:e.height},classes:e.class,caption:e.caption,style:e.style,vspace:e.vspace,border:e.border,hspace:e.hspace,borderstyle:e.borderStyle,fileinput:[],isDecorative:e.isDecorative}),je=(e,t)=>({src:e.src.value,alt:null!==e.alt&&0!==e.alt.length||!t?e.alt:null,title:e.title,width:e.dimensions.width,height:e.dimensions.height,class:e.classes,style:e.style,caption:e.caption,hspace:e.hspace,vspace:e.vspace,border:e.border,borderStyle:e.borderstyle,isDecorative:e.isDecorative}),Me=(e,t,a,i)=>{((e,t)=>{const a=t.getData();((e,t)=>/^(?:[a-zA-Z]+:)?\/\//.test(t)?h.none():e.prependURL.bind((e=>t.substring(0,e.length)!==e?h.some(e+t):h.none())))(e,a.src.value).each((e=>{t.setData({src:{value:e,meta:a.src.meta}})}))})(t,i),((e,t)=>{const a=t.getData(),i=a.src.meta;if(void 0!==i){const s=w({},a);((e,t,a)=>{e.hasDescription&&r(a.alt)&&(t.alt=a.alt),e.hasAccessibilityOptions&&(t.isDecorative=a.isDecorative||t.isDecorative||!1),e.hasImageTitle&&r(a.title)&&(t.title=a.title),e.hasDimensions&&(r(a.width)&&(t.dimensions.width=a.width),r(a.height)&&(t.dimensions.height=a.height)),r(a.class)&&xe(e.classList,a.class).each((e=>{t.classes=e.value})),e.hasImageCaption&&m(a.caption)&&(t.caption=a.caption),e.hasAdvTab&&(r(a.style)&&(t.style=a.style),r(a.vspace)&&(t.vspace=a.vspace),r(a.border)&&(t.border=a.border),r(a.hspace)&&(t.hspace=a.hspace),r(a.borderstyle)&&(t.borderstyle=a.borderstyle))})(e,s,i),t.setData(s)}})(t,i),((e,t,a,i)=>{const s=i.getData(),r=s.src.value,o=s.src.meta||{};o.width||o.height||!t.hasDimensions||(_(r)?e.imageSize(r).then((e=>{a.open&&i.setData({dimensions:e})})).catch((e=>console.error(e))):i.setData({dimensions:{width:"",height:""}}))})(e,t,a,i),((e,t,a)=>{const i=a.getData(),s=xe(e.imageList,i.src.value);t.prevImage=s,a.setData({images:s.map((e=>e.value)).getOr("")})})(t,a,i)},Re=(e,t,a,i)=>{const s=i.getData();var r;i.block("Uploading image"),(r=s.fileinput,(e=>0<e.length?h.some(e[0]):h.none())(r)).fold((()=>{i.unblock()}),(s=>{const r=URL.createObjectURL(s),o=()=>{i.unblock(),URL.revokeObjectURL(r)},n=s=>{i.setData({src:{value:s,meta:{}}}),i.showTab("general"),Me(e,t,a,i),i.focus("src")};var l;(l=s,new Promise(((e,t)=>{const a=new FileReader;a.onload=()=>{e(a.result)},a.onerror=()=>{var e;t(null===(e=a.error)||void 0===e?void 0:e.message)},a.readAsDataURL(l)}))).then((a=>{const l=e.createBlobCache(s,r,a);t.automaticUploads?e.uploadImage(l).then((e=>{n(e.url),o()})).catch((t=>{o(),e.alertErr(t,(()=>{i.focus("fileinput")}))})):(e.addToBlobCache(l),n(l.blobUri()),i.unblock())}))}))},ke=(e,t,a)=>(i,s)=>{"src"===s.name?Me(e,t,a,i):"images"===s.name?((e,t,a,i)=>{const s=i.getData(),r=xe(t.imageList,s.images);r.each((e=>{const t=""===s.alt||a.prevImage.map((e=>e.text===s.alt)).getOr(!1);t?""===e.value?i.setData({src:e,alt:a.prevAlt}):i.setData({src:e,alt:e.text}):i.setData({src:e})})),a.prevImage=r,Me(e,t,a,i)})(e,t,a,i):"alt"===s.name?a.prevAlt=i.getData().alt:"fileinput"===s.name?Re(e,t,a,i):"isDecorative"===s.name&&i.setEnabled("alt",!i.getData().isDecorative)},ze=e=>()=>{e.open=!1},Be=e=>e.hasAdvTab||e.hasUploadUrl||e.hasUploadHandler?{type:"tabpanel",tabs:v([[Ee(e)],e.hasAdvTab?[{title:"Advanced",name:"advanced",items:[{type:"grid",columns:2,items:[{type:"input",label:"Vertical space",name:"vspace",inputMode:"numeric"},{type:"input",label:"Horizontal space",name:"hspace",inputMode:"numeric"},{type:"input",label:"Border width",name:"border",inputMode:"numeric"},{type:"listbox",name:"borderstyle",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]}]}]}]:[],e.hasUploadTab&&(e.hasUploadUrl||e.hasUploadHandler)?[{title:"Upload",name:"upload",items:[{type:"dropzone",name:"fileinput"}]}]:[]])}:{type:"panel",items:Le(e)},Pe=(e,t,a)=>i=>{const s=w(Oe(t.image),i.getData()),r={...s,style:me(a.normalizeCss,je(s,!1))};e.execCommand("mceUpdateImage",!1,je(r,t.hasAccessibilityOptions)),e.editorUpload.uploadImagesAuto(),i.close()},He=e=>t=>$(e,t)?(e=>new Promise((t=>{const a=document.createElement("img"),i=e=>{a.parentNode&&a.parentNode.removeChild(a),t(e)};a.addEventListener("load",(()=>{const e={width:H(a.width,a.clientWidth),height:H(a.height,a.clientHeight)};i(Promise.resolve(e))})),a.addEventListener("error",(()=>{i(Promise.reject(`Failed to get image dimensions for: ${e}`))}));const s=a.style;s.visibility="hidden",s.position="fixed",s.bottom=s.left="0px",s.width=s.height="auto",document.body.appendChild(a),a.src=e})))(e.documentBaseURI.toAbsolute(t)).then((e=>({width:String(e.width),height:String(e.height)}))):Promise.resolve({width:"",height:""}),Fe=e=>(t,a,i)=>{var s;return e.editorUpload.blobCache.create({blob:t,blobUri:a,name:null===(s=t.name)||void 0===s?void 0:s.replace(/\.[^\.]+$/,""),filename:t.name,base64:i.split(",")[1]})},Ge=e=>t=>{e.editorUpload.blobCache.add(t)},We=e=>(t,a)=>{e.windowManager.alert(t,a)},$e=e=>t=>be(e,t),Ve=e=>t=>e.dom.parseStyle(t),Ke=e=>(t,a)=>e.dom.serializeStyle(t,a),Ze=e=>t=>we(e).upload([t],!1).then((e=>{var t;return 0===e.length?Promise.reject("Failed to upload image"):!1===e[0].status?Promise.reject(null===(t=e[0].error)||void 0===t?void 0:t.message):e[0]})),qe=e=>{const t={imageSize:He(e),addToBlobCache:Ge(e),createBlobCache:Fe(e),alertErr:We(e),normalizeCss:$e(e),parseStyle:Ve(e),serializeStyle:Ke(e),uploadImage:Ze(e)};return{open:()=>{Ne(e).then((a=>{const i=(e=>({prevImage:xe(e.imageList,e.image.src),prevAlt:e.image.alt,open:!0}))(a);return{title:"Insert/Edit Image",size:"normal",body:Be(a),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:Oe(a.image),onSubmit:Pe(e,a,t),onChange:ke(t,a,i),onClose:ze(i)}})).then(e.windowManager.open)}}},Je=e=>{const t=e.attr("class");return d(t)&&/\bimage\b/.test(t)},Qe=e=>t=>{let a=t.length;const i=t=>{t.attr("contenteditable",e?"true":null)};for(;a--;){const s=t[a];Je(s)&&(s.attr("contenteditable",e?"false":null),De.each(s.getAll("figcaption"),i))}},Xe=e=>t=>{const a=()=>{t.setEnabled(e.selection.isEditable())};return e.on("NodeChange",a),a(),()=>{e.off("NodeChange",a)}};e.add("image",(e=>{(e=>{const t=e.options.register;t("image_dimensions",{processor:"boolean",default:!0}),t("image_advtab",{processor:"boolean",default:!1}),t("image_uploadtab",{processor:"boolean",default:!0}),t("image_prepend_url",{processor:"string",default:""}),t("image_class_list",{processor:"object[]"}),t("image_description",{processor:"boolean",default:!0}),t("image_title",{processor:"boolean",default:!1}),t("image_caption",{processor:"boolean",default:!1}),t("image_list",{processor:e=>{const t=!1===e||r(e)||((e,t)=>{if(l(e)){for(let a=0,i=e.length;a<i;++a)if(!t(e[a]))return!1;return!0}return!1})(e,o)||g(e);return t?{value:e,valid:t}:{valid:!1,message:"Must be false, a string, an array or a function."}},default:!1})})(e),(e=>{e.on("PreInit",(()=>{e.parser.addNodeFilter("figure",Qe(!0)),e.serializer.addNodeFilter("figure",Qe(!1))}))})(e),(e=>{e.ui.registry.addToggleButton("image",{icon:"image",tooltip:"Insert/edit image",onAction:qe(e).open,onSetup:t=>{t.setActive(d(ve(e)));const a=e.selection.selectorChangedWithUnbind("img:not([data-mce-object]):not([data-mce-placeholder]),figure.image",t.setActive).unbind,i=Xe(e)(t);return()=>{a(),i()}}}),e.ui.registry.addMenuItem("image",{icon:"image",text:"Image...",onAction:qe(e).open,onSetup:Xe(e)}),e.ui.registry.addContextMenu("image",{update:t=>e.selection.isEditable()&&(ne(t)||"IMG"===t.nodeName&&!W(t))?["image"]:[]})})(e),(e=>{e.addCommand("mceImage",qe(e).open),e.addCommand("mceUpdateImage",((t,a)=>{e.undoManager.transact((()=>Ae(e,a)))}))})(e)}))}();