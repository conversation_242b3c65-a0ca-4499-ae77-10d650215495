tinymce.Resource.add('tinymce.html-i18n.help-keynav.ko_KR',
'<h1>키보드 탐색 시작</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>메뉴 모음 포커스 표시</dt>\n' +
  '  <dd>Windows 또는 Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>도구 모음 포커스 표시</dt>\n' +
  '  <dd>Windows 또는 Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>푸터 포커스 표시</dt>\n' +
  '  <dd>Windows 또는 Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>알림 포커스</dt>\n' +
  '  <dd>Windows 또는 Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>컨텍스트 도구 모음에 포커스 표시</dt>\n' +
  '  <dd>Windows, Linux 또는 macOS: Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>첫 번째 UI 항목에서 탐색이 시작되며, 이때 첫 번째 항목이 강조 표시되거나 푸터 요소 경로에 있는\n' +
  '  경우 밑줄 표시됩니다.</p>\n' +
  '\n' +
  '<h1>UI 섹션 간 탐색</h1>\n' +
  '\n' +
  '<p>한 UI 섹션에서 다음 UI 섹션으로 이동하려면 <strong>Tab(탭)</strong>을 누릅니다.</p>\n' +
  '\n' +
  '<p>한 UI 섹션에서 이전 UI 섹션으로 돌아가려면 <strong>Shift+Tab(시프트+탭)</strong>을 누릅니다.</p>\n' +
  '\n' +
  '<p>이 UI 섹션의 <strong>Tab(탭)</strong> 순서는 다음과 같습니다.</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>메뉴 바</li>\n' +
  '  <li>각 도구 모음 그룹</li>\n' +
  '  <li>사이드바</li>\n' +
  '  <li>푸터의 요소 경로</li>\n' +
  '  <li>푸터의 단어 수 토글 버튼</li>\n' +
  '  <li>푸터의 브랜딩 링크</li>\n' +
  '  <li>푸터의 에디터 크기 변경 핸들</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>UI 섹션이 없는 경우 건너뛰기합니다.</p>\n' +
  '\n' +
  '<p>푸터에 키보드 탐색 포커스가 있고 사이드바는 보이지 않는 경우 <strong>Shift+Tab(시프트+탭)</strong>을 누르면\n' +
  '  포커스 표시가 마지막이 아닌 첫 번째 도구 모음 그룹으로 이동합니다.</p>\n' +
  '\n' +
  '<h1>UI 섹션 내 탐색</h1>\n' +
  '\n' +
  '<p>한 UI 요소에서 다음 UI 요소로 이동하려면 적절한 <strong>화살표</strong> 키를 누릅니다.</p>\n' +
  '\n' +
  '<p><strong>왼쪽</strong>과 <strong>오른쪽</strong> 화살표 키의 용도:</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>메뉴 모음에서 메뉴 항목 사이를 이동합니다.</li>\n' +
  '  <li>메뉴에서 하위 메뉴를 엽니다.</li>\n' +
  '  <li>도구 모음 그룹에서 버튼 사이를 이동합니다.</li>\n' +
  '  <li>푸터의 요소 경로에서 항목 간에 이동합니다.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>아래</strong>와 <strong>위</strong> 화살표 키의 용도:</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>메뉴에서 메뉴 항목 사이를 이동합니다.</li>\n' +
  '  <li>도구 모음 팝업 메뉴에서 메뉴 항목 사이를 이동합니다.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>화살표</strong> 키는 포커스 표시 UI 섹션 내에서 순환됩니다.</p>\n' +
  '\n' +
  '<p>열려 있는 메뉴, 열려 있는 하위 메뉴 또는 열려 있는 팝업 메뉴를 닫으려면 <strong>Esc</strong> 키를 누릅니다.</p>\n' +
  '\n' +
  "<p>현재 포커스 표시가 특정 UI 섹션 '상단'에 있는 경우 이때도 <strong>Esc</strong> 키를 누르면\n" +
  '  키보드 탐색이 완전히 종료됩니다.</p>\n' +
  '\n' +
  '<h1>메뉴 항목 또는 도구 모음 버튼 실행</h1>\n' +
  '\n' +
  '<p>원하는 메뉴 항목 또는 도구 모음 버튼이 강조 표시되어 있을 때 <strong>Return(리턴)</strong>, <strong>Enter(엔터)</strong>,\n' +
  '  또는 <strong>Space bar(스페이스바)</strong>를 눌러 해당 항목을 실행합니다.</p>\n' +
  '\n' +
  '<h1>탭이 없는 대화 탐색</h1>\n' +
  '\n' +
  '<p>탭이 없는 대화의 경우, 첫 번째 대화형 요소가 포커스 표시된 상태로 대화가 열립니다.</p>\n' +
  '\n' +
  '<p>대화형 요소들 사이를 이동할 때는 <strong>Tab(탭)</strong> 또는 <strong>Shift+Tab(시프트+탭)</strong>을 누릅니다.</p>\n' +
  '\n' +
  '<h1>탭이 있는 대화 탐색</h1>\n' +
  '\n' +
  '<p>탭이 있는 대화의 경우, 탭 메뉴에서 첫 번째 버튼이 포커스 표시된 상태로 대화가 열립니다.</p>\n' +
  '\n' +
  '<p>이 대화 탭의 대화형 요소들 사이를 이동할 때는 <strong>Tab(탭)</strong> 또는\n' +
  '  <strong>Shift+Tab(시프트+탭)</strong>을 누릅니다.</p>\n' +
  '\n' +
  '<p>다른 대화 탭으로 이동하려면 탭 메뉴를 포커스 표시한 다음 적절한 <strong>화살표</strong>\n' +
  '  키를 눌러 사용 가능한 탭들을 지나 원하는 탭으로 이동합니다.</p>\n');