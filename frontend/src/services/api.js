import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('adminToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('adminToken')
      localStorage.removeItem('adminUser')
      window.location.href = '/login'
    }
    
    // Return the error response data if available, otherwise the error
    return Promise.reject(error.response?.data || error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  getProfile: () => api.get('/auth/profile'),
  verifyToken: () => api.get('/auth/verify'),
}

// Domain API
export const domainAPI = {
  getAll: (params) => api.get('/domains', { params }),
  getById: (id) => api.get(`/domains/${id}`),
  create: (data) => api.post('/domains', data),
  update: (id, data) => api.put(`/domains/${id}`, data),
  delete: (id) => api.delete(`/domains/${id}`),
}

// Subject API
export const subjectAPI = {
  getAll: (params) => api.get('/subjects', { params }),
  getById: (id) => api.get(`/subjects/${id}`),
  create: (data) => api.post('/subjects', data),
  update: (id, data) => api.put(`/subjects/${id}`, data),
  delete: (id) => api.delete(`/subjects/${id}`),
}

// Chapter API
export const chapterAPI = {
  getAll: (params) => api.get('/chapters', { params }),
  getById: (id) => api.get(`/chapters/${id}`),
  create: (data) => api.post('/chapters', data),
  update: (id, data) => api.put(`/chapters/${id}`, data),
  delete: (id) => api.delete(`/chapters/${id}`),
}

// MCQ API
export const mcqAPI = {
  getAll: (params) => api.get('/mcqs', { params }),
  getById: (id) => api.get(`/mcqs/${id}`),
  create: (data) => api.post('/mcqs', data),
  update: (id, data) => api.put(`/mcqs/${id}`, data),
  delete: (id) => api.delete(`/mcqs/${id}`),
}

// Viva Q&A API
export const vivaAPI = {
  getAll: (params) => api.get('/viva', { params }),
  getById: (id) => api.get(`/viva/${id}`),
  create: (data) => api.post('/viva', data),
  update: (id, data) => api.put(`/viva/${id}`, data),
  delete: (id) => api.delete(`/viva/${id}`),
}

// Subscription Plan API
export const subscriptionPlanAPI = {
  getAll: (params) => api.get('/subscription-plans', { params }),
  getById: (id) => api.get(`/subscription-plans/${id}`),
  create: (data) => api.post('/subscription-plans', data),
  update: (id, data) => api.put(`/subscription-plans/${id}`, data),
  delete: (id) => api.delete(`/subscription-plans/${id}`),
}

// User API
export const userAPI = {
  getAll: (params) => api.get('/users', { params }),
  getById: (id) => api.get(`/users/${id}`),
  create: (data) => api.post('/users', data),
  update: (id, data) => api.put(`/users/${id}`, data),
  delete: (id) => api.delete(`/users/${id}`),
}

// User Subscription API
export const userSubscriptionAPI = {
  getAll: (params) => api.get('/user-subscriptions', { params }),
  getById: (id) => api.get(`/user-subscriptions/${id}`),
  create: (data) => api.post('/user-subscriptions', data),
  update: (id, data) => api.put(`/user-subscriptions/${id}`, data),
  delete: (id) => api.delete(`/user-subscriptions/${id}`),
}

export default api
