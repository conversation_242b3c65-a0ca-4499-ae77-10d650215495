import axios from 'axios'

const API_BASE_URL = 'http://localhost:3000/api'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => {
    localStorage.removeItem('authToken')
    return Promise.resolve()
  },
}

// AI Generation API
export const aiAPI = {
  // Generate content
  generateChapters: (data) => api.post('/ai/generate/chapters', data),
  generateMCQs: (data) => api.post('/ai/generate/mcqs', data),
  generateViva: (data) => api.post('/ai/generate/viva', data),
  generateBulk: (data) => api.post('/ai/generate/bulk', data),
  
  // Job management
  getJobStatus: (jobId) => api.get(`/ai/jobs/${jobId}`),
  cancelJob: (jobId) => api.delete(`/ai/jobs/${jobId}`),
  getJobHistory: (params) => api.get('/ai/jobs', { params }),
  
  // Queue management
  getQueueStats: () => api.get('/queue/stats'),
  getQueueHealth: () => api.get('/queue/health'),
  pauseQueue: () => api.post('/queue/pause'),
  resumeQueue: () => api.post('/queue/resume'),
  cleanQueue: () => api.post('/queue/clean'),
}

// Prompts API
export const promptsAPI = {
  getPrompts: () => api.get('/prompts'),
  getPrompt: (id) => api.get(`/prompts/${id}`),
  createPrompt: (data) => api.post('/prompts', data),
  updatePrompt: (id, data) => api.put(`/prompts/${id}`, data),
  deletePrompt: (id) => api.delete(`/prompts/${id}`),
}

// Content Management API
export const contentAPI = {
  // Domains
  getDomains: (params) => api.get('/domains', { params }),
  getDomain: (id) => api.get(`/domains/${id}`),
  createDomain: (data) => api.post('/domains', data),
  updateDomain: (id, data) => api.put(`/domains/${id}`, data),
  deleteDomain: (id) => api.delete(`/domains/${id}`),
  
  // Subjects
  getSubjects: (params) => api.get('/subjects', { params }),
  getSubject: (id) => api.get(`/subjects/${id}`),
  createSubject: (data) => api.post('/subjects', data),
  updateSubject: (id, data) => api.put(`/subjects/${id}`, data),
  deleteSubject: (id) => api.delete(`/subjects/${id}`),
  
  // Chapters
  getChapters: (params) => api.get('/chapters', { params }),
  getChapter: (id) => api.get(`/chapters/${id}`),
  createChapter: (data) => api.post('/chapters', data),
  updateChapter: (id, data) => api.put(`/chapters/${id}`, data),
  deleteChapter: (id) => api.delete(`/chapters/${id}`),
  
  // MCQs
  getMCQs: (params) => api.get('/mcqs', { params }),
  getMCQ: (id) => api.get(`/mcqs/${id}`),
  createMCQ: (data) => api.post('/mcqs', data),
  updateMCQ: (id, data) => api.put(`/mcqs/${id}`, data),
  deleteMCQ: (id) => api.delete(`/mcqs/${id}`),
  
  // Viva Q&A
  getVivaQAs: (params) => api.get('/viva-qa', { params }),
  getVivaQA: (id) => api.get(`/viva-qa/${id}`),
  createVivaQA: (data) => api.post('/viva-qa', data),
  updateVivaQA: (id, data) => api.put(`/viva-qa/${id}`, data),
  deleteVivaQA: (id) => api.delete(`/viva-qa/${id}`),
}

export default api
