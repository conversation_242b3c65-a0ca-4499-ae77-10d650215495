import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './context/AuthContext'
import DashboardLayout from './components/Layout/DashboardLayout'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<Login />} />

          {/* Protected routes */}
          <Route path="/dashboard" element={
            <DashboardLayout>
              <Dashboard />
            </DashboardLayout>
          } />

          {/* Placeholder routes for future pages */}
          <Route path="/domains" element={
            <DashboardLayout>
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold text-gray-900">Domains Management</h2>
                <p className="mt-2 text-gray-600">Coming soon...</p>
              </div>
            </DashboardLayout>
          } />

          <Route path="/subjects" element={
            <DashboardLayout>
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold text-gray-900">Subjects Management</h2>
                <p className="mt-2 text-gray-600">Coming soon...</p>
              </div>
            </DashboardLayout>
          } />

          <Route path="/chapters" element={
            <DashboardLayout>
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold text-gray-900">Chapters Management</h2>
                <p className="mt-2 text-gray-600">Coming soon...</p>
              </div>
            </DashboardLayout>
          } />

          <Route path="/mcqs" element={
            <DashboardLayout>
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold text-gray-900">MCQs Management</h2>
                <p className="mt-2 text-gray-600">Coming soon...</p>
              </div>
            </DashboardLayout>
          } />

          <Route path="/viva" element={
            <DashboardLayout>
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold text-gray-900">Viva Q&A Management</h2>
                <p className="mt-2 text-gray-600">Coming soon...</p>
              </div>
            </DashboardLayout>
          } />

          <Route path="/subscription-plans" element={
            <DashboardLayout>
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold text-gray-900">Subscription Plans</h2>
                <p className="mt-2 text-gray-600">Coming soon...</p>
              </div>
            </DashboardLayout>
          } />

          <Route path="/user-subscriptions" element={
            <DashboardLayout>
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold text-gray-900">User Subscriptions</h2>
                <p className="mt-2 text-gray-600">Coming soon...</p>
              </div>
            </DashboardLayout>
          } />

          <Route path="/users" element={
            <DashboardLayout>
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold text-gray-900">Users Management</h2>
                <p className="mt-2 text-gray-600">Coming soon...</p>
              </div>
            </DashboardLayout>
          } />

          {/* Default redirect */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />

          {/* 404 fallback */}
          <Route path="*" element={
            <DashboardLayout>
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold text-gray-900">Page Not Found</h2>
                <p className="mt-2 text-gray-600">The page you're looking for doesn't exist.</p>
              </div>
            </DashboardLayout>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  )
}

export default App
