import { useState, useEffect } from 'react'
import AIGenerationDashboard from './components/AIGenerationDashboard'
import ContentManagement from './components/ContentManagement'
import Login from './components/Login'

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [adminData, setAdminData] = useState(null)
  const [activeView, setActiveView] = useState('ai-generation')

  // Check for existing auth on mount
  useEffect(() => {
    const token = localStorage.getItem('authToken')
    const storedAdminData = localStorage.getItem('adminData')

    if (token && storedAdminData) {
      setIsAuthenticated(true)
      setAdminData(JSON.parse(storedAdminData))
    }
  }, [])

  const handleLogin = (admin) => {
    setIsAuthenticated(true)
    setAdminData(admin)
  }

  const handleLogout = () => {
    localStorage.removeItem('authToken')
    localStorage.removeItem('adminData')
    setIsAuthenticated(false)
    setAdminData(null)
  }

  if (!isAuthenticated) {
    return <Login onLogin={handleLogin} />
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Navigation */}
      <nav className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-8">
              <h1 className="text-xl font-semibold text-gray-900">
                LearnLegend Admin Panel
              </h1>
              <div className="flex space-x-4">
                <button
                  onClick={() => setActiveView('ai-generation')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    activeView === 'ai-generation'
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  AI Generation
                </button>
                <button
                  onClick={() => setActiveView('content-management')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    activeView === 'content-management'
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Content Management
                </button>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {adminData?.name || 'Admin'}
              </span>
              <button
                onClick={handleLogout}
                className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main>
        {activeView === 'ai-generation' && <AIGenerationDashboard />}
        {activeView === 'content-management' && <ContentManagement />}
      </main>
    </div>
  )
}

export default App
