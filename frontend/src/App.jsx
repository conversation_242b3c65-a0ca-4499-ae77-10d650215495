import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import AIGenerationDashboard from './components/AIGenerationDashboard'
import ContentManagement from './components/ContentManagement'
import Login from './components/Login'
import Layout from './components/Layout'

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [adminData, setAdminData] = useState(null)
  const [activeView, setActiveView] = useState('ai-generation')

  // Check for existing auth on mount
  useEffect(() => {
    const token = localStorage.getItem('authToken')
    const storedAdminData = localStorage.getItem('adminData')

    if (token && storedAdminData) {
      setIsAuthenticated(true)
      setAdminData(JSON.parse(storedAdminData))
    }
  }, [])

  const handleLogin = (admin) => {
    setIsAuthenticated(true)
    setAdminData(admin)
  }

  const handleLogout = () => {
    localStorage.removeItem('authToken')
    localStorage.removeItem('adminData')
    setIsAuthenticated(false)
    setAdminData(null)
  }

  return (
    <Router>
      <div className="min-h-screen bg-gray-100">
        {!isAuthenticated ? (
          <Login onLogin={handleLogin} />
        ) : (
          <Layout adminData={adminData} onLogout={handleLogout}>
            <Routes>
              <Route path="/" element={<AIGenerationDashboard />} />
              <Route path="/content/*" element={<ContentManagement />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Layout>
        )}
      </div>
    </Router>
  )
}

export default App
