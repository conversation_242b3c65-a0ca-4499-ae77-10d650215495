import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import AIGenerationDashboard from './components/AIGenerationDashboard'
import ContentManagement from './components/ContentManagement'
import Login from './components/Login'
import Layout from './components/Layout'
import LandingPage from './components/public/LandingPage'
import SubjectPage from './components/public/SubjectPage'
import PublicLayout from './components/public/PublicLayout'

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [adminData, setAdminData] = useState(null)
  const [activeView, setActiveView] = useState('ai-generation')
  const [darkMode, setDarkMode] = useState(false)

  // Check for existing auth and theme on mount
  useEffect(() => {
    const token = localStorage.getItem('authToken')
    const storedAdminData = localStorage.getItem('adminData')
    const storedDarkMode = localStorage.getItem('darkMode')

    if (token && storedAdminData) {
      setIsAuthenticated(true)
      setAdminData(JSON.parse(storedAdminData))
    }

    if (storedDarkMode) {
      setDarkMode(JSON.parse(storedDarkMode))
    }
  }, [])

  const handleLogin = (admin) => {
    setIsAuthenticated(true)
    setAdminData(admin)
  }

  const handleLogout = () => {
    localStorage.removeItem('authToken')
    localStorage.removeItem('adminData')
    setIsAuthenticated(false)
    setAdminData(null)
  }

  const toggleDarkMode = () => {
    const newDarkMode = !darkMode
    setDarkMode(newDarkMode)
    localStorage.setItem('darkMode', JSON.stringify(newDarkMode))
  }

  return (
    <Router>
      <div className={`min-h-screen ${darkMode ? 'dark' : ''}`}>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={
            <PublicLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
              <LandingPage />
            </PublicLayout>
          } />

          <Route path="/:slug" element={
            <PublicLayout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
              <SubjectPage />
            </PublicLayout>
          } />

          {/* Admin Routes */}
          <Route path="/admin/*" element={
            !isAuthenticated ? (
              <Login onLogin={handleLogin} />
            ) : (
              <Layout adminData={adminData} onLogout={handleLogout}>
                <Routes>
                  <Route path="/" element={<AIGenerationDashboard />} />
                  <Route path="/content/*" element={<ContentManagement />} />
                  <Route path="*" element={<Navigate to="/admin/" replace />} />
                </Routes>
              </Layout>
            )
          } />

          {/* Redirect admin login to admin route */}
          <Route path="/admin" element={<Navigate to="/admin/" replace />} />

          {/* Catch all - redirect to home */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
