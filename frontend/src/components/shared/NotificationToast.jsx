import { Fragment, useState, useEffect } from 'react'
import { Transition } from '@headlessui/react'
import { CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon, InformationCircleIcon, XMarkIcon } from '@heroicons/react/24/outline'

const toastTypes = {
  success: {
    icon: CheckCircleIcon,
    bgColor: 'bg-green-50',
    iconColor: 'text-green-400',
    titleColor: 'text-green-800',
    messageColor: 'text-green-700'
  },
  error: {
    icon: XCircleIcon,
    bgColor: 'bg-red-50',
    iconColor: 'text-red-400',
    titleColor: 'text-red-800',
    messageColor: 'text-red-700'
  },
  warning: {
    icon: ExclamationTriangleIcon,
    bgColor: 'bg-yellow-50',
    iconColor: 'text-yellow-400',
    titleColor: 'text-yellow-800',
    messageColor: 'text-yellow-700'
  },
  info: {
    icon: InformationCircleIcon,
    bgColor: 'bg-blue-50',
    iconColor: 'text-blue-400',
    titleColor: 'text-blue-800',
    messageColor: 'text-blue-700'
  }
}

export default function NotificationToast({
  show,
  onClose,
  type = 'info',
  title,
  message,
  autoClose = true,
  autoCloseDelay = 5000
}) {
  const [isVisible, setIsVisible] = useState(show)
  const toastConfig = toastTypes[type]
  const IconComponent = toastConfig.icon

  useEffect(() => {
    setIsVisible(show)
  }, [show])

  useEffect(() => {
    if (show && autoClose) {
      const timer = setTimeout(() => {
        setIsVisible(false)
        setTimeout(onClose, 300) // Wait for transition to complete
      }, autoCloseDelay)

      return () => clearTimeout(timer)
    }
  }, [show, autoClose, autoCloseDelay, onClose])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(onClose, 300) // Wait for transition to complete
  }

  return (
    <div
      aria-live="assertive"
      className="pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-50"
    >
      <div className="flex w-full flex-col items-center space-y-4 sm:items-end">
        <Transition
          show={isVisible}
          as={Fragment}
          enter="transform ease-out duration-300 transition"
          enterFrom="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
          enterTo="translate-y-0 opacity-100 sm:translate-x-0"
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className={`pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg ${toastConfig.bgColor} shadow-lg ring-1 ring-black ring-opacity-5`}>
            <div className="p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <IconComponent className={`h-6 w-6 ${toastConfig.iconColor}`} aria-hidden="true" />
                </div>
                <div className="ml-3 w-0 flex-1 pt-0.5">
                  {title && (
                    <p className={`text-sm font-medium ${toastConfig.titleColor}`}>
                      {title}
                    </p>
                  )}
                  {message && (
                    <p className={`mt-1 text-sm ${toastConfig.messageColor}`}>
                      {message}
                    </p>
                  )}
                </div>
                <div className="ml-4 flex flex-shrink-0">
                  <button
                    type="button"
                    className={`inline-flex rounded-md ${toastConfig.bgColor} ${toastConfig.messageColor} hover:${toastConfig.titleColor} focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2`}
                    onClick={handleClose}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  )
}

// Toast manager hook
export function useToast() {
  const [toasts, setToasts] = useState([])

  const addToast = (toast) => {
    const id = Date.now()
    setToasts(prev => [...prev, { ...toast, id }])
  }

  const removeToast = (id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const showSuccess = (title, message) => addToast({ type: 'success', title, message })
  const showError = (title, message) => addToast({ type: 'error', title, message })
  const showWarning = (title, message) => addToast({ type: 'warning', title, message })
  const showInfo = (title, message) => addToast({ type: 'info', title, message })

  return {
    toasts,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    removeToast
  }
}

// Toast container component
export function ToastContainer() {
  const { toasts, removeToast } = useToast()

  return (
    <>
      {toasts.map(toast => (
        <NotificationToast
          key={toast.id}
          show={true}
          onClose={() => removeToast(toast.id)}
          type={toast.type}
          title={toast.title}
          message={toast.message}
        />
      ))}
    </>
  )
}
