import { useEffect, useRef } from 'react'

export default function SelfHostedEditor({ 
  value = '', 
  onChange, 
  onError,
  placeholder = 'Start typing...',
  height = 400,
  disabled = false 
}) {
  const editorRef = useRef(null)
  const containerRef = useRef(null)
  const editorInstanceRef = useRef(null)

  useEffect(() => {
    // Load TinyMCE script if not already loaded
    if (!window.tinymce) {
      const script = document.createElement('script')
      script.src = '/tinymce/tinymce.min.js'
      script.onload = initializeEditor
      script.onerror = () => {
        console.error('Failed to load TinyMCE')
        if (onError) onError('Failed to load TinyMCE')
      }
      document.head.appendChild(script)
    } else {
      initializeEditor()
    }

    return () => {
      // Cleanup editor on unmount
      if (editorInstanceRef.current) {
        try {
          editorInstanceRef.current.destroy()
        } catch (error) {
          console.warn('Error destroying TinyMCE editor:', error)
        }
      }
    }
  }, [])

  useEffect(() => {
    // Update editor content when value prop changes
    if (editorInstanceRef.current && value !== editorInstanceRef.current.getContent()) {
      editorInstanceRef.current.setContent(value || '')
    }
  }, [value])

  const initializeEditor = () => {
    if (!window.tinymce || !containerRef.current) return

    try {
      window.tinymce.init({
        target: containerRef.current,
        height: height,
        menubar: false,
        plugins: [
          'advlist', 'autolink', 'lists', 'link', 'charmap', 'anchor',
          'searchreplace', 'visualblocks', 'code', 'fullscreen',
          'insertdatetime', 'table', 'help', 'wordcount', 'emoticons'
        ],
        toolbar: 'undo redo | blocks | ' +
          'bold italic forecolor | alignleft aligncenter ' +
          'alignright alignjustify | bullist numlist outdent indent | ' +
          'removeformat | help',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px }',
        placeholder: placeholder,
        branding: false,
        promotion: false,
        license_key: 'gpl',
        setup: (editor) => {
          editorInstanceRef.current = editor
          
          editor.on('init', () => {
            editor.setContent(value || '')
            if (disabled) {
              editor.mode.set('readonly')
            }
          })

          editor.on('change keyup', () => {
            const content = editor.getContent()
            if (onChange) {
              onChange(content)
            }
          })

          editor.on('blur', () => {
            const content = editor.getContent()
            if (onChange) {
              onChange(content)
            }
          })
        },
        init_instance_callback: (editor) => {
          editorInstanceRef.current = editor
        }
      })
    } catch (error) {
      console.error('Error initializing TinyMCE:', error)
      if (onError) onError('Error initializing editor')
    }
  }

  return (
    <div className="w-full">
      <div 
        ref={containerRef}
        className="w-full border border-gray-300 rounded-md"
        style={{ minHeight: height }}
      />
    </div>
  )
}
