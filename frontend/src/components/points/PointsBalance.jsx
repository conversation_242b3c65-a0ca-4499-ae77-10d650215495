import React from 'react'
import { usePoints } from '../../contexts/PointsContext'
import { 
  CurrencyDollarIcon, 
  TrophyIcon, 
  FireIcon,
  ArrowTrendingUpIcon 
} from '@heroicons/react/24/outline'

const PointsBalance = ({ variant = 'default', showDetails = true }) => {
  const { 
    pointBalance, 
    totalPointsEarned, 
    loginStreak, 
    loading 
  } = usePoints()

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-6 bg-gray-200 rounded w-20"></div>
      </div>
    )
  }

  // Compact variant for header/navbar
  if (variant === 'compact') {
    return (
      <div className="flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 px-3 py-2 rounded-lg border border-yellow-200">
        <CurrencyDollarIcon className="h-5 w-5 text-yellow-600" />
        <span className="font-semibold text-yellow-800">
          {pointBalance.toLocaleString()} pts
        </span>
      </div>
    )
  }

  // Card variant for dashboard
  if (variant === 'card') {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Points Balance</h3>
          <CurrencyDollarIcon className="h-6 w-6 text-yellow-600" />
        </div>
        
        <div className="space-y-4">
          {/* Current Balance */}
          <div>
            <div className="text-3xl font-bold text-gray-900">
              {pointBalance.toLocaleString()}
            </div>
            <div className="text-sm text-gray-500">Available Points</div>
          </div>

          {showDetails && (
            <>
              {/* Stats Grid */}
              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <TrophyIcon className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-lg font-semibold text-gray-900">
                      {totalPointsEarned.toLocaleString()}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">Total Earned</div>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <FireIcon className="h-4 w-4 text-orange-600 mr-1" />
                    <span className="text-lg font-semibold text-gray-900">
                      {loginStreak}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">Day Streak</div>
                </div>
              </div>

              {/* Conversion Info */}
              <div className="bg-blue-50 rounded-lg p-3 mt-4">
                <div className="flex items-center text-sm text-blue-800">
                  <ArrowTrendingUpIcon className="h-4 w-4 mr-2" />
                  <span>1 Point = 1 BDT Discount</span>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    )
  }

  // Default inline variant
  return (
    <div className="flex items-center space-x-4">
      <div className="flex items-center space-x-2">
        <CurrencyDollarIcon className="h-5 w-5 text-yellow-600" />
        <div>
          <div className="font-semibold text-gray-900">
            {pointBalance.toLocaleString()} Points
          </div>
          {showDetails && (
            <div className="text-sm text-gray-500">
              Total earned: {totalPointsEarned.toLocaleString()}
            </div>
          )}
        </div>
      </div>

      {showDetails && loginStreak > 0 && (
        <div className="flex items-center space-x-1 bg-orange-100 px-2 py-1 rounded-full">
          <FireIcon className="h-4 w-4 text-orange-600" />
          <span className="text-sm font-medium text-orange-800">
            {loginStreak} day streak
          </span>
        </div>
      )}
    </div>
  )
}

export default PointsBalance
