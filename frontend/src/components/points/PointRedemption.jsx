import React, { useState, useEffect } from 'react'
import { usePoints } from '../../contexts/PointsContext'
import { 
  CurrencyDollarIcon,
  CalculatorIcon,
  TagIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

const PointRedemption = ({ 
  originalPrice, 
  purchaseType, 
  itemId, 
  onPointsChange,
  disabled = false 
}) => {
  const { pointBalance, calculateDiscount } = usePoints()
  
  const [pointsToUse, setPointsToUse] = useState(0)
  const [discount, setDiscount] = useState(0)
  const [finalPrice, setFinalPrice] = useState(originalPrice)
  const [isCalculating, setIsCalculating] = useState(false)
  const [error, setError] = useState('')

  const maxPointsUsable = Math.min(pointBalance, originalPrice)

  useEffect(() => {
    if (pointsToUse > 0 && pointsToUse <= maxPointsUsable) {
      calculateDiscountAmount()
    } else {
      setDiscount(0)
      setFinalPrice(originalPrice)
      setError('')
      if (onPointsChange) {
        onPointsChange({
          pointsToUse: 0,
          discount: 0,
          finalPrice: originalPrice
        })
      }
    }
  }, [pointsToUse, originalPrice, maxPointsUsable])

  const calculateDiscountAmount = async () => {
    try {
      setIsCalculating(true)
      setError('')
      
      const response = await calculateDiscount(pointsToUse, purchaseType, itemId)
      
      if (response.success) {
        const calculatedDiscount = response.data.discountAmount
        const calculatedFinalPrice = response.data.finalPrice
        
        setDiscount(calculatedDiscount)
        setFinalPrice(calculatedFinalPrice)
        
        if (onPointsChange) {
          onPointsChange({
            pointsToUse,
            discount: calculatedDiscount,
            finalPrice: calculatedFinalPrice
          })
        }
      }
    } catch (error) {
      console.error('Error calculating discount:', error)
      setError(error.response?.data?.message || 'Failed to calculate discount')
    } finally {
      setIsCalculating(false)
    }
  }

  const handlePointsChange = (value) => {
    const numValue = parseInt(value) || 0
    
    if (numValue < 0) {
      setPointsToUse(0)
      return
    }
    
    if (numValue > maxPointsUsable) {
      setPointsToUse(maxPointsUsable)
      return
    }
    
    setPointsToUse(numValue)
  }

  const handlePresetClick = (percentage) => {
    const presetAmount = Math.floor(maxPointsUsable * (percentage / 100))
    setPointsToUse(presetAmount)
  }

  const savings = originalPrice - finalPrice

  return (
    <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200 p-4">
      <div className="flex items-center space-x-2 mb-4">
        <TagIcon className="h-5 w-5 text-yellow-600" />
        <h3 className="font-semibold text-yellow-800">Use Points for Discount</h3>
      </div>

      {/* Points Balance Info */}
      <div className="mb-4 p-3 bg-white rounded-lg border border-yellow-200">
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-600">Available Points:</span>
          <span className="font-semibold text-gray-900">
            {pointBalance.toLocaleString()}
          </span>
        </div>
        <div className="flex justify-between items-center text-sm mt-1">
          <span className="text-gray-600">Max Usable:</span>
          <span className="font-semibold text-yellow-700">
            {maxPointsUsable.toLocaleString()} (1 point = 1 BDT)
          </span>
        </div>
      </div>

      {/* Points Input */}
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Points to Use
          </label>
          <div className="flex space-x-2">
            <div className="flex-1 relative">
              <input
                type="number"
                min="0"
                max={maxPointsUsable}
                value={pointsToUse}
                onChange={(e) => handlePointsChange(e.target.value)}
                disabled={disabled || maxPointsUsable === 0}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-yellow-500 disabled:bg-gray-100"
                placeholder="0"
              />
              <CurrencyDollarIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
            
            {isCalculating && (
              <div className="flex items-center px-3">
                <CalculatorIcon className="h-5 w-5 text-yellow-600 animate-pulse" />
              </div>
            )}
          </div>
        </div>

        {/* Preset Buttons */}
        {maxPointsUsable > 0 && (
          <div className="flex space-x-2">
            {[25, 50, 75, 100].map((percentage) => {
              const amount = Math.floor(maxPointsUsable * (percentage / 100))
              if (amount === 0) return null
              
              return (
                <button
                  key={percentage}
                  onClick={() => handlePresetClick(percentage)}
                  disabled={disabled}
                  className="flex-1 text-xs bg-yellow-100 text-yellow-700 py-1 px-2 rounded hover:bg-yellow-200 transition-colors disabled:opacity-50"
                >
                  {percentage}%
                </button>
              )
            })}
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="flex items-center space-x-2 text-red-600 text-sm">
            <ExclamationTriangleIcon className="h-4 w-4" />
            <span>{error}</span>
          </div>
        )}

        {/* Price Breakdown */}
        {pointsToUse > 0 && !error && (
          <div className="bg-white rounded-lg p-3 border border-yellow-200">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Original Price:</span>
                <span className="text-gray-900">{originalPrice} BDT</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Points Used:</span>
                <span className="text-yellow-700">-{pointsToUse} points</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Discount:</span>
                <span className="text-green-600">-{discount} BDT</span>
              </div>
              
              <div className="border-t border-gray-200 pt-2">
                <div className="flex justify-between font-semibold">
                  <span className="text-gray-900">Final Price:</span>
                  <span className="text-gray-900">{finalPrice} BDT</span>
                </div>
              </div>
              
              {savings > 0 && (
                <div className="bg-green-50 rounded p-2 mt-2">
                  <div className="text-center text-green-700 font-medium text-sm">
                    🎉 You save {savings} BDT!
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* No Points Available */}
        {maxPointsUsable === 0 && (
          <div className="text-center py-4">
            <CurrencyDollarIcon className="h-8 w-8 text-gray-300 mx-auto mb-2" />
            <p className="text-gray-500 text-sm">
              {pointBalance === 0 
                ? "You don't have any points to use yet."
                : "This item's price is higher than your available points."
              }
            </p>
            <p className="text-gray-400 text-xs mt-1">
              Earn more points by logging in daily and studying!
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default PointRedemption
