import React, { useEffect } from 'react'
import { usePoints } from '../../contexts/PointsContext'
import PointsBalance from './PointsBalance'
import PointsHistory from './PointsHistory'
import ReferralSystem from './ReferralSystem'
import DailyRewardPopup from './DailyRewardPopup'
import { 
  TrophyIcon,
  ClockIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

const PointsDashboard = () => {
  const { recordDailyLogin, loading } = usePoints()

  // Record daily login when component mounts
  useEffect(() => {
    const checkDailyLogin = async () => {
      try {
        await recordDailyLogin()
      } catch (error) {
        // Silently handle error - user might have already logged in today
        console.log('Daily login already recorded or error:', error.message)
      }
    }

    checkDailyLogin()
  }, [])

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse space-y-8">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">
              <div className="h-64 bg-gray-200 rounded-lg"></div>
              <div className="h-96 bg-gray-200 rounded-lg"></div>
            </div>
            <div className="space-y-8">
              <div className="h-48 bg-gray-200 rounded-lg"></div>
              <div className="h-64 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Points & Rewards
        </h1>
        <p className="text-gray-600">
          Earn points by studying and use them to get discounts on subscriptions and subjects.
        </p>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Points Balance Card */}
          <PointsBalance variant="card" showDetails={true} />

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrophyIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <div className="text-sm font-medium text-gray-500">
                    Today's Goal
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    5/5
                  </div>
                  <div className="text-sm text-green-600">
                    ✓ Daily login completed
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <div className="text-sm font-medium text-gray-500">
                    Study Time Today
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    0 min
                  </div>
                  <div className="text-sm text-gray-500">
                    Start studying to earn points!
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <div className="text-sm font-medium text-gray-500">
                    This Week
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    +25
                  </div>
                  <div className="text-sm text-green-600">
                    Points earned
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Points History */}
          <PointsHistory />
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-8">
          {/* Referral System */}
          <ReferralSystem />

          {/* How to Earn Points */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              How to Earn Points
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-semibold text-green-600">+5</span>
                </div>
                <div>
                  <div className="font-medium text-gray-900">Daily Login</div>
                  <div className="text-sm text-gray-600">
                    Log in every day to earn points + streak bonuses
                  </div>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-semibold text-blue-600">+1</span>
                </div>
                <div>
                  <div className="font-medium text-gray-900">Study Time</div>
                  <div className="text-sm text-gray-600">
                    Earn 1 point for every 10 minutes of active study
                  </div>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-semibold text-purple-600">+10</span>
                </div>
                <div>
                  <div className="font-medium text-gray-900">Referrals</div>
                  <div className="text-sm text-gray-600">
                    Invite friends and both get 10 points
                  </div>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-semibold text-yellow-600">+?</span>
                </div>
                <div>
                  <div className="font-medium text-gray-900">Achievements</div>
                  <div className="text-sm text-gray-600">
                    Complete chapters, pass exams, and reach milestones
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* How to Use Points */}
          <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg border border-yellow-200 p-6">
            <h3 className="text-lg font-semibold text-yellow-800 mb-4">
              How to Use Points
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                <span className="text-sm text-yellow-700">
                  1 Point = 1 BDT discount
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                <span className="text-sm text-yellow-700">
                  Use on subscription purchases
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                <span className="text-sm text-yellow-700">
                  Use on individual subject purchases
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                <span className="text-sm text-yellow-700">
                  Combine with other discounts
                </span>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-white rounded-lg border border-yellow-200">
              <div className="text-sm text-yellow-800 font-medium">
                💡 Pro Tip
              </div>
              <div className="text-sm text-yellow-700 mt-1">
                Save up points for bigger purchases to maximize your savings!
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Daily Reward Popup */}
      <DailyRewardPopup />
    </div>
  )
}

export default PointsDashboard
