import React, { useEffect, useState } from 'react'
import { usePoints } from '../../contexts/PointsContext'
import { 
  XMarkIcon, 
  GiftIcon, 
  FireIcon, 
  CurrencyDollarIcon,
  SparklesIcon 
} from '@heroicons/react/24/outline'

const DailyRewardPopup = () => {
  const { 
    showDailyReward, 
    dailyRewardData, 
    hideDailyReward 
  } = usePoints()
  
  const [isVisible, setIsVisible] = useState(false)
  const [animate, setAnimate] = useState(false)

  useEffect(() => {
    if (showDailyReward) {
      setIsVisible(true)
      // Trigger animation after component mounts
      setTimeout(() => setAnimate(true), 100)
    } else {
      setAnimate(false)
      // Hide component after animation completes
      setTimeout(() => setIsVisible(false), 300)
    }
  }, [showDailyReward])

  const handleClose = () => {
    setAnimate(false)
    setTimeout(() => {
      hideDailyReward()
    }, 300)
  }

  if (!isVisible || !dailyRewardData) return null

  const {
    pointsEarned,
    newStreak,
    streakBonus,
    totalPoints,
    isNewStreak
  } = dailyRewardData

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className={`fixed inset-0 bg-black transition-opacity duration-300 ${
          animate ? 'bg-opacity-50' : 'bg-opacity-0'
        }`}
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div 
          className={`relative bg-white rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 ${
            animate ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
          }`}
        >
          {/* Close Button */}
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>

          {/* Content */}
          <div className="p-8 text-center">
            {/* Icon */}
            <div className="mx-auto mb-6 relative">
              <div className="w-20 h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                <GiftIcon className="h-10 w-10 text-white" />
              </div>
              
              {/* Sparkles Animation */}
              <div className="absolute -top-2 -right-2">
                <SparklesIcon className="h-6 w-6 text-yellow-400 animate-pulse" />
              </div>
              <div className="absolute -bottom-1 -left-2">
                <SparklesIcon className="h-4 w-4 text-orange-400 animate-pulse" style={{ animationDelay: '0.5s' }} />
              </div>
            </div>

            {/* Title */}
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Daily Login Reward!
            </h2>
            
            <p className="text-gray-600 mb-6">
              Welcome back! Here's your daily reward.
            </p>

            {/* Reward Details */}
            <div className="space-y-4 mb-6">
              {/* Points Earned */}
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 border border-yellow-200">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <CurrencyDollarIcon className="h-6 w-6 text-yellow-600" />
                  <span className="text-2xl font-bold text-yellow-800">
                    +{pointsEarned}
                  </span>
                  <span className="text-lg text-yellow-700">Points</span>
                </div>
                
                {streakBonus > 0 && (
                  <div className="text-sm text-yellow-700">
                    Base: {pointsEarned - streakBonus} + Streak Bonus: {streakBonus}
                  </div>
                )}
              </div>

              {/* Streak Info */}
              {newStreak > 1 && (
                <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-200">
                  <div className="flex items-center justify-center space-x-2">
                    <FireIcon className="h-5 w-5 text-orange-600" />
                    <span className="font-semibold text-orange-800">
                      {newStreak} Day Streak!
                    </span>
                  </div>
                  
                  {isNewStreak && (
                    <div className="text-sm text-orange-700 mt-1">
                      🎉 New streak record!
                    </div>
                  )}
                  
                  <div className="text-xs text-orange-600 mt-2">
                    Keep logging in daily to earn streak bonuses!
                  </div>
                </div>
              )}

              {/* Total Balance */}
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">New Balance</div>
                <div className="text-xl font-bold text-gray-900">
                  {totalPoints.toLocaleString()} Points
                </div>
              </div>
            </div>

            {/* Streak Progress */}
            {newStreak > 0 && (
              <div className="mb-6">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Streak Progress</span>
                  <span>Next bonus in {7 - (newStreak % 7)} days</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-orange-400 to-red-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${((newStreak % 7) / 7) * 100}%` }}
                  />
                </div>
              </div>
            )}

            {/* Action Button */}
            <button
              onClick={handleClose}
              className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-semibold py-3 px-6 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 transform hover:scale-105"
            >
              Awesome! Continue Learning
            </button>

            {/* Tip */}
            <div className="mt-4 text-xs text-gray-500">
              💡 Tip: Use your points to get discounts on subscriptions and subjects!
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DailyRewardPopup
