import React, { useEffect, useState } from 'react'
import { usePoints } from '../../contexts/PointsContext'
import { 
  ClockIcon,
  PlusIcon,
  MinusIcon,
  FunnelIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'

const PointsHistory = () => {
  const { 
    transactions, 
    transactionsPagination, 
    loading, 
    loadTransactions 
  } = usePoints()
  
  const [filters, setFilters] = useState({
    type: 'all', // 'all', 'earned', 'spent'
    category: 'all', // 'all', 'daily_login', 'study_time', 'achievement', etc.
    dateRange: 'all' // 'all', 'today', 'week', 'month'
  })

  useEffect(() => {
    loadTransactions(1, filters)
  }, [filters])

  const handlePageChange = (newPage) => {
    loadTransactions(newPage, filters)
  }

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const getTransactionIcon = (type, category) => {
    if (type === 'earned') {
      return <PlusIcon className="h-4 w-4 text-green-600" />
    } else {
      return <MinusIcon className="h-4 w-4 text-red-600" />
    }
  }

  const getTransactionColor = (type) => {
    return type === 'earned' ? 'text-green-600' : 'text-red-600'
  }

  const getCategoryLabel = (category) => {
    const labels = {
      daily_login: 'Daily Login',
      study_time: 'Study Time',
      achievement: 'Achievement',
      referral: 'Referral',
      subscription_discount: 'Subscription Discount',
      subject_discount: 'Subject Discount',
      engagement: 'Engagement'
    }
    return labels[category] || category
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now - date)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return 'Today'
    if (diffDays === 2) return 'Yesterday'
    if (diffDays <= 7) return `${diffDays - 1} days ago`
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }

  if (loading && transactions.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                  <div className="h-3 bg-gray-200 rounded w-24"></div>
                </div>
              </div>
              <div className="h-4 bg-gray-200 rounded w-16"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Points History</h3>
          <ClockIcon className="h-5 w-5 text-gray-400" />
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-4 w-4 text-gray-400" />
            <select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              <option value="earned">Earned</option>
              <option value="spent">Spent</option>
            </select>
          </div>

          <select
            value={filters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Categories</option>
            <option value="daily_login">Daily Login</option>
            <option value="study_time">Study Time</option>
            <option value="achievement">Achievement</option>
            <option value="referral">Referral</option>
            <option value="subscription_discount">Subscription Discount</option>
            <option value="subject_discount">Subject Discount</option>
          </select>

          <select
            value={filters.dateRange}
            onChange={(e) => handleFilterChange('dateRange', e.target.value)}
            className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>
      </div>

      {/* Transactions List */}
      <div className="divide-y divide-gray-200">
        {transactions.length === 0 ? (
          <div className="p-8 text-center">
            <ClockIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions yet</h3>
            <p className="text-gray-500">
              Start earning points by logging in daily and studying!
            </p>
          </div>
        ) : (
          transactions.map((transaction) => (
            <div key={transaction.id} className="p-4 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${
                    transaction.type === 'earned' ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    {getTransactionIcon(transaction.type, transaction.category)}
                  </div>
                  
                  <div>
                    <div className="font-medium text-gray-900">
                      {transaction.description}
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <span>{getCategoryLabel(transaction.category)}</span>
                      <span>•</span>
                      <span>{formatDate(transaction.createdAt)}</span>
                    </div>
                  </div>
                </div>
                
                <div className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                  {transaction.type === 'earned' ? '+' : '-'}{Math.abs(transaction.amount)}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {transactionsPagination.pages > 1 && (
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Showing {((transactionsPagination.page - 1) * transactionsPagination.limit) + 1} to{' '}
              {Math.min(transactionsPagination.page * transactionsPagination.limit, transactionsPagination.total)} of{' '}
              {transactionsPagination.total} transactions
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(transactionsPagination.page - 1)}
                disabled={transactionsPagination.page === 1}
                className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon className="h-4 w-4" />
              </button>
              
              <span className="text-sm text-gray-700">
                Page {transactionsPagination.page} of {transactionsPagination.pages}
              </span>
              
              <button
                onClick={() => handlePageChange(transactionsPagination.page + 1)}
                disabled={transactionsPagination.page === transactionsPagination.pages}
                className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRightIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PointsHistory
