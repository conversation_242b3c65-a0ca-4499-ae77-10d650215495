import React, { useState } from 'react'
import { usePoints } from '../../contexts/PointsContext'
import { 
  ShareIcon,
  ClipboardDocumentIcon,
  UserGroupIcon,
  GiftIcon,
  CheckIcon,
  QrCodeIcon
} from '@heroicons/react/24/outline'

const ReferralSystem = () => {
  const { 
    referralCode, 
    referralStats, 
    generateReferralCode, 
    loading 
  } = usePoints()
  
  const [copied, setCopied] = useState(false)
  const [customCode, setCustomCode] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)

  const shareUrl = referralStats?.shareUrl || 
    `${window.location.origin}/signup?ref=${referralCode}`

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(referralCode)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const handleGenerateCode = async () => {
    try {
      setIsGenerating(true)
      await generateReferralCode(customCode || null)
      setCustomCode('')
    } catch (error) {
      console.error('Failed to generate code:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Join LearnLegend with my referral!',
          text: `Use my referral code "${referralCode}" to get 10 bonus points when you sign up!`,
          url: shareUrl
        })
      } catch (error) {
        console.error('Failed to share:', error)
      }
    } else {
      // Fallback to copy URL
      handleCopyUrl()
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Referral System</h3>
          <UserGroupIcon className="h-5 w-5 text-gray-400" />
        </div>
        <p className="text-sm text-gray-600 mt-1">
          Invite friends and earn points together!
        </p>
      </div>

      <div className="p-6 space-y-6">
        {/* Referral Code Section */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Your Referral Code</h4>
          
          {referralCode ? (
            <div className="space-y-3">
              {/* Code Display */}
              <div className="flex items-center space-x-2">
                <div className="flex-1 bg-gray-50 border border-gray-200 rounded-lg p-3 font-mono text-lg text-center">
                  {referralCode}
                </div>
                <button
                  onClick={handleCopyCode}
                  className="p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  title="Copy code"
                >
                  {copied ? (
                    <CheckIcon className="h-5 w-5" />
                  ) : (
                    <ClipboardDocumentIcon className="h-5 w-5" />
                  )}
                </button>
              </div>

              {/* Share URL */}
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={shareUrl}
                  readOnly
                  className="flex-1 bg-gray-50 border border-gray-200 rounded-lg p-3 text-sm"
                />
                <button
                  onClick={handleCopyUrl}
                  className="p-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  title="Copy URL"
                >
                  <ClipboardDocumentIcon className="h-5 w-5" />
                </button>
              </div>

              {/* Share Button */}
              <button
                onClick={handleShare}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-3 px-4 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center space-x-2"
              >
                <ShareIcon className="h-5 w-5" />
                <span>Share with Friends</span>
              </button>
            </div>
          ) : (
            <div className="space-y-3">
              <p className="text-gray-600 text-sm">
                Generate your unique referral code to start earning points!
              </p>
              
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Custom code (optional)"
                  value={customCode}
                  onChange={(e) => setCustomCode(e.target.value)}
                  className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  maxLength={20}
                />
                <button
                  onClick={handleGenerateCode}
                  disabled={isGenerating}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  {isGenerating ? 'Generating...' : 'Generate'}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Referral Stats */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Your Referral Stats</h4>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-green-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {referralStats?.totalReferrals || 0}
              </div>
              <div className="text-sm text-green-700">Total Referrals</div>
            </div>
            
            <div className="bg-yellow-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {referralStats?.totalEarnings || 0}
              </div>
              <div className="text-sm text-yellow-700">Points Earned</div>
            </div>
          </div>
        </div>

        {/* How It Works */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">How It Works</h4>
          
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-semibold text-blue-600">1</span>
              </div>
              <div>
                <div className="font-medium text-gray-900">Share your code</div>
                <div className="text-sm text-gray-600">
                  Send your referral code or link to friends
                </div>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-semibold text-green-600">2</span>
              </div>
              <div>
                <div className="font-medium text-gray-900">They sign up</div>
                <div className="text-sm text-gray-600">
                  Both of you get 10 points when they register
                </div>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-semibold text-yellow-600">3</span>
              </div>
              <div>
                <div className="font-medium text-gray-900">They purchase</div>
                <div className="text-sm text-gray-600">
                  You earn 50% of their first purchase as points
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Rewards Info */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200">
          <div className="flex items-center space-x-2 mb-2">
            <GiftIcon className="h-5 w-5 text-purple-600" />
            <span className="font-medium text-purple-800">Referral Rewards</span>
          </div>
          <div className="text-sm text-purple-700 space-y-1">
            <div>• 10 points for each successful referral</div>
            <div>• 50% of their first purchase as bonus points</div>
            <div>• Your friend also gets 10 welcome points</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReferralSystem
