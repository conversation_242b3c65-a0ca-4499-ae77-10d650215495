import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { BookOpenIcon, QuestionMarkCircleIcon, MicrophoneIcon, LockClosedIcon, CheckCircleIcon } from '@heroicons/react/24/outline'
import axios from 'axios'

const API_BASE = import.meta.env.VITE_API_BASE || 'http://localhost:3000/api'

export default function SubjectPage() {
  const { slug } = useParams()
  const [subject, setSubject] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    fetchSubjectData()
  }, [slug])

  const fetchSubjectData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await axios.get(`${API_BASE}/public/subjects/${slug}`)
      setSubject(response.data.data)
      
    } catch (error) {
      console.error('Error fetching subject data:', error)
      setError(error.response?.data?.message || 'Failed to load subject')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Subject Not Found</h1>
          <p className="text-gray-600 mb-8">{error}</p>
          <Link to="/" className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors">
            Back to Home
          </Link>
        </div>
      </div>
    )
  }

  if (!subject) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <nav className="text-blue-200 text-sm mb-4">
                <Link to="/" className="hover:text-white">Home</Link>
                <span className="mx-2">/</span>
                <Link to="/subjects" className="hover:text-white">Subjects</Link>
                <span className="mx-2">/</span>
                <span className="text-white">{subject.name}</span>
              </nav>
              
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {subject.name}
              </h1>
              
              <p className="text-xl text-blue-100 mb-6">
                {subject.description}
              </p>
              
              <div className="flex items-center space-x-6 mb-8">
                <div className="flex items-center">
                  <BookOpenIcon className="w-5 h-5 mr-2" />
                  <span>{subject.chaptersCount} Chapters</span>
                </div>
                <div className="flex items-center">
                  <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm">
                    {subject.domain.name}
                  </span>
                </div>
              </div>
              
              <div className="flex space-x-4">
                <button className="bg-white text-blue-600 px-6 py-3 rounded-md font-semibold hover:bg-gray-50 transition-colors">
                  Start Learning
                </button>
                <button className="border border-white text-white px-6 py-3 rounded-md font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                  View Pricing
                </button>
              </div>
            </div>
            
            <div className="lg:text-right">
              <div className="inline-block bg-white bg-opacity-10 rounded-lg p-8">
                <h3 className="text-2xl font-bold mb-4">Course Preview</h3>
                <p className="text-blue-100 mb-4">
                  Get access to the first 2 chapters for free!
                </p>
                <div className="text-left space-y-2">
                  <div className="flex items-center">
                    <CheckCircleIcon className="w-5 h-5 mr-2 text-green-300" />
                    <span>Interactive content</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircleIcon className="w-5 h-5 mr-2 text-green-300" />
                    <span>MCQ practice</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircleIcon className="w-5 h-5 mr-2 text-green-300" />
                    <span>Viva preparation</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Course Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Course Curriculum</h2>
              
              <div className="space-y-4">
                {subject.chapters.map((chapter, index) => {
                  const isPreview = index < 2 // First 2 chapters are preview
                  
                  return (
                    <div key={chapter.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                      <div className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center mb-2">
                              <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-0.5 rounded mr-3">
                                Chapter {chapter.order || index + 1}
                              </span>
                              {isPreview ? (
                                <span className="bg-green-100 text-green-800 text-sm font-medium px-2.5 py-0.5 rounded">
                                  Free Preview
                                </span>
                              ) : (
                                <span className="bg-gray-100 text-gray-800 text-sm font-medium px-2.5 py-0.5 rounded flex items-center">
                                  <LockClosedIcon className="w-3 h-3 mr-1" />
                                  Premium
                                </span>
                              )}
                            </div>
                            
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                              {chapter.title}
                            </h3>
                            
                            <p className="text-gray-600 mb-4">
                              {chapter.description}
                            </p>
                            
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <div className="flex items-center">
                                <QuestionMarkCircleIcon className="w-4 h-4 mr-1" />
                                <span>{chapter.mcqsCount} MCQs</span>
                              </div>
                              <div className="flex items-center">
                                <MicrophoneIcon className="w-4 h-4 mr-1" />
                                <span>{chapter.vivaQAsCount} Viva Q&As</span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="ml-4">
                            {isPreview ? (
                              <button className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors">
                                Start Chapter
                              </button>
                            ) : (
                              <button className="bg-gray-300 text-gray-500 px-4 py-2 rounded-md text-sm cursor-not-allowed">
                                Locked
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Get Full Access</h3>
                
                <div className="mb-6">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    ৳999
                  </div>
                  <p className="text-gray-600">One-time payment for lifetime access</p>
                </div>
                
                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold hover:bg-blue-700 transition-colors mb-4">
                  Purchase Now
                </button>
                
                <div className="space-y-3 text-sm">
                  <div className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 mr-2 text-green-500" />
                    <span>All {subject.chaptersCount} chapters</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 mr-2 text-green-500" />
                    <span>MCQ practice questions</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 mr-2 text-green-500" />
                    <span>Viva Q&A preparation</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 mr-2 text-green-500" />
                    <span>Lifetime access</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircleIcon className="w-4 h-4 mr-2 text-green-500" />
                    <span>Mobile friendly</span>
                  </div>
                </div>
                
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <p className="text-sm text-gray-600 text-center">
                    30-day money-back guarantee
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
