import { useState, useEffect } from 'react'
import { aiAPI, contentAPI } from '../services/api'
import { 
  SparklesIcon, 
  PlayIcon, 
  StopIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'

export default function AIGenerationDashboard() {
  const [domains, setDomains] = useState([])
  const [subjects, setSubjects] = useState([])
  const [selectedDomain, setSelectedDomain] = useState('')
  const [selectedSubject, setSelectedSubject] = useState('')
  const [generationType, setGenerationType] = useState('chapters')
  const [count, setCount] = useState(3)
  const [isGenerating, setIsGenerating] = useState(false)
  const [currentJob, setCurrentJob] = useState(null)
  const [queueStats, setQueueStats] = useState(null)
  const [jobHistory, setJobHistory] = useState([])

  // Load initial data
  useEffect(() => {
    loadDomains()
    loadQueueStats()
    loadJobHistory()
  }, [])

  // Load subjects when domain changes
  useEffect(() => {
    if (selectedDomain) {
      loadSubjects(selectedDomain)
    }
  }, [selectedDomain])

  // Poll job status and queue stats
  useEffect(() => {
    const interval = setInterval(() => {
      if (currentJob) {
        checkJobStatus(currentJob.id)
      }
      loadQueueStats()
    }, 2000)

    return () => clearInterval(interval)
  }, [currentJob])

  const loadDomains = async () => {
    try {
      const response = await contentAPI.getDomains()
      setDomains(response.data.data || [])
    } catch (error) {
      console.error('Failed to load domains:', error)
    }
  }

  const loadSubjects = async (domainId) => {
    try {
      const response = await contentAPI.getSubjects({ domainId })
      setSubjects(response.data.data || [])
    } catch (error) {
      console.error('Failed to load subjects:', error)
    }
  }

  const loadQueueStats = async () => {
    try {
      const response = await aiAPI.getQueueStats()
      setQueueStats(response.data)
    } catch (error) {
      console.error('Failed to load queue stats:', error)
    }
  }

  const loadJobHistory = async () => {
    try {
      const response = await aiAPI.getJobHistory({ limit: 10 })
      setJobHistory(response.data.data || [])
    } catch (error) {
      console.error('Failed to load job history:', error)
    }
  }

  const checkJobStatus = async (jobId) => {
    try {
      const response = await aiAPI.getJobStatus(jobId)
      const job = response.data
      
      if (job.status === 'completed' || job.status === 'failed') {
        setCurrentJob(null)
        setIsGenerating(false)
        loadJobHistory()
      } else {
        setCurrentJob(job)
      }
    } catch (error) {
      console.error('Failed to check job status:', error)
    }
  }

  const handleGenerate = async () => {
    if (!selectedDomain || !selectedSubject) {
      alert('Please select both domain and subject')
      return
    }

    setIsGenerating(true)
    
    try {
      let response
      const data = {
        domainId: parseInt(selectedDomain),
        subjectId: parseInt(selectedSubject),
        count: parseInt(count)
      }

      switch (generationType) {
        case 'chapters':
          response = await aiAPI.generateChapters(data)
          break
        case 'mcqs':
          response = await aiAPI.generateMCQs(data)
          break
        case 'viva':
          response = await aiAPI.generateViva(data)
          break
        default:
          throw new Error('Invalid generation type')
      }

      setCurrentJob(response.data.job)
    } catch (error) {
      console.error('Failed to start generation:', error)
      setIsGenerating(false)
      alert('Failed to start AI generation: ' + error.message)
    }
  }

  const handleCancelJob = async () => {
    if (!currentJob) return
    
    try {
      await aiAPI.cancelJob(currentJob.id)
      setCurrentJob(null)
      setIsGenerating(false)
      loadJobHistory()
    } catch (error) {
      console.error('Failed to cancel job:', error)
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
      case 'active':
        return <ArrowPathIcon className="h-5 w-5 text-blue-500 animate-spin" />
      default:
        return <ClockIcon className="h-5 w-5 text-yellow-500" />
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center">
          <SparklesIcon className="h-8 w-8 text-primary-500 mr-3" />
          AI Content Generation
        </h1>
        <p className="mt-2 text-gray-600">
          Generate educational content using AI for your learning platform
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Generation Form */}
        <div className="lg:col-span-2">
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Generate Content</h2>
            
            <div className="space-y-6">
              {/* Domain Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Domain
                </label>
                <select
                  value={selectedDomain}
                  onChange={(e) => setSelectedDomain(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  disabled={isGenerating}
                >
                  <option value="">Select a domain</option>
                  {domains.map((domain) => (
                    <option key={domain.id} value={domain.id}>
                      {domain.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Subject Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Subject
                </label>
                <select
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  disabled={isGenerating || !selectedDomain}
                >
                  <option value="">Select a subject</option>
                  {subjects.map((subject) => (
                    <option key={subject.id} value={subject.id}>
                      {subject.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Generation Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content Type
                </label>
                <select
                  value={generationType}
                  onChange={(e) => setGenerationType(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  disabled={isGenerating}
                >
                  <option value="chapters">Chapters</option>
                  <option value="mcqs">Multiple Choice Questions</option>
                  <option value="viva">Viva Q&A</option>
                </select>
              </div>

              {/* Count */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Number of Items
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={count}
                  onChange={(e) => setCount(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  disabled={isGenerating}
                />
              </div>

              {/* Generate Button */}
              <div className="flex space-x-4">
                <button
                  onClick={handleGenerate}
                  disabled={isGenerating || !selectedDomain || !selectedSubject}
                  className="flex-1 bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isGenerating ? (
                    <>
                      <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4 mr-2" />
                      Generate with AI
                    </>
                  )}
                </button>
                
                {currentJob && (
                  <button
                    onClick={handleCancelJob}
                    className="px-4 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-50 flex items-center"
                  >
                    <StopIcon className="h-4 w-4 mr-2" />
                    Cancel
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Status Panel */}
        <div className="space-y-6">
          {/* Current Job Status */}
          {currentJob && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Current Job</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  <div className="flex items-center">
                    {getStatusIcon(currentJob.status)}
                    <span className="ml-2 text-sm font-medium capitalize">
                      {currentJob.status}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Type</span>
                  <span className="text-sm font-medium capitalize">
                    {currentJob.name?.replace('generate-', '')}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Progress</span>
                  <span className="text-sm font-medium">
                    {currentJob.progress || 0}%
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Queue Stats */}
          {queueStats && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Queue Status</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Active</span>
                  <span className="text-sm font-medium">{queueStats.active || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Waiting</span>
                  <span className="text-sm font-medium">{queueStats.waiting || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Completed</span>
                  <span className="text-sm font-medium">{queueStats.completed || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Failed</span>
                  <span className="text-sm font-medium">{queueStats.failed || 0}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Job History */}
      <div className="mt-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Jobs</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {jobHistory.map((job) => (
                  <tr key={job.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 capitalize">
                      {job.name?.replace('generate-', '')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStatusIcon(job.status)}
                        <span className="ml-2 text-sm text-gray-900 capitalize">
                          {job.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(job.createdAt).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {job.duration ? `${job.duration}ms` : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
