import { Routes, Route, Navigate } from 'react-router-dom'
import DomainList from './content/DomainList'
import SubjectList from './content/SubjectList'
import ChapterList from './content/ChapterList'
import MCQList from './content/MCQList'
import VivaList from './content/VivaList'
import ContentOverview from './content/ContentOverview'

export default function ContentManagement() {

  return (
    <Routes>
      <Route index element={<ContentOverview />} />
      <Route path="domains" element={<DomainList />} />
      <Route path="domains/:domainId/subjects" element={<SubjectList />} />
      <Route path="domains/:domainId/subjects/:subjectId/chapters" element={<ChapterList />} />
      <Route path="domains/:domainId/subjects/:subjectId/chapters/:chapterId/mcqs" element={<MCQList />} />
      <Route path="domains/:domainId/subjects/:subjectId/chapters/:chapterId/viva" element={<VivaList />} />
      <Route path="*" element={<Navigate to="/content" replace />} />
    </Routes>
  )
}
