import { useState, useEffect } from 'react'
import { contentAPI } from '../services/api'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  EyeIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'

export default function ContentManagement() {
  const [activeTab, setActiveTab] = useState('domains')
  const [domains, setDomains] = useState([])
  const [subjects, setSubjects] = useState([])
  const [chapters, setChapters] = useState([])
  const [mcqs, setMCQs] = useState([])
  const [vivaQAs, setVivaQAs] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadData()
  }, [activeTab])

  const loadData = async () => {
    setLoading(true)
    try {
      switch (activeTab) {
        case 'domains':
          const domainsRes = await contentAPI.getDomains()
          setDomains(domainsRes.data.data || [])
          break
        case 'subjects':
          const subjectsRes = await contentAPI.getSubjects()
          setSubjects(subjectsRes.data.data || [])
          break
        case 'chapters':
          const chaptersRes = await contentAPI.getChapters()
          setChapters(chaptersRes.data.data || [])
          break
        case 'mcqs':
          const mcqsRes = await contentAPI.getMCQs()
          setMCQs(mcqsRes.data.data || [])
          break
        case 'viva':
          const vivaRes = await contentAPI.getVivaQAs()
          setVivaQAs(vivaRes.data.data || [])
          break
      }
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  const tabs = [
    { id: 'domains', name: 'Domains', count: domains.length },
    { id: 'subjects', name: 'Subjects', count: subjects.length },
    { id: 'chapters', name: 'Chapters', count: chapters.length },
    { id: 'mcqs', name: 'MCQs', count: mcqs.length },
    { id: 'viva', name: 'Viva Q&A', count: vivaQAs.length },
  ]

  const renderDomains = () => (
    <div className="space-y-4">
      {domains.map((domain) => (
        <div key={domain.id} className="bg-white border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">{domain.name}</h3>
              <p className="text-sm text-gray-600">{domain.description}</p>
              <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>Created: {new Date(domain.createdAt).toLocaleDateString()}</span>
                <span>Status: {domain.isActive ? 'Active' : 'Inactive'}</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <EyeIcon className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <PencilIcon className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-red-600">
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  const renderSubjects = () => (
    <div className="space-y-4">
      {subjects.map((subject) => (
        <div key={subject.id} className="bg-white border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">{subject.name}</h3>
              <p className="text-sm text-gray-600">{subject.description}</p>
              <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>Domain: {subject.domain?.name}</span>
                <span>Created: {new Date(subject.createdAt).toLocaleDateString()}</span>
                <span>Status: {subject.isActive ? 'Active' : 'Inactive'}</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <EyeIcon className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <PencilIcon className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-red-600">
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  const renderChapters = () => (
    <div className="space-y-4">
      {chapters.map((chapter) => (
        <div key={chapter.id} className="bg-white border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="text-lg font-medium text-gray-900">{chapter.title}</h3>
                {chapter.isAIGenerated && (
                  <SparklesIcon className="h-4 w-4 text-purple-500" title="AI Generated" />
                )}
                {chapter.isDraft && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                    Draft
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-600 mt-1">{chapter.content?.substring(0, 100)}...</p>
              <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>Subject: {chapter.subject?.name}</span>
                <span>Order: {chapter.order}</span>
                <span>Created: {new Date(chapter.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <EyeIcon className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <PencilIcon className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-red-600">
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  const renderMCQs = () => (
    <div className="space-y-4">
      {mcqs.map((mcq) => (
        <div key={mcq.id} className="bg-white border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="text-lg font-medium text-gray-900">{mcq.question}</h3>
                {mcq.isAIGenerated && (
                  <SparklesIcon className="h-4 w-4 text-purple-500" title="AI Generated" />
                )}
                {mcq.isDraft && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                    Draft
                  </span>
                )}
              </div>
              <div className="mt-2 text-sm text-gray-600">
                <p>A) {mcq.optionA}</p>
                <p>B) {mcq.optionB}</p>
                <p>C) {mcq.optionC}</p>
                <p>D) {mcq.optionD}</p>
                <p className="mt-1 font-medium">Correct: {mcq.correctAnswer}</p>
              </div>
              <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>Chapter: {mcq.chapter?.title}</span>
                <span>Difficulty: {mcq.difficulty}</span>
                <span>Created: {new Date(mcq.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <EyeIcon className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <PencilIcon className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-red-600">
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  const renderVivaQAs = () => (
    <div className="space-y-4">
      {vivaQAs.map((viva) => (
        <div key={viva.id} className="bg-white border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="text-lg font-medium text-gray-900">{viva.question}</h3>
                {viva.isAIGenerated && (
                  <SparklesIcon className="h-4 w-4 text-purple-500" title="AI Generated" />
                )}
                {viva.isDraft && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                    Draft
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-600 mt-1">{viva.answer?.substring(0, 150)}...</p>
              <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                <span>Chapter: {viva.chapter?.title}</span>
                <span>Difficulty: {viva.difficulty}</span>
                <span>Created: {new Date(viva.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <EyeIcon className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <PencilIcon className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-red-600">
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      )
    }

    switch (activeTab) {
      case 'domains':
        return renderDomains()
      case 'subjects':
        return renderSubjects()
      case 'chapters':
        return renderChapters()
      case 'mcqs':
        return renderMCQs()
      case 'viva':
        return renderVivaQAs()
      default:
        return null
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Content Management</h1>
        <p className="mt-2 text-gray-600">
          Manage your learning content including domains, subjects, chapters, MCQs, and viva questions
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name}
              <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                {tab.count}
              </span>
            </button>
          ))}
        </nav>
      </div>

      {/* Add Button */}
      <div className="mb-6">
        <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
          <PlusIcon className="h-4 w-4 mr-2" />
          Add {tabs.find(t => t.id === activeTab)?.name.slice(0, -1)}
        </button>
      </div>

      {/* Content */}
      {renderContent()}
    </div>
  )
}
