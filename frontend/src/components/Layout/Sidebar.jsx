import React from 'react'
import { NavLink, useLocation } from 'react-router-dom'
import { 
  LayoutDashboard, 
  FolderTree, 
  BookOpen, 
  FileText, 
  HelpCircle, 
  MessageSquare, 
  CreditCard, 
  Users,
  LogOut,
  ChevronRight
} from 'lucide-react'
import { useAuth } from '../../context/AuthContext'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'Content Management',
    icon: FolderTree,
    children: [
      { name: 'Domains', href: '/domains', icon: FolderTree },
      { name: 'Subjects', href: '/subjects', icon: BookOpen },
      { name: 'Chapters', href: '/chapters', icon: FileText },
      { name: 'MCQs', href: '/mcqs', icon: HelpCircle },
      { name: 'Viva Q&A', href: '/viva', icon: MessageSquare },
    ],
  },
  {
    name: 'Subscription Management',
    icon: CreditCard,
    children: [
      { name: 'Plans', href: '/subscription-plans', icon: CreditCard },
      { name: 'User Subscriptions', href: '/user-subscriptions', icon: Users },
    ],
  },
  {
    name: 'Users',
    href: '/users',
    icon: Users,
  },
]

const Sidebar = ({ isOpen, setIsOpen }) => {
  const { logout, user } = useAuth()
  const location = useLocation()
  const [expandedItems, setExpandedItems] = React.useState(new Set())

  const toggleExpanded = (itemName) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemName)) {
      newExpanded.delete(itemName)
    } else {
      newExpanded.add(itemName)
    }
    setExpandedItems(newExpanded)
  }

  const isActiveLink = (href) => {
    return location.pathname === href
  }

  const isActiveParent = (children) => {
    return children?.some(child => location.pathname === child.href)
  }

  const handleLogout = () => {
    logout()
  }

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-gray-600 bg-opacity-75 z-20 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-30 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 bg-primary-600">
            <h1 className="text-xl font-bold text-white">LearnLegend Admin</h1>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigation.map((item) => (
              <div key={item.name}>
                {item.children ? (
                  <div>
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className={`
                        w-full flex items-center justify-between px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200
                        ${isActiveParent(item.children) 
                          ? 'bg-primary-100 text-primary-700' 
                          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                        }
                      `}
                    >
                      <div className="flex items-center">
                        <item.icon className="mr-3 h-5 w-5" />
                        {item.name}
                      </div>
                      <ChevronRight 
                        className={`h-4 w-4 transition-transform duration-200 ${
                          expandedItems.has(item.name) ? 'rotate-90' : ''
                        }`}
                      />
                    </button>
                    
                    {expandedItems.has(item.name) && (
                      <div className="mt-2 ml-4 space-y-1">
                        {item.children.map((child) => (
                          <NavLink
                            key={child.name}
                            to={child.href}
                            className={`
                              flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200
                              ${isActiveLink(child.href)
                                ? 'bg-primary-100 text-primary-700'
                                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                              }
                            `}
                          >
                            <child.icon className="mr-3 h-4 w-4" />
                            {child.name}
                          </NavLink>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <NavLink
                    to={item.href}
                    className={`
                      flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200
                      ${isActiveLink(item.href)
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                      }
                    `}
                  >
                    <item.icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </NavLink>
                )}
              </div>
            ))}
          </nav>

          {/* User info and logout */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                  <span className="text-sm font-medium text-primary-600">
                    {user?.name?.charAt(0)?.toUpperCase() || 'A'}
                  </span>
                </div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                <p className="text-xs text-gray-500">{user?.email}</p>
              </div>
            </div>
            
            <button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-2 text-sm font-medium text-gray-600 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200"
            >
              <LogOut className="mr-3 h-4 w-4" />
              Sign out
            </button>
          </div>
        </div>
      </div>
    </>
  )
}

export default Sidebar
