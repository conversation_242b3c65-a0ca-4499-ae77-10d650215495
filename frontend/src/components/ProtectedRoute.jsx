import React from 'react'
import { useAuth } from '../contexts/AuthContext'
import UserAuth from './user/UserAuth'

const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return <UserAuth />
  }

  return children
}

export default ProtectedRoute
