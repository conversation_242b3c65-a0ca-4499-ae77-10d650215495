import { useState, useEffect } from 'react'
import { Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import SelfHostedEditor from '../shared/SelfHostedEditor'
import { contentAPI } from '../../services/api'

export default function ChapterForm({ chapter, subjectId, onClose, onSave }) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    subjectId: subjectId || '',
    order: 0,
    isActive: true,
    isDraft: false,
    isAIGenerated: false
  })
  const [errors, setErrors] = useState({})
  const [loading, setLoading] = useState(false)
  const [editorError, setEditorError] = useState(false)

  useEffect(() => {
    if (chapter) {
      setFormData({
        title: chapter.title || '',
        description: chapter.description || '',
        content: chapter.content || '',
        subjectId: chapter.subjectId || subjectId || '',
        order: chapter.order || 0,
        isActive: chapter.isActive !== false,
        isDraft: chapter.isDraft || false,
        isAIGenerated: chapter.isAIGenerated || false
      })
    } else {
      setFormData(prev => ({
        ...prev,
        subjectId: subjectId || ''
      }))
    }
  }, [chapter, subjectId])

  const validateForm = () => {
    const newErrors = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Chapter title is required'
    } else if (formData.title.length < 2) {
      newErrors.title = 'Chapter title must be at least 2 characters'
    }

    if (!formData.subjectId) {
      newErrors.subjectId = 'Subject is required'
    }

    if (formData.order < 0) {
      newErrors.order = 'Order must be a positive number'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      setLoading(true)
      
      const submitData = {
        ...formData,
        order: parseInt(formData.order) || 0
      }

      if (chapter) {
        await contentAPI.updateChapter(chapter.id, submitData)
      } else {
        await contentAPI.createChapter(submitData)
      }

      onSave()
    } catch (error) {
      console.error('Failed to save chapter:', error)
      
      // Handle validation errors from backend
      if (error.response?.data?.errors) {
        const backendErrors = {}
        error.response.data.errors.forEach(err => {
          backendErrors[err.path] = err.message
        })
        setErrors(backendErrors)
      } else {
        setErrors({ 
          general: error.response?.data?.message || 'Failed to save chapter. Please try again.' 
        })
      }
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleEditorChange = (content) => {
    setFormData(prev => ({
      ...prev,
      content
    }))
  }

  return (
    <Transition.Root show={true} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl">
                <form onSubmit={handleSubmit}>
                  <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                    <div className="flex items-center justify-between mb-4">
                      <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900">
                        {chapter ? 'Edit Chapter' : 'Create Chapter'}
                      </Dialog.Title>
                      <button
                        type="button"
                        className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                        onClick={onClose}
                      >
                        <XMarkIcon className="h-6 w-6" />
                      </button>
                    </div>

                    {errors.general && (
                      <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
                        <p className="text-sm text-red-600">{errors.general}</p>
                      </div>
                    )}

                    <div className="space-y-4">
                      {/* Title Field */}
                      <div>
                        <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                          Chapter Title *
                        </label>
                        <input
                          type="text"
                          name="title"
                          id="title"
                          value={formData.title}
                          onChange={handleChange}
                          className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm ${
                            errors.title ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                          }`}
                          placeholder="Enter chapter title"
                        />
                        {errors.title && (
                          <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                        )}
                      </div>

                      {/* Description Field */}
                      <div>
                        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                          Description
                        </label>
                        <textarea
                          name="description"
                          id="description"
                          rows={3}
                          value={formData.description}
                          onChange={handleChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                          placeholder="Enter chapter description"
                        />
                        {errors.description && (
                          <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                        )}
                      </div>

                      {/* Content Field with TinyMCE */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Chapter Content
                        </label>
                        <div>
                          {!editorError ? (
                            <SelfHostedEditor
                              value={formData.content}
                              onChange={handleEditorChange}
                              onError={() => setEditorError(true)}
                              placeholder="Enter chapter content..."
                              height={400}
                            />
                          ) : (
                            <div>
                              <div className="mb-2 text-sm text-yellow-600 bg-yellow-50 p-2 rounded">
                                Rich text editor failed to load. Using basic text editor.
                              </div>
                              <textarea
                                value={formData.content}
                                onChange={(e) => handleEditorChange(e.target.value)}
                                rows={15}
                                className="w-full border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 p-3"
                                placeholder="Enter chapter content..."
                              />
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        {/* Order Field */}
                        <div>
                          <label htmlFor="order" className="block text-sm font-medium text-gray-700">
                            Display Order
                          </label>
                          <input
                            type="number"
                            name="order"
                            id="order"
                            min="0"
                            value={formData.order}
                            onChange={handleChange}
                            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm ${
                              errors.order ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                            }`}
                            placeholder="0"
                          />
                          {errors.order && (
                            <p className="mt-1 text-sm text-red-600">{errors.order}</p>
                          )}
                        </div>

                        {/* Status Checkboxes */}
                        <div className="space-y-3">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              name="isActive"
                              id="isActive"
                              checked={formData.isActive}
                              onChange={handleChange}
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            />
                            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                              Active (visible to users)
                            </label>
                          </div>

                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              name="isDraft"
                              id="isDraft"
                              checked={formData.isDraft}
                              onChange={handleChange}
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            />
                            <label htmlFor="isDraft" className="ml-2 block text-sm text-gray-700">
                              Draft (not published)
                            </label>
                          </div>

                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              name="isAIGenerated"
                              id="isAIGenerated"
                              checked={formData.isAIGenerated}
                              onChange={handleChange}
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            />
                            <label htmlFor="isAIGenerated" className="ml-2 block text-sm text-gray-700">
                              AI Generated content
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    <button
                      type="submit"
                      disabled={loading}
                      className="inline-flex w-full justify-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Saving...' : (chapter ? 'Update' : 'Create')}
                    </button>
                    <button
                      type="button"
                      className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                      onClick={onClose}
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
