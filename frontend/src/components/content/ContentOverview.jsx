import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { contentAPI } from '../../services/api'
import { 
  AcademicCapIcon,
  BookOpenIcon,
  DocumentTextIcon,
  QuestionMarkCircleIcon,
  ChatBubbleLeftRightIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import { LoadingPage } from '../shared/LoadingSpinner'

export default function ContentOverview() {
  const [stats, setStats] = useState({
    domains: 0,
    subjects: 0,
    chapters: 0,
    mcqs: 0,
    vivaQAs: 0
  })
  const [recentContent, setRecentContent] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadOverviewData()
  }, [])

  const loadOverviewData = async () => {
    try {
      setLoading(true)
      
      // Load stats
      const [domainsRes, subjectsRes, chaptersRes, mcqsRes, vivaRes] = await Promise.all([
        contentAPI.getDomains({ limit: 1 }),
        contentAPI.getSubjects({ limit: 1 }),
        contentAPI.getChapters({ limit: 1 }),
        contentAPI.getMCQs({ limit: 1 }),
        contentAPI.getVivaQAs({ limit: 1 })
      ])

      setStats({
        domains: domainsRes.data.meta?.total || 0,
        subjects: subjectsRes.data.meta?.total || 0,
        chapters: chaptersRes.data.meta?.total || 0,
        mcqs: mcqsRes.data.meta?.total || 0,
        vivaQAs: vivaRes.data.meta?.total || 0
      })

      // Load recent content (latest chapters)
      const recentRes = await contentAPI.getChapters({ limit: 5, sortBy: 'createdAt', sortOrder: 'desc' })
      setRecentContent(recentRes.data.data || [])
    } catch (error) {
      console.error('Failed to load overview data:', error)
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      name: 'Domains',
      value: stats.domains,
      icon: AcademicCapIcon,
      color: 'bg-blue-500',
      href: '/content/domains'
    },
    {
      name: 'Subjects',
      value: stats.subjects,
      icon: BookOpenIcon,
      color: 'bg-green-500',
      href: '/content/domains'
    },
    {
      name: 'Chapters',
      value: stats.chapters,
      icon: DocumentTextIcon,
      color: 'bg-purple-500',
      href: '/content/domains'
    },
    {
      name: 'MCQs',
      value: stats.mcqs,
      icon: QuestionMarkCircleIcon,
      color: 'bg-yellow-500',
      href: '/content/domains'
    },
    {
      name: 'Viva Q&A',
      value: stats.vivaQAs,
      icon: ChatBubbleLeftRightIcon,
      color: 'bg-red-500',
      href: '/content/domains'
    }
  ]

  if (loading) {
    return <LoadingPage message="Loading content overview..." />
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Content Management</h1>
        <p className="mt-2 text-gray-600">
          Manage your learning content including domains, subjects, chapters, MCQs, and viva questions
        </p>
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="flex flex-wrap gap-4">
          <Link
            to="/content/domains"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Domain
          </Link>
          <Link
            to="/content/domains"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <BookOpenIcon className="h-4 w-4 mr-2" />
            Browse Content
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="mb-8">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Content Statistics</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {statCards.map((stat) => (
            <Link
              key={stat.name}
              to={stat.href}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-200"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 ${stat.color} rounded-md flex items-center justify-center`}>
                      <stat.icon className="w-5 h-5 text-white" aria-hidden="true" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">{stat.name}</dt>
                      <dd className="text-lg font-medium text-gray-900">{stat.value}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Content */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900">Recent Chapters</h2>
          <Link
            to="/content/domains"
            className="text-sm text-primary-600 hover:text-primary-500"
          >
            View all
          </Link>
        </div>
        
        {recentContent.length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No chapters yet</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating a domain and subject first.</p>
            <div className="mt-6">
              <Link
                to="/content/domains"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Domain
              </Link>
            </div>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {recentContent.map((chapter) => (
                <li key={chapter.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {chapter.title}
                        </p>
                        <p className="mt-1 text-sm text-gray-500">
                          {chapter.subject?.name} • {chapter.subject?.domain?.name}
                        </p>
                        {chapter.description && (
                          <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                            {chapter.description.replace(/<[^>]*>/g, '').substring(0, 100)}...
                          </p>
                        )}
                      </div>
                      <div className="flex-shrink-0 text-sm text-gray-500">
                        {new Date(chapter.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  )
}
