import { useState, useEffect } from 'react'
import { Link, useParams } from 'react-router-dom'
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  QuestionMarkCircleIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline'
import Breadcrumb from '../shared/Breadcrumb'
import LoadingSpinner, { LoadingPage } from '../shared/LoadingSpinner'
import SearchFilter from '../shared/SearchFilter'
import Pagination from '../shared/Pagination'
import ConfirmDialog from '../shared/ConfirmDialog'
import { useToast } from '../shared/NotificationToast'
import ChapterForm from './ChapterForm'
import { contentAPI } from '../../services/api'

export default function ChapterList() {
  const { domainId, subjectId } = useParams()
  const [domain, setDomain] = useState(null)
  const [subject, setSubject] = useState(null)
  const [chapters, setChapters] = useState([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [editingChapter, setEditingChapter] = useState(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletingChapter, setDeletingChapter] = useState(null)
  const [selectedChapters, setSelectedChapters] = useState([])
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)

  // Pagination and search state
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    isActive: '',
    isDraft: ''
  })

  const { showToast } = useToast()

  const loadDomainAndSubject = async () => {
    try {
      const [domainResponse, subjectResponse] = await Promise.all([
        contentAPI.getDomain(domainId),
        contentAPI.getSubject(subjectId)
      ])
      setDomain(domainResponse.data.data)
      setSubject(subjectResponse.data.data)
    } catch (error) {
      console.error('Failed to load domain/subject:', error)
      showToast('Failed to load domain or subject', 'error')
    }
  }

  const loadChapters = async () => {
    try {
      setLoading(true)
      const params = {
        page: currentPage,
        limit: 10,
        subjectId,
        search: searchTerm || undefined,
        isActive: filters.isActive !== '' ? filters.isActive === 'true' : undefined,
        isDraft: filters.isDraft !== '' ? filters.isDraft === 'true' : undefined
      }

      const response = await contentAPI.getChapters(params)
      const { data, pagination } = response.data

      setChapters(data)
      setTotalPages(pagination.totalPages)
      setTotalItems(pagination.total)
    } catch (error) {
      console.error('Failed to load chapters:', error)
      showToast('Failed to load chapters', 'error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (domainId && subjectId) {
      loadDomainAndSubject()
    }
  }, [domainId, subjectId])

  useEffect(() => {
    if (subjectId) {
      loadChapters()
    }
  }, [subjectId, currentPage, searchTerm, filters])

  const handleSearch = (term) => {
    setSearchTerm(term)
    setCurrentPage(1)
  }

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters)
    setCurrentPage(1)
  }

  const handleCreateChapter = () => {
    setEditingChapter(null)
    setShowForm(true)
  }

  const handleEditChapter = (chapter) => {
    setEditingChapter(chapter)
    setShowForm(true)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingChapter(null)
  }

  const handleFormSave = () => {
    setShowForm(false)
    setEditingChapter(null)
    loadChapters()
    showToast(
      editingChapter ? 'Chapter updated successfully' : 'Chapter created successfully',
      'success'
    )
  }

  const handleDeleteChapter = (chapter) => {
    setDeletingChapter(chapter)
    setShowDeleteDialog(true)
  }

  const confirmDelete = async () => {
    if (!deletingChapter) return

    try {
      setActionLoading(true)
      await contentAPI.deleteChapter(deletingChapter.id)

      setShowDeleteDialog(false)
      setDeletingChapter(null)
      loadChapters()
      showToast('Chapter deleted successfully', 'success')
    } catch (error) {
      console.error('Failed to delete chapter:', error)
      showToast('Failed to delete chapter', 'error')
    } finally {
      setActionLoading(false)
    }
  }

  const handleSelectChapter = (chapterId) => {
    setSelectedChapters(prev =>
      prev.includes(chapterId)
        ? prev.filter(id => id !== chapterId)
        : [...prev, chapterId]
    )
  }

  const handleSelectAll = () => {
    if (selectedChapters.length === chapters.length) {
      setSelectedChapters([])
    } else {
      setSelectedChapters(chapters.map(chapter => chapter.id))
    }
  }

  const handleBulkDelete = () => {
    if (selectedChapters.length === 0) return
    setShowBulkDeleteDialog(true)
  }

  const confirmBulkDelete = async () => {
    try {
      setActionLoading(true)

      await Promise.all(
        selectedChapters.map(id => contentAPI.deleteChapter(id))
      )

      setShowBulkDeleteDialog(false)
      setSelectedChapters([])
      loadChapters()
      showToast(`${selectedChapters.length} chapters deleted successfully`, 'success')
    } catch (error) {
      console.error('Failed to delete chapters:', error)
      showToast('Failed to delete some chapters', 'error')
    } finally {
      setActionLoading(false)
    }
  }

  const breadcrumbItems = [
    { name: 'Domains', href: '/content/domains' },
    { name: domain?.name || 'Loading...', href: '/content/domains' },
    { name: 'Subjects', href: `/content/domains/${domainId}/subjects` },
    { name: subject?.name || 'Loading...', href: `/content/domains/${domainId}/subjects` },
    { name: 'Chapters' }
  ]

  const filterOptions = [
    {
      key: 'isActive',
      label: 'Status',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' }
      ]
    },
    {
      key: 'isDraft',
      label: 'Publication',
      options: [
        { value: '', label: 'All' },
        { value: 'false', label: 'Published' },
        { value: 'true', label: 'Draft' }
      ]
    }
  ]

  if (loading && !domain && !subject) {
    return <LoadingPage message="Loading chapters..." />
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <div className="mb-6">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3">
              <Link
                to={`/content/domains/${domainId}/subjects`}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {subject?.name} - Chapters
                </h1>
                <p className="mt-2 text-gray-600">
                  Manage chapters for this subject
                </p>
              </div>
            </div>
          </div>
          <button
            onClick={handleCreateChapter}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Chapter
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6">
        <SearchFilter
          searchTerm={searchTerm}
          onSearchChange={handleSearch}
          filters={filters}
          onFilterChange={handleFilterChange}
          filterOptions={filterOptions}
          placeholder="Search chapters..."
        />
      </div>

      {/* Bulk Actions */}
      {selectedChapters.length > 0 && (
        <div className="mb-4 bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              {selectedChapters.length} chapter{selectedChapters.length !== 1 ? 's' : ''} selected
            </span>
            <button
              onClick={handleBulkDelete}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete Selected
            </button>
          </div>
        </div>
      )}

      {/* Chapters List */}
      {loading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : chapters.length === 0 ? (
        <div className="text-center py-12">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No chapters found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || Object.values(filters).some(f => f !== '')
              ? 'Try adjusting your search or filters.'
              : 'Get started by creating your first chapter.'
            }
          </p>
          <div className="mt-6">
            <button
              onClick={handleCreateChapter}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Chapter
            </button>
          </div>
        </div>
      ) : (
        <>
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-3 bg-gray-50 border-b border-gray-200 sm:px-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedChapters.length === chapters.length}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-3 text-sm font-medium text-gray-700">
                  Select All ({chapters.length})
                </span>
              </div>
            </div>

            <ul className="divide-y divide-gray-200">
              {chapters.map((chapter) => (
                <li key={chapter.id} className="px-4 py-4 sm:px-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedChapters.includes(chapter.id)}
                        onChange={() => handleSelectChapter(chapter.id)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {chapter.title}
                          </h3>
                          <div className="flex items-center space-x-1">
                            {chapter.isDraft && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                Draft
                              </span>
                            )}
                            {!chapter.isActive && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                Inactive
                              </span>
                            )}
                            {chapter.isAIGenerated && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                AI Generated
                              </span>
                            )}
                          </div>
                        </div>
                        {chapter.description && (
                          <p className="mt-1 text-sm text-gray-500 truncate">
                            {chapter.description}
                          </p>
                        )}
                        <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                          <span>Order: {chapter.order}</span>
                          <span>MCQs: {chapter._count?.mcqs || 0}</span>
                          <span>Viva Q&A: {chapter._count?.vivaQuestions || 0}</span>
                          <span>Created: {new Date(chapter.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Link
                        to={`/content/domains/${domainId}/subjects/${subjectId}/chapters/${chapter.id}/mcqs`}
                        className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md"
                        title="View MCQs"
                      >
                        <QuestionMarkCircleIcon className="h-4 w-4" />
                      </Link>
                      <Link
                        to={`/content/domains/${domainId}/subjects/${subjectId}/chapters/${chapter.id}/viva`}
                        className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-md"
                        title="View Viva Q&A"
                      >
                        <ChatBubbleLeftRightIcon className="h-4 w-4" />
                      </Link>
                      <button
                        onClick={() => handleEditChapter(chapter)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                        title="Edit Chapter"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteChapter(chapter)}
                        className="p-2 text-gray-400 hover:text-red-600"
                        title="Delete Chapter"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>

          {/* Pagination */}
          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              onPageChange={setCurrentPage}
            />
          </div>
        </>
      )}

      {/* Chapter Form Modal */}
      {showForm && (
        <ChapterForm
          chapter={editingChapter}
          subjectId={subjectId}
          onClose={handleFormClose}
          onSave={handleFormSave}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDelete}
        title="Delete Chapter"
        message={`Are you sure you want to delete "${deletingChapter?.title}"? This action cannot be undone and will also delete all associated MCQs and Viva questions.`}
        confirmText="Delete"
        type="danger"
        loading={actionLoading}
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showBulkDeleteDialog}
        onClose={() => setShowBulkDeleteDialog(false)}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Chapters"
        message={`Are you sure you want to delete ${selectedChapters.length} chapter${selectedChapters.length !== 1 ? 's' : ''}? This action cannot be undone and will also delete all associated MCQs and Viva questions.`}
        confirmText="Delete All"
        type="danger"
        loading={actionLoading}
      />
    </div>
  )
}
