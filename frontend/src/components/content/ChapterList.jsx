import { useState, useEffect } from 'react'
import { Link, useParams } from 'react-router-dom'
import { ArrowLeftIcon, DocumentTextIcon, PlusIcon } from '@heroicons/react/24/outline'
import Breadcrumb from '../shared/Breadcrumb'
import { LoadingPage } from '../shared/LoadingSpinner'

export default function ChapterList() {
  const { domainId, subjectId } = useParams()
  const [domain, setDomain] = useState(null)
  const [subject, setSubject] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // TODO: Load domain and subject data
    setTimeout(() => {
      setDomain({ name: 'Sample Domain' })
      setSubject({ name: 'Sample Subject' })
      setLoading(false)
    }, 1000)
  }, [domainId, subjectId])

  const breadcrumbItems = [
    { name: 'Domains', href: '/content/domains' },
    { name: domain?.name || 'Loading...', href: '/content/domains' },
    { name: 'Subjects', href: `/content/domains/${domainId}/subjects` },
    { name: subject?.name || 'Loading...', href: `/content/domains/${domainId}/subjects` },
    { name: 'Chapters' }
  ]

  if (loading) {
    return <LoadingPage message="Loading chapters..." />
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <div className="mb-6">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3">
              <Link
                to={`/content/domains/${domainId}/subjects`}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {subject?.name} - Chapters
                </h1>
                <p className="mt-2 text-gray-600">
                  Manage chapters for this subject
                </p>
              </div>
            </div>
          </div>
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Chapter
          </button>
        </div>
      </div>

      {/* Placeholder Content */}
      <div className="text-center py-12">
        <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Chapter Management</h3>
        <p className="mt-1 text-sm text-gray-500">
          Chapter management with rich text editor will be implemented here.
        </p>
        <div className="mt-6">
          <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Chapter
          </button>
        </div>
      </div>
    </div>
  )
}
