import { useState, useEffect } from 'react'
import { XMarkIcon, SparklesIcon, ArrowPathIcon } from '@heroicons/react/24/outline'
import { aiAPI } from '../../services/api'
import { useToast } from '../shared/NotificationToast'

export default function AIVivaGenerationModal({ 
  isOpen, 
  onClose, 
  onComplete, 
  chapter,
  chapterId 
}) {
  const [count, setCount] = useState(5)
  const [customPrompt, setCustomPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [currentJob, setCurrentJob] = useState(null)
  const [jobProgress, setJobProgress] = useState(0)
  const [errors, setErrors] = useState({})

  const { showSuccess, showError, showWarning } = useToast()

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCount(5)
      setCustomPrompt('')
      setIsGenerating(false)
      setCurrentJob(null)
      setJobProgress(0)
      setErrors({})
    }
  }, [isOpen])

  // Poll job status
  useEffect(() => {
    let pollInterval
    if (currentJob && isGenerating) {
      pollInterval = setInterval(async () => {
        try {
          const response = await aiAPI.getJobStatus(currentJob.jobId)
          const job = response.data
          
          if (job.state === 'completed') {
            setIsGenerating(false)
            setCurrentJob(null)
            setJobProgress(100)
            showSuccess('AI Viva generation completed', 'Viva Q&As have been generated successfully')
            onComplete()
            clearInterval(pollInterval)
          } else if (job.state === 'failed') {
            setIsGenerating(false)
            setCurrentJob(null)
            setJobProgress(0)
            showError('AI Viva generation failed', job.error || 'Unknown error occurred')
            clearInterval(pollInterval)
          } else if (job.progress) {
            setJobProgress(job.progress)
          }
        } catch (error) {
          console.error('Failed to check job status:', error)
        }
      }, 2000)
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval)
      }
    }
  }, [currentJob, isGenerating, onComplete, showSuccess, showError])

  const validateForm = () => {
    const newErrors = {}

    if (!count || count < 1 || count > 20) {
      newErrors.count = 'Count must be between 1 and 20'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleGenerate = async () => {
    if (!validateForm()) {
      return
    }

    setIsGenerating(true)
    setJobProgress(0)
    
    try {
      const response = await aiAPI.generateViva({
        chapterId,
        count: parseInt(count),
        customPrompt: customPrompt.trim(),
        options: {
          model: 'gpt-3.5-turbo',
          temperature: 0.7,
          maxTokens: 3000
        }
      })
      
      setCurrentJob(response.data)
      showSuccess('AI Viva generation started', 'Your Viva Q&As are being generated in the background')
    } catch (error) {
      console.error('Failed to start Viva generation:', error)
      setIsGenerating(false)
      showError('Failed to start AI generation', error.response?.data?.message || error.message)
    }
  }

  const handleCancel = async () => {
    if (currentJob && isGenerating) {
      try {
        await aiAPI.cancelJob(currentJob.jobId)
        setIsGenerating(false)
        setCurrentJob(null)
        setJobProgress(0)
        showWarning('AI generation cancelled')
      } catch (error) {
        console.error('Failed to cancel job:', error)
        showError('Failed to cancel generation')
      }
    }
    onClose()
  }

  const handleClose = () => {
    if (isGenerating) {
      showWarning('Generation in progress', 'Please wait for generation to complete or cancel it first')
      return
    }
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <SparklesIcon className="h-6 w-6 text-purple-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">
              Generate AI Viva Q&As
            </h3>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {chapter && (
          <div className="mb-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <span className="font-medium">Chapter:</span> {chapter.title}
            </p>
          </div>
        )}

        <div className="space-y-4">
          {/* Count Input */}
          <div>
            <label htmlFor="count" className="block text-sm font-medium text-gray-700 mb-1">
              Number of Viva Q&As
            </label>
            <input
              type="number"
              id="count"
              min="1"
              max="20"
              value={count}
              onChange={(e) => setCount(e.target.value)}
              disabled={isGenerating}
              className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${
                errors.count ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {errors.count && (
              <p className="mt-1 text-sm text-red-600">{errors.count}</p>
            )}
          </div>

          {/* Custom Prompt */}
          <div>
            <label htmlFor="customPrompt" className="block text-sm font-medium text-gray-700 mb-1">
              Custom Prompt (Optional)
            </label>
            <textarea
              id="customPrompt"
              rows={3}
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              disabled={isGenerating}
              placeholder="Enter custom instructions for AI generation..."
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          {/* Progress Bar */}
          {isGenerating && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Generating Viva Q&As...</span>
                <span>{jobProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${jobProgress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            {isGenerating ? 'Cancel' : 'Close'}
          </button>
          <button
            onClick={handleGenerate}
            disabled={isGenerating}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isGenerating ? (
              <>
                <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <SparklesIcon className="h-4 w-4 mr-2" />
                Generate Viva Q&As
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
