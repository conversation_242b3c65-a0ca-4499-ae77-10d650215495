import { useState, useEffect } from 'react'
import { Link, useParams } from 'react-router-dom'
import {
  ArrowLeftIcon,
  QuestionMarkCircleIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import Breadcrumb from '../shared/Breadcrumb'
import { LoadingPage } from '../shared/LoadingSpinner'
import { useToast } from '../shared/NotificationToast'
import { contentAPI } from '../../services/api'
import SearchFilter from '../shared/SearchFilter'
import Pagination from '../shared/Pagination'
import ConfirmDialog from '../shared/ConfirmDialog'
import MCQForm from './MCQForm'

export default function MCQList() {
  const { domainId, subjectId, chapterId } = useParams()
  const [domain, setDomain] = useState(null)
  const [subject, setSubject] = useState(null)
  const [chapter, setChapter] = useState(null)
  const [mcqs, setMcqs] = useState([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({})
  const [showForm, setShowForm] = useState(false)
  const [editingMcq, setEditingMcq] = useState(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletingMcq, setDeletingMcq] = useState(null)

  const { showSuccess, showError } = useToast()
  const itemsPerPage = 10

  useEffect(() => {
    loadDomainSubjectChapter()
  }, [domainId, subjectId, chapterId])

  useEffect(() => {
    if (chapterId) {
      loadMcqs()
    }
  }, [chapterId, currentPage, searchTerm, filters])

  const loadDomainSubjectChapter = async () => {
    try {
      const [domainResponse, subjectResponse, chapterResponse] = await Promise.all([
        contentAPI.getDomain(domainId),
        contentAPI.getSubject(subjectId),
        contentAPI.getChapter(chapterId)
      ])

      setDomain(domainResponse.data.data || domainResponse.data)
      setSubject(subjectResponse.data.data || subjectResponse.data)
      setChapter(chapterResponse.data.data || chapterResponse.data)
    } catch (error) {
      console.error('Failed to load domain/subject/chapter:', error)
      showError('Failed to load page data')
    }
  }

  const loadMcqs = async () => {
    try {
      setLoading(true)
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: searchTerm || undefined,
        chapterId,
        ...filters
      }

      const response = await contentAPI.getMCQs(params)
      const { data, meta } = response.data

      setMcqs(data?.mcqs || [])
      setTotalPages(meta?.pagination?.totalPages || 1)
      setTotalItems(meta?.pagination?.total || 0)
    } catch (error) {
      console.error('Failed to load MCQs:', error)
      showError('Failed to load MCQs')
      setMcqs([])
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (term) => {
    setSearchTerm(term)
    setCurrentPage(1)
  }

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters)
    setCurrentPage(1)
  }

  const handleCreateMcq = () => {
    setEditingMcq(null)
    setShowForm(true)
  }

  const handleEditMcq = (mcq) => {
    setEditingMcq(mcq)
    setShowForm(true)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingMcq(null)
  }

  const handleFormSave = () => {
    setShowForm(false)
    setEditingMcq(null)
    loadMcqs()
    showSuccess(
      editingMcq ? 'MCQ updated successfully' : 'MCQ created successfully'
    )
  }

  const handleDeleteMcq = (mcq) => {
    setDeletingMcq(mcq)
    setShowDeleteDialog(true)
  }

  const confirmDelete = async () => {
    if (!deletingMcq) return

    try {
      await contentAPI.deleteMCQ(deletingMcq.id)
      setShowDeleteDialog(false)
      setDeletingMcq(null)
      loadMcqs()
      showSuccess('MCQ deleted successfully')
    } catch (error) {
      console.error('Failed to delete MCQ:', error)
      showError('Failed to delete MCQ')
    }
  }

  const breadcrumbItems = [
    { name: 'Domains', href: '/content/domains' },
    { name: domain?.name || 'Loading...', href: '/content/domains' },
    { name: 'Subjects', href: `/content/domains/${domainId}/subjects` },
    { name: subject?.name || 'Loading...', href: `/content/domains/${domainId}/subjects` },
    { name: 'Chapters', href: `/content/domains/${domainId}/subjects/${subjectId}/chapters` },
    { name: chapter?.title || 'Loading...', href: `/content/domains/${domainId}/subjects/${subjectId}/chapters` },
    { name: 'MCQs' }
  ]

  if (loading && !domain && !subject && !chapter) {
    return <LoadingPage message="Loading MCQs..." />
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <div className="mb-6">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3">
              <Link
                to={`/content/domains/${domainId}/subjects/${subjectId}/chapters`}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {chapter?.title} - MCQs
                </h1>
                <p className="mt-2 text-gray-600">
                  Manage multiple choice questions for this chapter
                </p>
              </div>
            </div>
          </div>
          <button
            onClick={handleCreateMcq}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add MCQ
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6">
        <SearchFilter
          searchTerm={searchTerm}
          onSearch={handleSearch}
          filters={filters}
          onFilterChange={handleFilterChange}
          placeholder="Search MCQs..."
        />
      </div>

      {/* MCQ List */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading MCQs...</p>
        </div>
      ) : mcqs.length === 0 ? (
        <div className="text-center py-12">
          <QuestionMarkCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No MCQs found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first MCQ for this chapter.
          </p>
          <div className="mt-6">
            <button
              onClick={handleCreateMcq}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add MCQ
            </button>
          </div>
        </div>
      ) : (
        <>
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {mcqs.map((mcq) => (
                <li key={mcq.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {mcq.question}
                        </p>
                        <div className="mt-2 grid grid-cols-2 gap-2 text-sm text-gray-600">
                          <div>A) {mcq.optionA}</div>
                          <div>B) {mcq.optionB}</div>
                          <div>C) {mcq.optionC}</div>
                          <div>D) {mcq.optionD}</div>
                        </div>
                        <p className="mt-2 text-sm text-green-600">
                          Correct Answer: {mcq.correctAnswer}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEditMcq(mcq)}
                          className="p-2 text-gray-400 hover:text-gray-600"
                          title="Edit MCQ"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteMcq(mcq)}
                          className="p-2 text-gray-400 hover:text-red-600"
                          title="Delete MCQ"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-6">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
              />
            </div>
          )}
        </>
      )}

      {/* MCQ Form */}
      <MCQForm
        isOpen={showForm}
        onClose={handleFormClose}
        onSave={handleFormSave}
        mcq={editingMcq}
        chapterId={chapterId}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDelete}
        title="Delete MCQ"
        message={`Are you sure you want to delete this MCQ? This action cannot be undone.`}
        confirmText="Delete"
        type="danger"
      />
    </div>
  )
}
