import { useState, useEffect } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { contentAPI } from '../../services/api'

export default function MCQForm({ 
  isOpen, 
  onClose, 
  onSave, 
  mcq = null,
  chapterId 
}) {
  const [formData, setFormData] = useState({
    question: '',
    optionA: '',
    optionB: '',
    optionC: '',
    optionD: '',
    correctAnswer: 'A',
    explanation: '',
    reference: '',
    difficulty: 'MEDIUM',
    order: 0
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState({})

  useEffect(() => {
    if (mcq) {
      setFormData({
        question: mcq.question || '',
        optionA: mcq.optionA || '',
        optionB: mcq.optionB || '',
        optionC: mcq.optionC || '',
        optionD: mcq.optionD || '',
        correctAnswer: mcq.correctAnswer || 'A',
        explanation: mcq.explanation || '',
        reference: mcq.reference || '',
        difficulty: mcq.difficulty || 'MEDIUM',
        order: mcq.order || 0
      })
    } else {
      setFormData({
        question: '',
        optionA: '',
        optionB: '',
        optionC: '',
        optionD: '',
        correctAnswer: 'A',
        explanation: '',
        reference: '',
        difficulty: 'MEDIUM',
        order: 0
      })
    }
    setErrors({})
  }, [mcq, isOpen])

  const validateForm = () => {
    const newErrors = {}

    if (!formData.question.trim()) {
      newErrors.question = 'Question is required'
    }

    if (!formData.optionA.trim()) {
      newErrors.optionA = 'Option A is required'
    }

    if (!formData.optionB.trim()) {
      newErrors.optionB = 'Option B is required'
    }

    if (!formData.optionC.trim()) {
      newErrors.optionC = 'Option C is required'
    }

    if (!formData.optionD.trim()) {
      newErrors.optionD = 'Option D is required'
    }

    if (!['A', 'B', 'C', 'D'].includes(formData.correctAnswer)) {
      newErrors.correctAnswer = 'Please select a valid correct answer'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      const submitData = {
        ...formData,
        chapterId
      }

      if (mcq) {
        await contentAPI.updateMCQ(mcq.id, submitData)
      } else {
        await contentAPI.createMCQ(submitData)
      }

      onSave()
    } catch (error) {
      console.error('Failed to save MCQ:', error)
      setErrors({ submit: 'Failed to save MCQ. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            {mcq ? 'Edit MCQ' : 'Create New MCQ'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Question */}
          <div>
            <label htmlFor="question" className="block text-sm font-medium text-gray-700">
              Question *
            </label>
            <textarea
              id="question"
              name="question"
              rows={3}
              value={formData.question}
              onChange={handleChange}
              className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                errors.question ? 'border-red-300' : 'border-gray-300'
              } focus:outline-none focus:ring-primary-500 focus:border-primary-500`}
              placeholder="Enter the MCQ question..."
            />
            {errors.question && (
              <p className="mt-1 text-sm text-red-600">{errors.question}</p>
            )}
          </div>

          {/* Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {['A', 'B', 'C', 'D'].map((option) => (
              <div key={option}>
                <label htmlFor={`option${option}`} className="block text-sm font-medium text-gray-700">
                  Option {option} *
                </label>
                <input
                  type="text"
                  id={`option${option}`}
                  name={`option${option}`}
                  value={formData[`option${option}`]}
                  onChange={handleChange}
                  className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                    errors[`option${option}`] ? 'border-red-300' : 'border-gray-300'
                  } focus:outline-none focus:ring-primary-500 focus:border-primary-500`}
                  placeholder={`Enter option ${option}...`}
                />
                {errors[`option${option}`] && (
                  <p className="mt-1 text-sm text-red-600">{errors[`option${option}`]}</p>
                )}
              </div>
            ))}
          </div>

          {/* Correct Answer */}
          <div>
            <label htmlFor="correctAnswer" className="block text-sm font-medium text-gray-700">
              Correct Answer *
            </label>
            <select
              id="correctAnswer"
              name="correctAnswer"
              value={formData.correctAnswer}
              onChange={handleChange}
              className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                errors.correctAnswer ? 'border-red-300' : 'border-gray-300'
              } focus:outline-none focus:ring-primary-500 focus:border-primary-500`}
            >
              <option value="A">A</option>
              <option value="B">B</option>
              <option value="C">C</option>
              <option value="D">D</option>
            </select>
            {errors.correctAnswer && (
              <p className="mt-1 text-sm text-red-600">{errors.correctAnswer}</p>
            )}
          </div>

          {/* Explanation */}
          <div>
            <label htmlFor="explanation" className="block text-sm font-medium text-gray-700">
              Explanation (Optional)
            </label>
            <textarea
              id="explanation"
              name="explanation"
              rows={2}
              value={formData.explanation}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Explain why this is the correct answer..."
            />
          </div>

          {/* Reference */}
          <div>
            <label htmlFor="reference" className="block text-sm font-medium text-gray-700">
              Reference (Optional)
            </label>
            <textarea
              id="reference"
              name="reference"
              rows={2}
              value={formData.reference}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Add reference sources, page numbers, or citations..."
            />
          </div>

          {/* Difficulty and Order */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="difficulty" className="block text-sm font-medium text-gray-700">
                Difficulty
              </label>
              <select
                id="difficulty"
                name="difficulty"
                value={formData.difficulty}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="EASY">Easy</option>
                <option value="MEDIUM">Medium</option>
                <option value="HARD">Hard</option>
              </select>
            </div>

            <div>
              <label htmlFor="order" className="block text-sm font-medium text-gray-700">
                Order
              </label>
              <input
                type="number"
                id="order"
                name="order"
                value={formData.order}
                onChange={handleChange}
                min="0"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="0"
              />
            </div>
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="text-sm text-red-600">{errors.submit}</div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {loading ? 'Saving...' : (mcq ? 'Update MCQ' : 'Create MCQ')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
