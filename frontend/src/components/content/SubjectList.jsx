import { useState, useEffect } from 'react'
import { Link, useParams } from 'react-router-dom'
import { contentAPI } from '../../services/api'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  BookOpenIcon,
  DocumentTextIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline'
import Breadcrumb from '../shared/Breadcrumb'
import SearchFilter from '../shared/SearchFilter'
import Pagination from '../shared/Pagination'
import ConfirmDialog from '../shared/ConfirmDialog'
import { LoadingPage } from '../shared/LoadingSpinner'
import { useToast } from '../shared/NotificationToast'
import SubjectForm from './SubjectForm'

export default function SubjectList() {
  const { domainId } = useParams()
  const [domain, setDomain] = useState(null)
  const [subjects, setSubjects] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState({})
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [selectedSubjects, setSelectedSubjects] = useState([])
  const [deleteDialog, setDeleteDialog] = useState({ show: false, subject: null })
  const [bulkDeleteDialog, setBulkDeleteDialog] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [editingSubject, setEditingSubject] = useState(null)

  const { showToast } = useToast()

  const itemsPerPage = 10

  useEffect(() => {
    loadDomain()
    loadSubjects()
  }, [domainId, currentPage, searchQuery, filters])

  const loadDomain = async () => {
    try {
      const response = await contentAPI.getDomain(domainId)
      setDomain(response.data.data)
    } catch (error) {
      console.error('Failed to load domain:', error)
      showToast('Failed to load domain', 'error')
    }
  }

  const loadSubjects = async () => {
    try {
      setLoading(true)
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: searchQuery || undefined,
        domainId,
        ...filters
      }

      const response = await contentAPI.getSubjects(params)
      const { data, meta } = response.data

      setSubjects(data || [])
      setTotalPages(meta?.totalPages || 1)
      setTotalItems(meta?.total || 0)
    } catch (error) {
      console.error('Failed to load subjects:', error)
      showToast('Failed to load subjects', 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateSubject = () => {
    setEditingSubject(null)
    setShowForm(true)
  }

  const handleEditSubject = (subject) => {
    setEditingSubject(subject)
    setShowForm(true)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingSubject(null)
  }

  const handleFormSave = () => {
    setShowForm(false)
    setEditingSubject(null)
    loadSubjects()
    showToast(
      editingSubject ? 'Subject updated successfully' : 'Subject created successfully',
      'success'
    )
  }

  const handleDelete = (subject) => {
    setDeleteDialog({ show: true, subject })
  }

  const confirmDelete = async () => {
    try {
      await contentAPI.deleteSubject(deleteDialog.subject.id)
      setDeleteDialog({ show: false, subject: null })
      loadSubjects()
      showToast('Subject deleted successfully', 'success')
    } catch (error) {
      console.error('Failed to delete subject:', error)
      showToast('Failed to delete subject', 'error')
    }
  }

  const handleBulkDelete = () => {
    setBulkDeleteDialog(true)
  }

  const confirmBulkDelete = async () => {
    try {
      await Promise.all(selectedSubjects.map(id => contentAPI.deleteSubject(id)))
      setBulkDeleteDialog(false)
      setSelectedSubjects([])
      loadSubjects()
      showToast(`${selectedSubjects.length} subjects deleted successfully`, 'success')
    } catch (error) {
      console.error('Failed to delete subjects:', error)
      showToast('Failed to delete some subjects', 'error')
    }
  }

  const handleSelectSubject = (subjectId) => {
    setSelectedSubjects(prev => 
      prev.includes(subjectId) 
        ? prev.filter(id => id !== subjectId)
        : [...prev, subjectId]
    )
  }

  const handleSelectAll = () => {
    setSelectedSubjects(
      selectedSubjects.length === subjects.length 
        ? [] 
        : subjects.map(subject => subject.id)
    )
  }

  const filterOptions = [
    {
      key: 'isActive',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' }
      ]
    }
  ]

  const breadcrumbItems = [
    { name: 'Domains', href: '/content/domains' },
    { name: domain?.name || 'Loading...', href: `/content/domains` },
    { name: 'Subjects' }
  ]

  if (loading && !domain) {
    return <LoadingPage message="Loading subjects..." />
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <div className="mb-6">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3">
              <Link
                to="/content/domains"
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {domain?.name} - Subjects
                </h1>
                <p className="mt-2 text-gray-600">
                  Manage subjects for this domain
                </p>
              </div>
            </div>
          </div>
          <button
            onClick={handleCreateSubject}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Subject
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6">
        <SearchFilter
          searchValue={searchQuery}
          onSearchChange={setSearchQuery}
          placeholder="Search subjects..."
          filters={filterOptions}
          activeFilters={filters}
          onFilterChange={setFilters}
        />
      </div>

      {/* Bulk Actions */}
      {selectedSubjects.length > 0 && (
        <div className="mb-4 bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              {selectedSubjects.length} subject{selectedSubjects.length > 1 ? 's' : ''} selected
            </span>
            <button
              onClick={handleBulkDelete}
              className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete Selected
            </button>
          </div>
        </div>
      )}

      {/* Subjects List */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      ) : subjects.length === 0 ? (
        <div className="text-center py-12">
          <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No subjects found</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating your first subject for this domain.</p>
          <div className="mt-6">
            <button
              onClick={handleCreateSubject}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Subject
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-3 border-b border-gray-200 sm:px-6">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedSubjects.length === subjects.length}
                onChange={handleSelectAll}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <span className="ml-3 text-sm font-medium text-gray-700">
                Select All
              </span>
            </div>
          </div>
          <ul className="divide-y divide-gray-200">
            {subjects.map((subject) => (
              <li key={subject.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedSubjects.includes(subject.id)}
                        onChange={() => handleSelectSubject(subject.id)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <div className="ml-4 flex-1 min-w-0">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900">{subject.name}</h3>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            subject.isActive 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {subject.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        {subject.description && (
                          <p className="mt-1 text-sm text-gray-600">{subject.description}</p>
                        )}
                        <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                          <span>Order: {subject.order}</span>
                          <span>Chapters: {subject._count?.chapters || 0}</span>
                          <span>Created: {new Date(subject.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Link
                        to={`/content/domains/${domainId}/subjects/${subject.id}/chapters`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        <DocumentTextIcon className="h-4 w-4 mr-1" />
                        View Chapters
                      </Link>
                      <button
                        onClick={() => handleEditSubject(subject)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(subject)}
                        className="p-2 text-gray-400 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={setCurrentPage}
          />
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteDialog.show}
        onClose={() => setDeleteDialog({ show: false, subject: null })}
        onConfirm={confirmDelete}
        title="Delete Subject"
        message={`Are you sure you want to delete "${deleteDialog.subject?.name}"? This action cannot be undone and will also delete all associated chapters, MCQs, and viva questions.`}
        confirmText="Delete"
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={bulkDeleteDialog}
        onClose={() => setBulkDeleteDialog(false)}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Subjects"
        message={`Are you sure you want to delete ${selectedSubjects.length} subject${selectedSubjects.length > 1 ? 's' : ''}? This action cannot be undone and will also delete all associated content.`}
        confirmText="Delete All"
        type="danger"
      />

      {/* Subject Form Modal */}
      {showForm && (
        <SubjectForm
          subject={editingSubject}
          domainId={domainId}
          onClose={handleFormClose}
          onSave={handleFormSave}
        />
      )}
    </div>
  )
}
