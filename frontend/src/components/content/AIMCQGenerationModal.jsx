import { useState, useEffect } from 'react'
import { XMarkIcon, SparklesIcon, ArrowPathIcon } from '@heroicons/react/24/outline'
import { aiAPI } from '../../services/api'
import { useToast } from '../shared/NotificationToast'

export default function AIMCQGenerationModal({ 
  isOpen, 
  onClose, 
  onComplete, 
  chapter,
  chapterId 
}) {
  const [count, setCount] = useState(10)
  const [customPrompt, setCustomPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [currentJob, setCurrentJob] = useState(null)
  const [jobProgress, setJobProgress] = useState(0)
  const [errors, setErrors] = useState({})

  const { showSuccess, showError, showWarning } = useToast()

  // Default prompt template
  const defaultPromptTemplate = `Generate {count} multiple choice questions with 4 options (A, B, C, D) and correct answers for the chapter titled '{title}'. Chapter content: {content}. Each MCQ should include a question, four distinct options, the correct answer (A/B/C/D), and a brief explanation.`

  useEffect(() => {
    if (isOpen && chapter) {
      // Initialize with default prompt template
      const prompt = defaultPromptTemplate
        .replace('{count}', count)
        .replace('{title}', chapter.title || '')
        .replace('{content}', chapter.content || chapter.description || '')
      setCustomPrompt(prompt)
    }
  }, [isOpen, chapter, count])

  useEffect(() => {
    let pollInterval
    if (currentJob && isGenerating) {
      pollInterval = setInterval(async () => {
        try {
          const response = await aiAPI.getJobStatus(currentJob.jobId)
          const job = response.data.job
          
          if (job.status === 'completed') {
            setIsGenerating(false)
            setCurrentJob(null)
            setJobProgress(100)
            onComplete()
            clearInterval(pollInterval)
          } else if (job.status === 'failed') {
            setIsGenerating(false)
            setCurrentJob(null)
            setJobProgress(0)
            showError('AI MCQ generation failed', job.error || 'Unknown error occurred')
            clearInterval(pollInterval)
          } else if (job.progress) {
            setJobProgress(job.progress)
          }
        } catch (error) {
          console.error('Failed to check job status:', error)
        }
      }, 2000)
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval)
      }
    }
  }, [currentJob, isGenerating, onComplete, showError])

  const validateForm = () => {
    const newErrors = {}
    
    if (!count || count < 1 || count > 50) {
      newErrors.count = 'Count must be between 1 and 50'
    }
    
    if (!customPrompt.trim()) {
      newErrors.customPrompt = 'Prompt is required'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleGenerate = async () => {
    if (!validateForm()) {
      return
    }

    setIsGenerating(true)
    setJobProgress(0)
    
    try {
      const response = await aiAPI.generateMCQs({
        chapterId,
        count: parseInt(count),
        customPrompt: customPrompt.trim(),
        options: {
          model: 'gpt-3.5-turbo',
          temperature: 0.7,
          maxTokens: 3000
        }
      })
      
      setCurrentJob(response.data)
      showSuccess('AI MCQ generation started', 'Your MCQs are being generated in the background')
    } catch (error) {
      console.error('Failed to start MCQ generation:', error)
      setIsGenerating(false)
      showError('Failed to start AI generation', error.response?.data?.message || error.message)
    }
  }

  const handleCancel = async () => {
    if (currentJob && isGenerating) {
      try {
        await aiAPI.cancelJob(currentJob.jobId)
        setIsGenerating(false)
        setCurrentJob(null)
        setJobProgress(0)
        showWarning('AI generation cancelled')
      } catch (error) {
        console.error('Failed to cancel job:', error)
        showError('Failed to cancel generation')
      }
    }
    onClose()
  }

  const handleClose = () => {
    if (isGenerating) {
      showWarning('Generation in progress', 'Please wait for generation to complete or cancel it first')
      return
    }
    onClose()
  }

  const handleCountChange = (e) => {
    const newCount = parseInt(e.target.value) || 0
    setCount(newCount)
    
    // Update prompt template with new count
    if (customPrompt.includes('{count}')) {
      setCustomPrompt(customPrompt.replace(/\{count\}/g, newCount))
    }
    
    // Clear count error when user starts typing
    if (errors.count) {
      setErrors(prev => ({ ...prev, count: '' }))
    }
  }

  const handlePromptChange = (e) => {
    setCustomPrompt(e.target.value)
    
    // Clear prompt error when user starts typing
    if (errors.customPrompt) {
      setErrors(prev => ({ ...prev, customPrompt: '' }))
    }
  }

  const resetToDefault = () => {
    const prompt = defaultPromptTemplate
      .replace('{count}', count)
      .replace('{title}', chapter?.title || '')
      .replace('{content}', chapter?.content || chapter?.description || '')
    setCustomPrompt(prompt)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b">
          <div className="flex items-center space-x-3">
            <SparklesIcon className="h-6 w-6 text-purple-600" />
            <h3 className="text-lg font-medium text-gray-900">
              AI Generate MCQs
            </h3>
          </div>
          <button
            onClick={handleClose}
            disabled={isGenerating}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="mt-6">
          {/* Chapter Info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Chapter Information</h4>
            <p className="text-sm text-gray-600">
              <span className="font-medium">Title:</span> {chapter?.title || 'N/A'}
            </p>
            {chapter?.description && (
              <p className="text-sm text-gray-600 mt-1">
                <span className="font-medium">Description:</span> {chapter.description}
              </p>
            )}
          </div>

          {/* Generation Form */}
          <div className="space-y-6">
            {/* Count Input */}
            <div>
              <label htmlFor="count" className="block text-sm font-medium text-gray-700 mb-2">
                Number of MCQs to Generate
              </label>
              <input
                type="number"
                id="count"
                min="1"
                max="50"
                value={count}
                onChange={handleCountChange}
                disabled={isGenerating}
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed ${
                  errors.count ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter number between 1-50"
              />
              {errors.count && (
                <p className="mt-1 text-sm text-red-600">{errors.count}</p>
              )}
              <p className="mt-1 text-sm text-gray-500">
                Recommended: 5-15 MCQs for optimal quality
              </p>
            </div>

            {/* Custom Prompt */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label htmlFor="customPrompt" className="block text-sm font-medium text-gray-700">
                  AI Generation Prompt
                </label>
                <button
                  type="button"
                  onClick={resetToDefault}
                  disabled={isGenerating}
                  className="text-sm text-purple-600 hover:text-purple-700 disabled:opacity-50"
                >
                  Reset to Default
                </button>
              </div>
              <textarea
                id="customPrompt"
                rows={6}
                value={customPrompt}
                onChange={handlePromptChange}
                disabled={isGenerating}
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed ${
                  errors.customPrompt ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter your custom prompt for AI generation..."
              />
              {errors.customPrompt && (
                <p className="mt-1 text-sm text-red-600">{errors.customPrompt}</p>
              )}
              <p className="mt-1 text-sm text-gray-500">
                Customize this prompt to guide the AI in generating MCQs that match your requirements.
              </p>
            </div>

            {/* Progress Bar */}
            {isGenerating && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Generation Progress</span>
                  <span className="text-sm text-gray-500">{jobProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${jobProgress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-500">
                  AI is generating your MCQs... This may take a few minutes.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 flex justify-end space-x-3 pt-4 border-t">
          <button
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            {isGenerating ? 'Cancel Generation' : 'Cancel'}
          </button>
          <button
            onClick={handleGenerate}
            disabled={isGenerating || !chapter}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isGenerating ? (
              <>
                <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <SparklesIcon className="h-4 w-4 mr-2" />
                Generate MCQs
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
