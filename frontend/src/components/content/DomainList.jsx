import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { contentAPI } from '../../services/api'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  EyeIcon,
  AcademicCapIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline'
import Breadcrumb from '../shared/Breadcrumb'
import SearchFilter from '../shared/SearchFilter'
import Pagination from '../shared/Pagination'
import ConfirmDialog from '../shared/ConfirmDialog'
import { LoadingPage } from '../shared/LoadingSpinner'
import DomainForm from './DomainForm'

export default function DomainList() {
  const [domains, setDomains] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState({})
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [selectedDomains, setSelectedDomains] = useState([])
  const [showForm, setShowForm] = useState(false)
  const [editingDomain, setEditingDomain] = useState(null)
  const [deleteDialog, setDeleteDialog] = useState({ show: false, domain: null })
  const [bulkDeleteDialog, setBulkDeleteDialog] = useState(false)

  const itemsPerPage = 10

  useEffect(() => {
    loadDomains()
  }, [currentPage, searchQuery, filters])

  const loadDomains = async () => {
    try {
      setLoading(true)
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: searchQuery || undefined,
        ...filters
      }

      const response = await contentAPI.getDomains(params)
      const { data, meta } = response.data

      setDomains(data || [])
      setTotalPages(meta?.totalPages || 1)
      setTotalItems(meta?.total || 0)
    } catch (error) {
      console.error('Failed to load domains:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreate = () => {
    setEditingDomain(null)
    setShowForm(true)
  }

  const handleEdit = (domain) => {
    setEditingDomain(domain)
    setShowForm(true)
  }

  const handleDelete = (domain) => {
    setDeleteDialog({ show: true, domain })
  }

  const confirmDelete = async () => {
    try {
      await contentAPI.deleteDomain(deleteDialog.domain.id)
      setDeleteDialog({ show: false, domain: null })
      loadDomains()
      // Show success notification
    } catch (error) {
      console.error('Failed to delete domain:', error)
      // Show error notification
    }
  }

  const handleBulkDelete = () => {
    setBulkDeleteDialog(true)
  }

  const confirmBulkDelete = async () => {
    try {
      await Promise.all(selectedDomains.map(id => contentAPI.deleteDomain(id)))
      setBulkDeleteDialog(false)
      setSelectedDomains([])
      loadDomains()
      // Show success notification
    } catch (error) {
      console.error('Failed to delete domains:', error)
      // Show error notification
    }
  }

  const handleSelectDomain = (domainId) => {
    setSelectedDomains(prev => 
      prev.includes(domainId) 
        ? prev.filter(id => id !== domainId)
        : [...prev, domainId]
    )
  }

  const handleSelectAll = () => {
    setSelectedDomains(
      selectedDomains.length === domains.length 
        ? [] 
        : domains.map(domain => domain.id)
    )
  }

  const filterOptions = [
    {
      key: 'isActive',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' }
      ]
    }
  ]

  const breadcrumbItems = [
    { name: 'Domains' }
  ]

  if (loading) {
    return <LoadingPage message="Loading domains..." />
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <div className="mb-6">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Domains</h1>
            <p className="mt-2 text-gray-600">
              Manage learning domains and their subjects
            </p>
          </div>
          <button
            onClick={handleCreate}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Domain
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6">
        <SearchFilter
          searchValue={searchQuery}
          onSearchChange={setSearchQuery}
          placeholder="Search domains..."
          filters={filterOptions}
          activeFilters={filters}
          onFilterChange={setFilters}
        />
      </div>

      {/* Bulk Actions */}
      {selectedDomains.length > 0 && (
        <div className="mb-4 bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              {selectedDomains.length} domain{selectedDomains.length > 1 ? 's' : ''} selected
            </span>
            <button
              onClick={handleBulkDelete}
              className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete Selected
            </button>
          </div>
        </div>
      )}

      {/* Domains List */}
      {domains.length === 0 ? (
        <div className="text-center py-12">
          <AcademicCapIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No domains found</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating your first domain.</p>
          <div className="mt-6">
            <button
              onClick={handleCreate}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Domain
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-3 border-b border-gray-200 sm:px-6">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedDomains.length === domains.length}
                onChange={handleSelectAll}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <span className="ml-3 text-sm font-medium text-gray-700">
                Select All
              </span>
            </div>
          </div>
          <ul className="divide-y divide-gray-200">
            {domains.map((domain) => (
              <li key={domain.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedDomains.includes(domain.id)}
                        onChange={() => handleSelectDomain(domain.id)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <div className="ml-4 flex-1 min-w-0">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900">{domain.name}</h3>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            domain.isActive 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {domain.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        {domain.description && (
                          <p className="mt-1 text-sm text-gray-600">{domain.description}</p>
                        )}
                        <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                          <span>Order: {domain.order}</span>
                          <span>Subjects: {domain._count?.subjects || 0}</span>
                          <span>Created: {new Date(domain.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Link
                        to={`/content/domains/${domain.id}/subjects`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        <BookOpenIcon className="h-4 w-4 mr-1" />
                        View Subjects
                      </Link>
                      <button
                        onClick={() => handleEdit(domain)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(domain)}
                        className="p-2 text-gray-400 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={setCurrentPage}
          />
        </div>
      )}

      {/* Domain Form Modal */}
      {showForm && (
        <DomainForm
          domain={editingDomain}
          onClose={() => setShowForm(false)}
          onSave={() => {
            setShowForm(false)
            loadDomains()
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteDialog.show}
        onClose={() => setDeleteDialog({ show: false, domain: null })}
        onConfirm={confirmDelete}
        title="Delete Domain"
        message={`Are you sure you want to delete "${deleteDialog.domain?.name}"? This action cannot be undone and will also delete all associated subjects, chapters, MCQs, and viva questions.`}
        confirmText="Delete"
        type="danger"
      />

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={bulkDeleteDialog}
        onClose={() => setBulkDeleteDialog(false)}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Domains"
        message={`Are you sure you want to delete ${selectedDomains.length} domain${selectedDomains.length > 1 ? 's' : ''}? This action cannot be undone and will also delete all associated content.`}
        confirmText="Delete All"
        type="danger"
      />
    </div>
  )
}
