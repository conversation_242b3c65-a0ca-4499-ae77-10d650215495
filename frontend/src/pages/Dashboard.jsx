import React from 'react'
import { 
  FolderTree, 
  BookOpen, 
  FileText, 
  HelpCircle, 
  MessageSquare, 
  CreditCard, 
  Users,
  TrendingUp,
  Activity
} from 'lucide-react'

const Dashboard = () => {
  // Mock data - replace with real API calls
  const stats = [
    {
      name: 'Total Domains',
      value: '12',
      change: '+2.1%',
      changeType: 'increase',
      icon: FolderTree,
    },
    {
      name: 'Total Subjects',
      value: '48',
      change: '+5.4%',
      changeType: 'increase',
      icon: BookOpen,
    },
    {
      name: 'Total Chapters',
      value: '156',
      change: '+12.3%',
      changeType: 'increase',
      icon: FileText,
    },
    {
      name: 'Total MCQs',
      value: '1,234',
      change: '+8.7%',
      changeType: 'increase',
      icon: HelpCircle,
    },
    {
      name: 'Viva Questions',
      value: '567',
      change: '+3.2%',
      changeType: 'increase',
      icon: MessageSquare,
    },
    {
      name: 'Active Users',
      value: '89',
      change: '+15.3%',
      changeType: 'increase',
      icon: Users,
    },
  ]

  const recentActivity = [
    {
      id: 1,
      type: 'domain',
      action: 'created',
      item: 'Web Development',
      time: '2 hours ago',
    },
    {
      id: 2,
      type: 'mcq',
      action: 'added',
      item: '10 new MCQs to React Basics',
      time: '4 hours ago',
    },
    {
      id: 3,
      type: 'user',
      action: 'subscribed',
      item: 'John Doe subscribed to Premium plan',
      time: '6 hours ago',
    },
    {
      id: 4,
      type: 'chapter',
      action: 'updated',
      item: 'JavaScript Fundamentals',
      time: '1 day ago',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        <p className="mt-2 text-sm text-gray-700">
          Overview of your learning platform
        </p>
      </div>

      {/* Stats grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {stats.map((item) => (
          <div key={item.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                  <item.icon className="w-5 h-5 text-primary-600" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {item.name}
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {item.value}
                    </div>
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                      item.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      <TrendingUp className="w-4 h-4 mr-1" />
                      {item.change}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
            <Activity className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2"></div>
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm text-gray-900">
                    <span className="font-medium capitalize">{activity.action}</span>{' '}
                    {activity.item}
                  </p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick actions */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
              <div className="flex items-center">
                <FolderTree className="w-5 h-5 text-primary-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">Create New Domain</span>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
              <div className="flex items-center">
                <HelpCircle className="w-5 h-5 text-primary-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">Add MCQs</span>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
              <div className="flex items-center">
                <CreditCard className="w-5 h-5 text-primary-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">Manage Subscriptions</span>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
              <div className="flex items-center">
                <Users className="w-5 h-5 text-primary-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">View Users</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
