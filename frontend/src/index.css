@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the admin panel */
@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .input-field {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .sidebar-link {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }

  .sidebar-link-active {
    @apply bg-primary-100 text-primary-700;
  }

  .sidebar-link-inactive {
    @apply text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
}
