import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { authAPI } from '../services/api'

// Auth Context
const AuthContext = createContext()

// Initial state
const initialState = {
  // User authentication
  isAuthenticated: false,
  user: null,
  token: null,
  
  // Loading states
  loading: true,
  loginLoading: false,
  
  // Error handling
  error: null
}

// Action types
const ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_LOGIN_LOADING: 'SET_LOGIN_LOADING',
  SET_ERROR: 'SET_ERROR',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGOUT: 'LOGOUT',
  UPDATE_USER: 'UPDATE_USER',
  CLEAR_ERROR: 'CLEAR_ERROR'
}

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case ACTIONS.SET_LOADING:
      return { ...state, loading: action.payload }
    
    case ACTIONS.SET_LOGIN_LOADING:
      return { ...state, loginLoading: action.payload }
    
    case ACTIONS.SET_ERROR:
      return { ...state, error: action.payload, loginLoading: false }
    
    case ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        loading: false,
        loginLoading: false,
        error: null
      }
    
    case ACTIONS.LOGOUT:
      return {
        ...initialState,
        loading: false
      }
    
    case ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload }
      }
    
    case ACTIONS.CLEAR_ERROR:
      return { ...state, error: null }
    
    default:
      return state
  }
}

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Check for existing auth on mount
  useEffect(() => {
    const checkAuth = () => {
      const token = localStorage.getItem('authToken')
      const userData = localStorage.getItem('userData')
      
      if (token && userData) {
        try {
          const user = JSON.parse(userData)
          dispatch({
            type: ACTIONS.LOGIN_SUCCESS,
            payload: { user, token }
          })
        } catch (error) {
          console.error('Error parsing stored user data:', error)
          localStorage.removeItem('authToken')
          localStorage.removeItem('userData')
          dispatch({ type: ACTIONS.SET_LOADING, payload: false })
        }
      } else {
        dispatch({ type: ACTIONS.SET_LOADING, payload: false })
      }
    }

    checkAuth()
  }, [])

  // Login function
  const login = async (credentials) => {
    try {
      dispatch({ type: ACTIONS.SET_LOGIN_LOADING, payload: true })
      dispatch({ type: ACTIONS.CLEAR_ERROR })
      
      const response = await authAPI.login(credentials)
      
      if (response.data.success) {
        const { user, token } = response.data.data
        
        // Store in localStorage
        localStorage.setItem('authToken', token)
        localStorage.setItem('userData', JSON.stringify(user))
        
        dispatch({
          type: ACTIONS.LOGIN_SUCCESS,
          payload: { user, token }
        })
        
        return { success: true, user }
      } else {
        throw new Error(response.data.message || 'Login failed')
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Login failed'
      dispatch({
        type: ACTIONS.SET_ERROR,
        payload: errorMessage
      })
      throw error
    }
  }

  // Register function
  const register = async (userData) => {
    try {
      dispatch({ type: ACTIONS.SET_LOGIN_LOADING, payload: true })
      dispatch({ type: ACTIONS.CLEAR_ERROR })
      
      const response = await authAPI.register(userData)
      
      if (response.data.success) {
        const { user, token } = response.data.data
        
        // Store in localStorage
        localStorage.setItem('authToken', token)
        localStorage.setItem('userData', JSON.stringify(user))
        
        dispatch({
          type: ACTIONS.LOGIN_SUCCESS,
          payload: { user, token }
        })
        
        return { success: true, user }
      } else {
        throw new Error(response.data.message || 'Registration failed')
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Registration failed'
      dispatch({
        type: ACTIONS.SET_ERROR,
        payload: errorMessage
      })
      throw error
    }
  }

  // Logout function
  const logout = async () => {
    try {
      await authAPI.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear localStorage
      localStorage.removeItem('authToken')
      localStorage.removeItem('userData')
      
      dispatch({ type: ACTIONS.LOGOUT })
    }
  }

  // Update user data
  const updateUser = (userData) => {
    const updatedUser = { ...state.user, ...userData }
    localStorage.setItem('userData', JSON.stringify(updatedUser))
    dispatch({
      type: ACTIONS.UPDATE_USER,
      payload: userData
    })
  }

  // Clear error
  const clearError = () => {
    dispatch({ type: ACTIONS.CLEAR_ERROR })
  }

  const value = {
    // State
    ...state,
    
    // Actions
    login,
    register,
    logout,
    updateUser,
    clearError
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext
