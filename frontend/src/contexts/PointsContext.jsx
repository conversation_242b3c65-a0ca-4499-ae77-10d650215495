import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { pointsAPI, referralAPI } from '../services/api'

// Points Context
const PointsContext = createContext()

// Initial state
const initialState = {
  // User points data
  pointBalance: 0,
  totalPointsEarned: 0,
  loginStreak: 0,
  lastLoginDate: null,
  
  // Referral data
  referralCode: null,
  referredBy: null,
  referralStats: {
    totalReferrals: 0,
    totalEarnings: 0,
    shareUrl: null
  },
  
  // UI state
  loading: false,
  error: null,
  showDailyReward: false,
  dailyRewardData: null,
  
  // Transaction history
  transactions: [],
  transactionsPagination: {
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  }
}

// Action types
const ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_POINTS_DATA: 'SET_POINTS_DATA',
  SET_REFERRAL_DATA: 'SET_REFERRAL_DATA',
  SET_TRANSACTIONS: 'SET_TRANSACTIONS',
  ADD_TRANSACTION: 'ADD_TRANSACTION',
  UPDATE_POINTS: 'UPDATE_POINTS',
  SHOW_DAILY_REWARD: 'SHOW_DAILY_REWARD',
  HIDE_DAILY_REWARD: 'HIDE_DAILY_REWARD',
  RESET_STATE: 'RESET_STATE'
}

// Reducer
const pointsReducer = (state, action) => {
  switch (action.type) {
    case ACTIONS.SET_LOADING:
      return { ...state, loading: action.payload }
    
    case ACTIONS.SET_ERROR:
      return { ...state, error: action.payload, loading: false }
    
    case ACTIONS.SET_POINTS_DATA:
      return {
        ...state,
        ...action.payload,
        loading: false,
        error: null
      }
    
    case ACTIONS.SET_REFERRAL_DATA:
      return {
        ...state,
        referralCode: action.payload.referralCode,
        referredBy: action.payload.referredBy,
        referralStats: action.payload.referralStats,
        loading: false,
        error: null
      }
    
    case ACTIONS.SET_TRANSACTIONS:
      return {
        ...state,
        transactions: action.payload.transactions,
        transactionsPagination: action.payload.pagination,
        loading: false,
        error: null
      }
    
    case ACTIONS.ADD_TRANSACTION:
      return {
        ...state,
        transactions: [action.payload, ...state.transactions],
        transactionsPagination: {
          ...state.transactionsPagination,
          total: state.transactionsPagination.total + 1
        }
      }
    
    case ACTIONS.UPDATE_POINTS:
      return {
        ...state,
        pointBalance: action.payload.pointBalance,
        totalPointsEarned: action.payload.totalPointsEarned || state.totalPointsEarned
      }
    
    case ACTIONS.SHOW_DAILY_REWARD:
      return {
        ...state,
        showDailyReward: true,
        dailyRewardData: action.payload
      }
    
    case ACTIONS.HIDE_DAILY_REWARD:
      return {
        ...state,
        showDailyReward: false,
        dailyRewardData: null
      }
    
    case ACTIONS.RESET_STATE:
      return initialState
    
    default:
      return state
  }
}

// Points Provider Component
export const PointsProvider = ({ children }) => {
  const [state, dispatch] = useReducer(pointsReducer, initialState)

  // Load initial points data
  const loadPointsData = async () => {
    try {
      dispatch({ type: ACTIONS.SET_LOADING, payload: true })
      
      const [pointsResponse, referralResponse] = await Promise.all([
        pointsAPI.getPointsHistory({ page: 1, limit: 1 }),
        referralAPI.getStats()
      ])

      if (pointsResponse.data.success) {
        dispatch({
          type: ACTIONS.SET_POINTS_DATA,
          payload: {
            pointBalance: pointsResponse.data.data.user.pointBalance,
            totalPointsEarned: pointsResponse.data.data.user.totalPointsEarned,
            loginStreak: pointsResponse.data.data.user.loginStreak,
            lastLoginDate: pointsResponse.data.data.user.lastLoginDate
          }
        })
      }

      if (referralResponse.data.success) {
        dispatch({
          type: ACTIONS.SET_REFERRAL_DATA,
          payload: {
            referralCode: referralResponse.data.data.user.referralCode,
            referredBy: referralResponse.data.data.user.referredBy,
            referralStats: referralResponse.data.data.referralStats
          }
        })
      }
    } catch (error) {
      console.error('Error loading points data:', error)
      dispatch({
        type: ACTIONS.SET_ERROR,
        payload: error.response?.data?.message || 'Failed to load points data'
      })
    }
  }

  // Record daily login
  const recordDailyLogin = async () => {
    try {
      const response = await pointsAPI.recordDailyLogin()
      
      if (response.data.success && response.data.data.pointsEarned > 0) {
        // Show daily reward popup
        dispatch({
          type: ACTIONS.SHOW_DAILY_REWARD,
          payload: response.data.data
        })
        
        // Update points balance
        dispatch({
          type: ACTIONS.UPDATE_POINTS,
          payload: {
            pointBalance: response.data.data.totalPoints,
            totalPointsEarned: state.totalPointsEarned + response.data.data.pointsEarned
          }
        })
        
        // Add transaction to history
        if (response.data.data.transaction) {
          dispatch({
            type: ACTIONS.ADD_TRANSACTION,
            payload: {
              id: response.data.data.transaction.id,
              type: 'earned',
              category: 'daily_login',
              amount: response.data.data.pointsEarned,
              description: response.data.data.transaction.description,
              createdAt: new Date().toISOString()
            }
          })
        }
      }
      
      return response.data
    } catch (error) {
      console.error('Error recording daily login:', error)
      throw error
    }
  }

  // Record study time
  const recordStudyTime = async (studyTimeMinutes, activityType) => {
    try {
      const response = await pointsAPI.recordStudyTime({
        studyTimeMinutes,
        activityType
      })
      
      if (response.data.success && response.data.data.pointsEarned > 0) {
        // Update points balance
        dispatch({
          type: ACTIONS.UPDATE_POINTS,
          payload: {
            pointBalance: response.data.data.totalPoints,
            totalPointsEarned: state.totalPointsEarned + response.data.data.pointsEarned
          }
        })
        
        // Add transaction to history
        if (response.data.data.transaction) {
          dispatch({
            type: ACTIONS.ADD_TRANSACTION,
            payload: {
              id: response.data.data.transaction.id,
              type: 'earned',
              category: 'study_time',
              amount: response.data.data.pointsEarned,
              description: response.data.data.transaction.description,
              createdAt: new Date().toISOString()
            }
          })
        }
      }
      
      return response.data
    } catch (error) {
      console.error('Error recording study time:', error)
      throw error
    }
  }

  // Calculate point discount
  const calculateDiscount = async (pointsToUse, purchaseType, itemId) => {
    try {
      const response = await pointsAPI.calculateDiscount({
        pointsToUse,
        purchaseType,
        itemId
      })
      return response.data
    } catch (error) {
      console.error('Error calculating discount:', error)
      throw error
    }
  }

  // Load transaction history
  const loadTransactions = async (page = 1, filters = {}) => {
    try {
      dispatch({ type: ACTIONS.SET_LOADING, payload: true })
      
      const response = await pointsAPI.getPointsHistory({
        page,
        limit: state.transactionsPagination.limit,
        ...filters
      })
      
      if (response.data.success) {
        dispatch({
          type: ACTIONS.SET_TRANSACTIONS,
          payload: {
            transactions: response.data.data.transactions,
            pagination: response.data.data.pagination
          }
        })
      }
    } catch (error) {
      console.error('Error loading transactions:', error)
      dispatch({
        type: ACTIONS.SET_ERROR,
        payload: error.response?.data?.message || 'Failed to load transactions'
      })
    }
  }

  // Generate referral code
  const generateReferralCode = async (customCode = null) => {
    try {
      const response = await referralAPI.generateCode(
        customCode ? { customCode } : {}
      )
      
      if (response.data.success) {
        dispatch({
          type: ACTIONS.SET_REFERRAL_DATA,
          payload: {
            ...state,
            referralCode: response.data.data.referralCode,
            referralStats: {
              ...state.referralStats,
              shareUrl: response.data.data.shareUrl
            }
          }
        })
      }
      
      return response.data
    } catch (error) {
      console.error('Error generating referral code:', error)
      throw error
    }
  }

  // Apply referral code
  const applyReferralCode = async (referralCode) => {
    try {
      const response = await referralAPI.applyCode({ referralCode })
      
      if (response.data.success) {
        // Update points and referral data
        dispatch({
          type: ACTIONS.UPDATE_POINTS,
          payload: {
            pointBalance: response.data.data.totalPoints,
            totalPointsEarned: state.totalPointsEarned + response.data.data.pointsEarned
          }
        })
        
        dispatch({
          type: ACTIONS.SET_REFERRAL_DATA,
          payload: {
            ...state,
            referredBy: referralCode
          }
        })
      }
      
      return response.data
    } catch (error) {
      console.error('Error applying referral code:', error)
      throw error
    }
  }

  // Hide daily reward popup
  const hideDailyReward = () => {
    dispatch({ type: ACTIONS.HIDE_DAILY_REWARD })
  }

  // Reset state (for logout)
  const resetState = () => {
    dispatch({ type: ACTIONS.RESET_STATE })
  }

  // Load data on mount
  useEffect(() => {
    loadPointsData()
  }, [])

  const value = {
    // State
    ...state,
    
    // Actions
    recordDailyLogin,
    recordStudyTime,
    calculateDiscount,
    loadTransactions,
    generateReferralCode,
    applyReferralCode,
    hideDailyReward,
    resetState,
    loadPointsData
  }

  return (
    <PointsContext.Provider value={value}>
      {children}
    </PointsContext.Provider>
  )
}

// Custom hook to use points context
export const usePoints = () => {
  const context = useContext(PointsContext)
  if (!context) {
    throw new Error('usePoints must be used within a PointsProvider')
  }
  return context
}

export default PointsContext
