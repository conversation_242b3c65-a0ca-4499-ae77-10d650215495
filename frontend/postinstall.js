import fse from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Copying TinyMCE assets to public directory...');

// Empty the tinymce directory in public
fse.emptyDirSync(path.join(__dirname, 'public', 'tinymce'));

// Copy TinyMCE from node_modules to public
fse.copySync(
  path.join(__dirname, 'node_modules', 'tinymce'), 
  path.join(__dirname, 'public', 'tinymce'), 
  { overwrite: true }
);

console.log('TinyMCE assets copied successfully!');
