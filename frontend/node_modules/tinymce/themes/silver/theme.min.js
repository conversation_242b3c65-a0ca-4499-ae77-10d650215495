!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),x=(e,t)=>(...o)=>e(t.apply(null,o)),y=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function C(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const k=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=y(!1),E=y(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,I=(e,t)=>D.call(e,t),F=(e,t)=>I(e,t)>-1,R=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},N=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},L=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},V=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},H=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},P=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},U=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),W=(e,t,o)=>(V(e,((e,n)=>{o=t(o,e,n)})),o),$=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),G=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},j=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},q=(e,t)=>j(L(e,t)),X=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},Y=e=>{const t=M.call(e,0);return t.reverse(),t},K=(e,t)=>P(e,(e=>!F(t,e))),J=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],Z=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},ee=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),te=e=>ee(e,0),oe=e=>ee(e,e.length-1),ne=p(Array.from)?Array.from:e=>M.call(e),se=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},re=Object.keys,ae=Object.hasOwnProperty,ie=(e,t)=>{const o=re(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},le=(e,t)=>ce(e,((e,o)=>({k:o,v:t(e,o)}))),ce=(e,t)=>{const o={};return ie(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},de=e=>(t,o)=>{e[o]=t},ue=(e,t,o,n)=>{ie(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},me=(e,t)=>{const o={};return ue(e,t,de(o),b),o},ge=(e,t)=>{const o=[];return ie(e,((e,n)=>{o.push(t(e,n))})),o},pe=(e,t)=>{const o=re(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},he=e=>ge(e,w),fe=(e,t)=>be(e,t)?A.from(e[t]):A.none(),be=(e,t)=>ae.call(e,t),ve=(e,t)=>be(e,t)&&void 0!==e[t]&&null!==e[t],xe=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return V(e,((n,s)=>{const r=re(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=re(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!X(t,(e=>F(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o},ye=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},we=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),V(o,r),o=[])})),{get:n,map:e=>we((t=>{n((o=>{t(e(o))}))})),isReady:s}},Se={nu:we,pure:e=>we((t=>{t(e)}))},Ce=e=>{setTimeout((()=>{throw e}),0)},ke=e=>{const t=t=>{e().then(t,Ce)};return{map:t=>ke((()=>e().then(t))),bind:t=>ke((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>ke((()=>e().then((()=>t.toPromise())))),toLazy:()=>Se.nu(t),toCached:()=>{let t=null;return ke((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},Oe=e=>ke((()=>new Promise(e))),_e=e=>ke((()=>Promise.resolve(e))),Te=e=>{const t=t=>t(e),o=y(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>Ae.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},Ee=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>Ae.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},Ae={value:Te,error:Ee,fromOption:(e,t)=>e.fold((()=>Ee(t)),Te)},Me=e=>({...e,toCached:()=>Me(e.toCached()),bindFuture:t=>Me(e.bind((e=>e.fold((e=>_e(Ae.error(e))),(e=>t(e)))))),bindResult:t=>Me(e.map((e=>e.bind(t)))),mapResult:t=>Me(e.map((e=>e.map(t)))),mapError:t=>Me(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>Me(Oe((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(Ae.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),De=e=>Me(Oe(e)),Be="undefined"!=typeof window?window:Function("return this;")(),Ie=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Fe=(e,t,o)=>Math.min(Math.max(e,t),o);let Re=0;const Ne=e=>{const t=(new Date).getTime(),o=Math.floor(window.crypto.getRandomValues(new Uint32Array(1))[0]/4294967295*1e9);return Re++,e+"_"+o+Re+String(t)},ze=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)be(s,t)&&(o[t]=e(o[t],s[t]))}return o},Le=ze(((e,t)=>i(e)&&i(t)?Le(e,t):t)),Ve=ze(((e,t)=>t)),He=(e,t,o=S)=>e.exists((e=>o(e,t))),Pe=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Ue=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),We=(e,t)=>null!=e?A.some(t(e)):A.none(),$e=(e,t)=>e?A.some(t):A.none(),Ge=(e,t)=>((e,t)=>{let o=null!=t?t:Be;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t);xe([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const je=e=>{const t=ye(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},qe=()=>je((e=>e.unbind())),Xe=()=>{const e=je(b);return{...e,on:t=>e.get().each(t)}},Ye=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Ke=(e,t)=>Qe(e,t)?((e,t)=>e.substring(t))(e,t.length):e,Je=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Qe=(e,t)=>Ye(e,t,0),Ze=(e,t)=>Ye(e,t,e.length-t.length),et=(ft=/^\s+|\s+$/g,e=>e.replace(ft,"")),tt=e=>e.length>0,ot=e=>!tt(e),nt=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},st=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},rt=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},at=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return rt(o.childNodes[0])},it=(e,t)=>{const o=(t||document).createElement(e);return rt(o)},lt=(e,t)=>{const o=(t||document).createTextNode(e);return rt(o)},ct=rt,dt=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},ut=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},mt=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),gt=xe([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),pt=(e,t,o)=>t(ct(o.startContainer),o.startOffset,ct(o.endContainer),o.endOffset),ht=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:y(e),rtl:A.none}),relative:(t,o)=>({ltr:st((()=>dt(e,t,o))),rtl:st((()=>A.some(dt(e,o,t))))}),exact:(t,o,n,s)=>({ltr:st((()=>ut(e,t,o,n,s))),rtl:st((()=>A.some(ut(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>gt.rtl(ct(e.endContainer),e.endOffset,ct(e.startContainer),e.startOffset))).getOrThunk((()=>pt(0,gt.ltr,o))):pt(0,gt.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});var ft;gt.ltr,gt.rtl;const bt=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},vt=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,xt=(e,t)=>{const o=void 0===t?document:t.dom;return vt(o)?A.none():A.from(o.querySelector(e)).map(ct)},yt=(e,t)=>e.dom===t.dom,wt=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},St=()=>Ct(0,0),Ct=(e,t)=>({major:e,minor:t}),kt={nu:Ct,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?St():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return Ct(n(1),n(2))})(e,o)},unknown:St},Ot=(e,t)=>{const o=String(t).toLowerCase();return $(e,(e=>e.search(o)))},_t=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Tt=e=>t=>Je(t,e),Et=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Je(e,"edge/")&&Je(e,"chrome")&&Je(e,"safari")&&Je(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,_t],search:e=>Je(e,"chrome")&&!Je(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Je(e,"msie")||Je(e,"trident")},{name:"Opera",versionRegexes:[_t,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Tt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Tt("firefox")},{name:"Safari",versionRegexes:[_t,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Je(e,"safari")||Je(e,"mobile/"))&&Je(e,"applewebkit")}],At=[{name:"Windows",search:Tt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Je(e,"iphone")||Je(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Tt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Tt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Tt("linux"),versionRegexes:[]},{name:"Solaris",search:Tt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Tt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Tt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Mt={browsers:y(Et),oses:y(At)},Dt="Edge",Bt="Chromium",It="Opera",Ft="Firefox",Rt="Safari",Nt=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(Dt),isChromium:n(Bt),isIE:n("IE"),isOpera:n(It),isFirefox:n(Ft),isSafari:n(Rt)}},zt=()=>Nt({current:void 0,version:kt.unknown()}),Lt=Nt,Vt=(y(Dt),y(Bt),y("IE"),y(It),y(Ft),y(Rt),"Windows"),Ht="Android",Pt="Linux",Ut="macOS",Wt="Solaris",$t="FreeBSD",Gt="ChromeOS",jt=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(Vt),isiOS:n("iOS"),isAndroid:n(Ht),isMacOS:n(Ut),isLinux:n(Pt),isSolaris:n(Wt),isFreeBSD:n($t),isChromeOS:n(Gt)}},qt=()=>jt({current:void 0,version:kt.unknown()}),Xt=jt,Yt=(y(Vt),y("iOS"),y(Ht),y(Pt),y(Ut),y(Wt),y($t),y(Gt),e=>window.matchMedia(e).matches);let Kt=st((()=>((e,t,o)=>{const n=Mt.browsers(),s=Mt.oses(),r=t.bind((e=>((e,t)=>se(t.brands,(t=>{const o=t.brand.toLowerCase();return $(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:kt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>Ot(e,t).map((e=>{const o=kt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(zt,Lt),a=((e,t)=>Ot(e,t).map((e=>{const o=kt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(qt,Xt),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:y(s),isiPhone:y(r),isTablet:y(l),isPhone:y(c),isTouch:y(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:y(d),isDesktop:y(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(window.navigator.userAgent,A.from(window.navigator.userAgentData),Yt)));const Jt=()=>Kt(),Qt=Object.getPrototypeOf,Zt=e=>{const t=Ge("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>Ge(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Qt(e).constructor.name))},eo=e=>e.dom.nodeName.toLowerCase(),to=e=>t=>(e=>e.dom.nodeType)(t)===e,oo=e=>no(e)&&Zt(e.dom),no=to(1),so=to(3),ro=to(9),ao=to(11),io=e=>t=>no(t)&&eo(t)===e,lo=e=>ct(e.dom.ownerDocument),co=e=>ro(e)?e:lo(e),uo=e=>ct(co(e).dom.documentElement),mo=e=>ct(co(e).dom.defaultView),go=e=>A.from(e.dom.parentNode).map(ct),po=e=>A.from(e.dom.parentElement).map(ct),ho=e=>A.from(e.dom.offsetParent).map(ct),fo=e=>L(e.dom.childNodes,ct),bo=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(ct)},vo=e=>bo(e,0),xo=(e,t)=>({element:e,offset:t}),yo=(e,t)=>{const o=fo(e);return o.length>0&&t<o.length?xo(o[t],0):xo(e,t)},wo=e=>ao(e)&&g(e.dom.host),So=e=>ct(e.dom.getRootNode()),Co=e=>wo(e)?e:ct(co(e).dom.body),ko=e=>{const t=So(e);return wo(t)?A.some(t):A.none()},Oo=e=>ct(e.dom.host),_o=e=>{const t=ct((e=>{if(g(e.target)){const t=ct(e.target);if(no(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return te(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=x(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},To=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(_o(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:C(Eo,e,t,r,s)}},Eo=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Ao=E,Mo=(e,t,o)=>((e,t,o,n)=>To(e,t,o,n,!1))(e,t,Ao,o),Do=(e,t,o)=>((e,t,o,n)=>To(e,t,o,n,!0))(e,t,Ao,o),Bo=_o,Io=()=>ct(document),Fo=(e,t=!1)=>e.dom.focus({preventScroll:t}),Ro=e=>e.dom.blur(),No=e=>{const t=So(e).dom;return e.dom===t.activeElement},zo=(e=Io())=>A.from(e.dom.activeElement).map(ct),Lo=e=>zo(So(e)).filter((t=>e.dom.contains(t.dom))),Vo=(e,t)=>{go(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},Ho=(e,t)=>{const o=(e=>A.from(e.dom.nextSibling).map(ct))(e);o.fold((()=>{go(e).each((e=>{Uo(e,t)}))}),(e=>{Vo(e,t)}))},Po=(e,t)=>{vo(e).fold((()=>{Uo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Uo=(e,t)=>{e.dom.appendChild(t.dom)},Wo=(e,t)=>{V(t,(t=>{Uo(e,t)}))},$o=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},Go=(e,t,o)=>{$o(e.dom,t,o)},jo=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{$o(o,t,e)}))},qo=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},Xo=(e,t)=>A.from(qo(e,t)),Yo=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},Ko=(e,t)=>{e.dom.removeAttribute(t)},Jo=e=>{e.dom.textContent="",V(fo(e),(e=>{Qo(e)}))},Qo=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Zo=(e,t)=>ct(e.dom.cloneNode(t)),en=e=>e.dom.innerHTML,tn=(e,t)=>{const o=lo(e).dom,n=ct(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,fo(ct(o))})(t,o);Wo(n,s),Jo(e),Uo(e,n)},on=e=>new Promise(((t,o)=>{const n=()=>{r(),t(e)},s=[Mo(e,"load",n),Mo(e,"error",(()=>{r(),o("Unable to load data from image: "+e.dom.src)}))],r=()=>V(s,(e=>e.unbind()));e.dom.complete&&n()})),nn=e=>void 0!==e.style&&p(e.style.getPropertyValue),sn=e=>{const t=so(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return ko(ct(t)).fold((()=>o.body.contains(t)),(n=sn,s=Oo,e=>n(s(e))));var n,s},rn=()=>an(ct(document)),an=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return ct(t)},ln=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);nn(e)&&e.style.setProperty(t,o)},cn=(e,t)=>{nn(e)&&e.style.removeProperty(t)},dn=(e,t,o)=>{const n=e.dom;ln(n,t,o)},un=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{ln(o,t,e)}))},mn=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{e.fold((()=>{cn(o,t)}),(e=>{ln(o,t,e)}))}))},gn=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||sn(e)?n:pn(o,t)},pn=(e,t)=>nn(e)?e.style.getPropertyValue(t):"",hn=(e,t)=>{const o=e.dom,n=pn(o,t);return A.from(n).filter((e=>e.length>0))},fn=e=>{const t={},o=e.dom;if(nn(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},bn=(e,t,o)=>{const n=it(e);return dn(n,t,o),hn(n,t).isSome()},vn=(e,t)=>{const o=e.dom;cn(o,t),He(Xo(e,"style").map(et),"")&&Ko(e,"style")},xn=e=>e.dom.offsetWidth,yn=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=gn(o,e);return parseFloat(t)||0}return n},n=(e,t)=>W(t,((t,o)=>{const n=gn(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;nn(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},wn=yn("height",(e=>{const t=e.dom;return sn(e)?t.getBoundingClientRect().height:t.offsetHeight})),Sn=e=>wn.get(e),Cn=e=>wn.getOuter(e),kn=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),On=yn("width",(e=>e.dom.offsetWidth)),_n=yn("width",(e=>{const t=e.dom;return sn(e)?t.getBoundingClientRect().width:t.offsetWidth})),Tn=e=>On.get(e),En=e=>On.getOuter(e),An=e=>_n.getOuter(e),Mn=(e,t)=>({left:e,top:t,translate:(o,n)=>Mn(e+o,t+n)}),Dn=Mn,Bn=(e,t)=>void 0!==e?e:void 0!==t?t:0,In=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return Dn(o.offsetLeft,o.offsetTop);const r=Bn(null==n?void 0:n.pageYOffset,s.scrollTop),a=Bn(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Bn(s.clientTop,o.clientTop),l=Bn(s.clientLeft,o.clientLeft);return Fn(e).translate(a-l,r-i)},Fn=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?Dn(o.offsetLeft,o.offsetTop):sn(e)?(e=>{const t=e.getBoundingClientRect();return Dn(t.left,t.top)})(t):Dn(0,0)},Rn=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return Dn(o,n)},Nn=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},zn=(e=>{const t=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:o=>{if(!e(o))throw new Error("Can only get text value of a text node");return t(o).getOr("")},getOption:t,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(so),Ln=(e,t)=>o=>"rtl"===Vn(o)?t:e,Vn=e=>"rtl"===gn(e,"direction")?"rtl":"ltr",Hn=(e,t)=>{const o=qo(e,t);return void 0===o||""===o?[]:o.split(" ")};var Pn=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const Un=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=ct(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},Wn=(e,t,o)=>Pn(((e,t)=>t(e)),Un,e,t,o),$n=(e,t)=>$(e.dom.childNodes,(e=>t(ct(e)))).map(ct),Gn=(e,t,o)=>Un(e,(e=>bt(e,t)),o),jn=(e,t)=>(e=>{const o=e.dom;return o.parentNode?$n(ct(o.parentNode),(o=>!yt(e,o)&&bt(o,t))):A.none()})(e),qn=(e,t)=>$n(e,(e=>bt(e,t))),Xn=(e,t)=>xt(t,e),Yn=(e,t,o)=>Pn(((e,t)=>bt(e,t)),Gn,e,t,o),Kn=(e,t)=>{e.dom.checked=t},Jn=e=>e.dom.checked,Qn=e=>void 0!==e.dom.classList,Zn=e=>Hn(e,"class"),es=(e,t)=>((e,t,o)=>{const n=Hn(e,t).concat([o]);return Go(e,t,n.join(" ")),!0})(e,"class",t),ts=(e,t)=>((e,t,o)=>{const n=P(Hn(e,t),(e=>e!==o));return n.length>0?Go(e,t,n.join(" ")):Ko(e,t),!1})(e,"class",t),os=(e,t)=>{Qn(e)?e.dom.classList.add(t):es(e,t)},ns=e=>{0===(Qn(e)?e.dom.classList:Zn(e)).length&&Ko(e,"class")},ss=(e,t)=>{Qn(e)?e.dom.classList.remove(t):ts(e,t),ns(e)},rs=(e,t)=>Qn(e)&&e.dom.classList.contains(t),as=(e,t)=>{V(t,(t=>{os(e,t)}))},is=(e,t)=>{V(t,(t=>{ss(e,t)}))},ls=e=>Qn(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Zn(e),cs=e=>e.dom.value,ds=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},us=(e,t,o)=>P(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=ct(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),ms=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return vt(o)?[]:L(o.querySelectorAll(e),ct)})(t,e),gs=(e,t,o)=>Wn(e,t,o).isSome(),ps=(e,t,o)=>Yn(e,t,o).isSome(),hs=e=>p(e)?e:T,fs=(e,t,o)=>{let n=e.dom;const s=hs(o);for(;n.parentNode;){n=n.parentNode;const e=ct(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},bs=(e,t,o)=>{const n=t(e),s=hs(o);return n.orThunk((()=>s(e)?A.none():fs(e,t,s)))},vs=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),xs=xe([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),ys=(xs.before,xs.on,xs.after,e=>e.fold(w,w,w)),ws=xe([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Ss={domRange:ws.domRange,relative:ws.relative,exact:ws.exact,exactFromRange:e=>ws.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>ct(e.startContainer),relative:(e,t)=>ys(e),exact:(e,t,o,n)=>e}))(e);return mo(t)},range:vs},Cs=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(vs(ct(t.startContainer),t.startOffset,ct(o.endContainer),o.endOffset))}return A.none()},ks=e=>{if(null===e.anchorNode||null===e.focusNode)return Cs(e);{const t=ct(e.anchorNode),o=ct(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=lo(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=yt(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(vs(t,e.anchorOffset,o,e.focusOffset)):Cs(e)}},Os=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(mt):A.none()})(ht(e,t)),_s={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},Ts=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),Es=(e,t)=>A.from(Ts.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>R(t,(t=>R(_s[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),As=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Ms=e=>{const t=void 0===e?window:e,o=t.document,n=Rn(ct(o));return(e=>{const t=void 0===e?window:e;return Jt().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return As(n.left,n.top,o,s)}),(e=>As(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},Ds=(e,t)=>e.view(t).fold(y([]),(t=>{const o=e.owner(t),n=Ds(e,o);return[t].concat(n)}));var Bs=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(ct)},owner:e=>lo(e)});const Is=e=>{const t=Io(),o=Rn(t),n=((e,t)=>{const o=t.owner(e),n=Ds(t,o);return A.some(n)})(e,Bs);return n.fold(C(In,e),(t=>{const n=Fn(e),s=U(t,((e,t)=>{const o=Fn(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return Dn(s.left+n.left+o.left,s.top+n.top+o.top)}))},Fs=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Rs=e=>{const t=In(e),o=En(e),n=Cn(e);return Fs(t.left,t.top,o,n)},Ns=e=>{const t=Is(e),o=En(e),n=Cn(e);return Fs(t.left,t.top,o,n)},zs=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Fs(o,n,s-o,r-n)},Ls=()=>Ms(window),Vs=(e,t)=>yt(e.element,t.event.target),Hs=e=>(He(hn(e,"position"),"fixed")?A.none():ho(e)).orThunk((()=>{const t=it("span");return go(e).bind((e=>{Uo(e,t);const o=ho(t);return Qo(t),o}))})),Ps=e=>Hs(e).map(In).getOrThunk((()=>Dn(0,0)));var Us;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(Us||(Us={}));const Ws=(e,t,o)=>e.stype===Us.Error?t(e.serror):o(e.svalue),$s=e=>({stype:Us.Value,svalue:e}),Gs=e=>({stype:Us.Error,serror:e}),js=$s,qs=Gs,Xs=Ws,Ys=e=>a(e)&&re(e).length>100?" removed due to size":JSON.stringify(e,null,2),Ks=(e,t)=>qs([{path:e,getErrorInfo:t}]),Js=e=>({extract:(t,o)=>((e,t)=>e.stype===Us.Error?t(e.serror):e)(e(o),(e=>((e,t)=>Ks(e,y(t)))(t,e))),toString:y("val")}),Qs=Js(js),Zs=y(Qs),er=(e,t)=>Js((o=>{const n=typeof o;return e(o)?js(o):qs(`Expected type: ${t} but got: ${n}`)})),tr=er(h,"number"),or=er(r,"string"),nr=er(d,"boolean"),sr=er(p,"function"),rr=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>rr(e[t])));default:return!1}},ar=Js((e=>rr(e)?js(e):qs("Expected value to be acceptable for sending via postMessage"))),ir=e=>({tag:"defaultedThunk",process:e}),lr=e=>ir(y(e)),cr=e=>({tag:"mergeWithThunk",process:e}),dr=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),ur=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},mr=e=>{const t=(e=>{const t=[],o=[];return V(e,(e=>{Ws(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,x(qs,j)(o)):js(t.values);var o},gr=(e,t,o,n)=>n(fe(e,t).getOrThunk((()=>o(e)))),pr=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>js(A.none())),(e=>((e,t)=>e.stype===Us.Value?{stype:Us.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>fe(t,o).fold((()=>((e,t,o)=>Ks(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Ys(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return gr(o,n,e.process,r);case"option":return((e,t,o)=>o(fe(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(fe(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return gr(o,n,y({}),(t=>{const n=Le(e.process(o),t);return r(n)}))}},hr=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),fr=e=>re(me(e,g)),br=e=>{const t=vr(e),o=U(e,((e,t)=>ur(t,(t=>Le(e,{[t]:!0})),y(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:fr(n),r=P(s,(e=>!ve(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>Ks(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},vr=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)ur(r,((o,r,a,i)=>{const l=pr(a,e,t,o,i);Xs(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?qs(s):js(n)})(t,o,e),toString:()=>{const t=L(e,(e=>ur(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),xr=e=>({extract:(t,o)=>{const n=L(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return mr(n)},toString:()=>"array("+e.toString()+")"}),yr=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===Us.Value)return{stype:Us.Value,svalue:o(e.svalue)};s.push(e)}return mr(s)},toString:()=>"oneOf("+L(e,(e=>e.toString())).join(", ")+")"}},wr=(e,t)=>({extract:(o,n)=>{const s=re(n),r=((t,o)=>xr(Js(e)).extract(t,o))(o,s);return((e,t)=>e.stype===Us.Value?t(e.svalue):e)(r,(e=>{const s=L(e,(e=>dr(e,e,{tag:"required",process:{}},t)));return vr(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),Sr=x(xr,vr),Cr=(e,t)=>({extract:(o,n)=>fe(n,e).fold((()=>((e,t)=>Ks(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>fe(o,n).fold((()=>((e,t,o)=>Ks(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+Ys(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+re(t)}),kr=e=>Js((t=>e(t).fold(qs,js))),Or=(e,t)=>wr((t=>e(t).fold(Gs,$s)),t),_r=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===Us.Error?{stype:Us.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),Ws(n,Ae.error,Ae.value);var n},Tr=e=>e.fold((e=>{throw new Error(Ar(e))}),w),Er=(e,t,o)=>Tr(_r(e,t,o)),Ar=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:y("... (only showing first ten failures)")}]):e;return L(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+Ys(e.input),Mr=(e,t)=>Cr(e,le(t,vr)),Dr=(e,t)=>((e,t)=>{const o=st(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),Br=dr,Ir=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),Fr=e=>kr((t=>F(e,t)?Ae.value(t):Ae.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),Rr=e=>Br(e,e,{tag:"required",process:{}},Zs()),Nr=(e,t)=>Br(e,e,{tag:"required",process:{}},t),zr=e=>Nr(e,tr),Lr=e=>Nr(e,or),Vr=(e,t)=>Br(e,e,{tag:"required",process:{}},Fr(t)),Hr=e=>Nr(e,sr),Pr=(e,t)=>Br(e,e,{tag:"required",process:{}},vr(t)),Ur=(e,t)=>Br(e,e,{tag:"required",process:{}},Sr(t)),Wr=(e,t)=>Br(e,e,{tag:"required",process:{}},xr(t)),$r=e=>Br(e,e,{tag:"option",process:{}},Zs()),Gr=(e,t)=>Br(e,e,{tag:"option",process:{}},t),jr=e=>Gr(e,tr),qr=e=>Gr(e,or),Xr=(e,t)=>Gr(e,Fr(t)),Yr=e=>Gr(e,sr),Kr=(e,t)=>Gr(e,xr(t)),Jr=(e,t)=>Gr(e,vr(t)),Qr=(e,t)=>Br(e,e,lr(t),Zs()),Zr=(e,t,o)=>Br(e,e,lr(t),o),ea=(e,t)=>Zr(e,t,tr),ta=(e,t)=>Zr(e,t,or),oa=(e,t,o)=>Zr(e,t,Fr(o)),na=(e,t)=>Zr(e,t,nr),sa=(e,t)=>Zr(e,t,sr),ra=(e,t,o)=>Zr(e,t,xr(o)),aa=(e,t,o)=>Zr(e,t,vr(o)),ia=(e,t)=>((e,t)=>({[e]:t}))(e,t),la=e=>(e=>{const t={};return V(e,(e=>{t[e.key]=e.value})),t})(e),ca=y,da=ca("touchstart"),ua=ca("touchmove"),ma=ca("touchend"),ga=ca("touchcancel"),pa=ca("mousedown"),ha=ca("mousemove"),fa=ca("mouseout"),ba=ca("mouseup"),va=ca("mouseover"),xa=ca("focusin"),ya=ca("focusout"),wa=ca("keydown"),Sa=ca("keyup"),Ca=ca("input"),ka=ca("change"),Oa=ca("click"),_a=ca("transitioncancel"),Ta=ca("transitionend"),Ea=ca("transitionstart"),Aa=ca("selectstart"),Ma=e=>y("alloy."+e),Da={tap:Ma("tap")},Ba=Ma("focus"),Ia=Ma("blur.post"),Fa=Ma("paste.post"),Ra=Ma("receive"),Na=Ma("execute"),za=Ma("focus.item"),La=Da.tap,Va=Ma("longpress"),Ha=Ma("sandbox.close"),Pa=Ma("typeahead.cancel"),Ua=Ma("system.init"),Wa=Ma("system.touchmove"),$a=Ma("system.touchend"),Ga=Ma("system.scroll"),ja=Ma("system.resize"),qa=Ma("system.attached"),Xa=Ma("system.detached"),Ya=Ma("system.dismissRequested"),Ka=Ma("system.repositionRequested"),Ja=Ma("focusmanager.shifted"),Qa=Ma("slotcontainer.visibility"),Za=Ma("system.external.element.scroll"),ei=Ma("change.tab"),ti=Ma("dismiss.tab"),oi=Ma("highlight"),ni=Ma("dehighlight"),si=e=>(e=>{if(wo(e))return"#shadow-root";{const t=(e=>Zo(e,!1))(e);return(e=>{const t=it("div"),o=ct(e.dom.cloneNode(!0));return Uo(t,o),en(t)})(t)}})(e);var ri;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(ri||(ri={}));const ai=ye({}),ii=["alloy/data/Fields","alloy/debugging/Debugging"],li=(e,t,o)=>((e,t,o)=>{switch(fe(ai.get(),e).orThunk((()=>{const t=re(ai.get());return se(t,(t=>e.indexOf(t)>-1?A.some(ai.get()[t]):A.none()))})).getOr(ri.NORMAL)){case ri.NORMAL:return o(ci());case ri.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();F(["mousemove","mouseover","mouseout",Ua()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:L(o,(e=>F(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+si(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case ri.STOP:return!0}})(e,t,o),ci=y({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),di=y([Rr("menu"),Rr("selectedMenu")]),ui=y([Rr("item"),Rr("selectedItem")]);y(vr(ui().concat(di())));const mi=y(vr(ui())),gi=Pr("initSize",[Rr("numColumns"),Rr("numRows")]),pi=()=>Pr("markers",[Rr("backgroundMenu")].concat(di()).concat(ui())),hi=e=>Pr("markers",L(e,Rr)),fi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");$(t,(e=>e.indexOf("alloy")>0&&!R(ii,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),Br(t,t,o,kr((e=>Ae.value(((...t)=>e.apply(void 0,t))))))),bi=e=>fi(0,e,lr(b)),vi=e=>fi(0,e,lr(A.none)),xi=e=>fi(0,e,{tag:"required",process:{}}),yi=e=>fi(0,e,{tag:"required",process:{}}),wi=(e,t)=>Ir(e,y(t)),Si=e=>Ir(e,w),Ci=y(gi),ki=e=>L(e,(e=>Ze(e,"/*")?e.substring(0,e.length-2):e)),Oi=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ki(r)}),e},_i=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},Ti=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])};var Ei;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(Ei||(Ei={}));const Ai="placeholder",Mi=xe([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Di=e=>be(e,"uiType"),Bi=(e,t,o,n)=>((e,t,o,n)=>Di(o)&&o.uiType===Ai?((e,t,o,n)=>e.exists((e=>e!==o.owner))?Mi.single(!0,y(o)):fe(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+re(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):Mi.single(!1,y(o)))(e,0,o,n).fold(((s,r)=>{const a=Di(o)?r(t,o.config,o.validated):r(t),i=fe(a,"components").getOr([]),l=q(i,(o=>Bi(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(Di(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),Ii=Mi.single,Fi=Mi.multiple,Ri=y(Ai),Ni=xe([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),zi=Qr("factory",{sketch:w}),Li=Qr("schema",[]),Vi=Rr("name"),Hi=Br("pname","pname",ir((e=>"<alloy."+Ne(e.name)+">")),Zs()),Pi=Ir("schema",(()=>[$r("preprocess")])),Ui=Qr("defaults",y({})),Wi=Qr("overrides",y({})),$i=vr([zi,Li,Vi,Hi,Ui,Wi]),Gi=vr([zi,Li,Vi,Ui,Wi]),ji=vr([zi,Li,Vi,Hi,Ui,Wi]),qi=vr([zi,Pi,Vi,Rr("unit"),Hi,Ui,Wi]),Xi=e=>e.fold(A.some,A.none,A.some,A.some),Yi=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Ki=(e,t)=>o=>{const n=Er("Converting part type",t,o);return e(n)},Ji=Ki(Ni.required,$i),Qi=Ki(Ni.external,Gi),Zi=Ki(Ni.optional,ji),el=Ki(Ni.group,qi),tl=y("entirety");var ol=Object.freeze({__proto__:null,required:Ji,external:Qi,optional:Zi,group:el,asNamedPart:Xi,name:Yi,asCommon:e=>e.fold(w,w,w,w),original:tl});const nl=(e,t,o,n)=>Le(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),sl=(e,t)=>{const o={};return V(t,(t=>{Xi(t).each((t=>{const n=rl(e,t.pname);o[t.name]=o=>{const s=Er("Part: "+t.name+" in "+e,vr(t.schema),o);return{...n,config:o,validated:s}}}))})),o},rl=(e,t)=>({uiType:Ri(),owner:e,name:t}),al=(e,t,o)=>({uiType:Ri(),owner:e,name:t,config:o,validated:{}}),il=e=>q(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>Pr(e.name,e.schema.concat([Si(tl())])))).toArray())),ll=e=>L(e,Yi),cl=(e,t,o)=>((e,t,o)=>{const n={},s={};return V(o,(e=>{e.fold((e=>{n[e.pname]=Ii(!0,((t,o,n)=>e.factory.sketch(nl(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=y(e.factory.sketch(nl(t,e,o[tl()]),o))}),(e=>{n[e.pname]=Ii(!1,((t,o,n)=>e.factory.sketch(nl(t,e,o,n))))}),(e=>{n[e.pname]=Fi(!0,((t,o,n)=>{const s=t[e.name];return L(s,(o=>e.factory.sketch(Le(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:y(n),externals:y(s)}})(0,t,o),dl=(e,t,o)=>((e,t,o,n)=>{const s=le(n,((e,t)=>((e,t)=>{let o=!1;return{name:y(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>q(o,(o=>Bi(e,t,o,n))))(e,t,o,s);return ie(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),ul=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},ml=(e,t,o)=>ul(e,t,o).getOrDie("Could not find part: "+o),gl=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return V(o,(e=>{n[e]=y(r.getByUid(s[e]))})),n},pl=(e,t)=>{const o=e.getSystem();return le(t.partUids,((e,t)=>y(o.getByUid(e))))},hl=e=>re(e.partUids),fl=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return V(o,(e=>{n[e]=y(r.getByUid(s[e]).getOrDie())})),n},bl=(e,t)=>{const o=ll(t);return la(L(o,(t=>({key:t,value:e+"-"+t}))))},vl=e=>Br("partUids","partUids",cr((t=>bl(t.uid,e))),Zs());var xl=Object.freeze({__proto__:null,generate:sl,generateOne:al,schemas:il,names:ll,substitutes:cl,components:dl,defaultUids:bl,defaultUidsSchema:vl,getAllParts:pl,getAllPartNames:hl,getPart:ul,getPartOrDie:ml,getParts:gl,getPartsOrDie:fl});const yl=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],wl=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>fe(o,e).getOr([]),i=(e,t,o)=>{const n=K(yl,o);return{offset:Dn(e,t),classesOn:q(o,a),classesOff:q(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},Sl=()=>wl(0,0,{}),Cl=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),kl=xe([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Ol=kl.southeast,_l=kl.southwest,Tl=kl.northeast,El=kl.northwest,Al=kl.south,Ml=kl.north,Dl=kl.east,Bl=kl.west,Il=(e,t)=>J(["left","right","top","bottom"],(o=>fe(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Fl="layout",Rl=e=>e.x,Nl=(e,t)=>e.x+e.width/2-t.width/2,zl=(e,t)=>e.x+e.width-t.width,Ll=(e,t)=>e.y-t.height,Vl=e=>e.y+e.height,Hl=(e,t)=>e.y+e.height/2-t.height/2,Pl=(e,t,o)=>Cl(Rl(e),Vl(e),o.southeast(),Ol(),"southeast",Il(e,{left:1,top:3}),Fl),Ul=(e,t,o)=>Cl(zl(e,t),Vl(e),o.southwest(),_l(),"southwest",Il(e,{right:0,top:3}),Fl),Wl=(e,t,o)=>Cl(Rl(e),Ll(e,t),o.northeast(),Tl(),"northeast",Il(e,{left:1,bottom:2}),Fl),$l=(e,t,o)=>Cl(zl(e,t),Ll(e,t),o.northwest(),El(),"northwest",Il(e,{right:0,bottom:2}),Fl),Gl=(e,t,o)=>Cl(Nl(e,t),Ll(e,t),o.north(),Ml(),"north",Il(e,{bottom:2}),Fl),jl=(e,t,o)=>Cl(Nl(e,t),Vl(e),o.south(),Al(),"south",Il(e,{top:3}),Fl),ql=(e,t,o)=>Cl((e=>e.x+e.width)(e),Hl(e,t),o.east(),Dl(),"east",Il(e,{left:0}),Fl),Xl=(e,t,o)=>Cl(((e,t)=>e.x-t.width)(e,t),Hl(e,t),o.west(),Bl(),"west",Il(e,{right:1}),Fl),Yl=()=>[Pl,Ul,Wl,$l,jl,Gl,ql,Xl],Kl=()=>[Ul,Pl,$l,Wl,jl,Gl,ql,Xl],Jl=()=>[Wl,$l,Pl,Ul,Gl,jl],Ql=()=>[$l,Wl,Ul,Pl,Gl,jl],Zl=()=>[Pl,Ul,Wl,$l,jl,Gl],ec=()=>[Ul,Pl,$l,Wl,jl,Gl],tc="data-alloy-placement",oc=e=>Xo(e,tc),nc="layout-inset",sc=e=>e.x,rc=(e,t)=>e.x+e.width/2-t.width/2,ac=(e,t)=>e.x+e.width-t.width,ic=e=>e.y,lc=(e,t)=>e.y+e.height-t.height,cc=(e,t)=>e.y+e.height/2-t.height/2,dc=(e,t,o)=>Cl(ac(e,t),lc(e,t),o.insetSouthwest(),El(),"southwest",Il(e,{right:0,bottom:3}),nc),uc=(e,t,o)=>Cl(sc(e),lc(e,t),o.insetSoutheast(),Tl(),"southeast",Il(e,{left:1,bottom:3}),nc),mc=(e,t,o)=>Cl(ac(e,t),ic(e),o.insetNorthwest(),_l(),"northwest",Il(e,{right:0,top:2}),nc),gc=(e,t,o)=>Cl(sc(e),ic(e),o.insetNortheast(),Ol(),"northeast",Il(e,{left:1,top:2}),nc),pc=(e,t,o)=>Cl(rc(e,t),ic(e),o.insetNorth(),Al(),"north",Il(e,{top:2}),nc),hc=(e,t,o)=>Cl(rc(e,t),lc(e,t),o.insetSouth(),Ml(),"south",Il(e,{bottom:3}),nc),fc=(e,t,o)=>Cl(ac(e,t),cc(e,t),o.insetEast(),Bl(),"east",Il(e,{right:0}),nc),bc=(e,t,o)=>Cl(sc(e),cc(e,t),o.insetWest(),Dl(),"west",Il(e,{left:1}),nc),vc=e=>{switch(e){case"north":return pc;case"northeast":return gc;case"northwest":return mc;case"south":return hc;case"southeast":return uc;case"southwest":return dc;case"east":return fc;case"west":return bc}},xc=(e,t,o,n,s)=>oc(n).map(vc).getOr(pc)(e,t,o,n,s),yc=e=>{switch(e){case"north":return hc;case"northeast":return uc;case"northwest":return dc;case"south":return pc;case"southeast":return gc;case"southwest":return mc;case"east":return bc;case"west":return fc}},wc=(e,t,o,n,s)=>oc(n).map(yc).getOr(pc)(e,t,o,n,s),Sc=(e,t)=>{((e,t)=>{const o=wn.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);dn(e,"max-height",o+"px")})(e,Math.floor(t))},Cc=y(((e,t)=>{Sc(e,t),un(e,{"overflow-x":"hidden","overflow-y":"auto"})})),kc=y(((e,t)=>{Sc(e,t)})),Oc=y(((e,t)=>{((e,t)=>{const o=On.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);dn(e,"max-width",o+"px")})(e,Math.floor(t))}));var _c;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(_c||(_c={}));const Tc="data-alloy-vertical-dir",Ec=e=>gs(e,(e=>no(e)&&qo(e,"data-alloy-vertical-dir")===_c.BottomToTop));var Ac;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(Ac||(Ac={}));const Mc={init:()=>Dc({readState:y("No State required")})},Dc=e=>e,Bc={can:E,abort:T,run:b},Ic=e=>{if(!ve(e,"can")&&!ve(e,"abort")&&!ve(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Bc,...e}},Fc=(e,t)=>{Lc(e,e.element,t,{})},Rc=(e,t,o)=>{Lc(e,e.element,t,o)},Nc=e=>{Fc(e,Na())},zc=(e,t,o)=>{Lc(e,t,o,{})},Lc=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Vc=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Hc=e=>la(e),Pc=(e,t)=>({key:e,value:Ic({abort:t})}),Uc=e=>({key:e,value:Ic({run:(e,t)=>{t.event.prevent()}})}),Wc=(e,t)=>({key:e,value:Ic({run:t})}),$c=(e,t,o)=>({key:e,value:Ic({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),Gc=e=>t=>({key:e,value:Ic({run:(e,o)=>{Vs(e,o)&&t(e,o)}})}),jc=(e,t,o)=>((e,t)=>Wc(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Vc(t,t.element,e,n)}))})))(e,t.partUids[o]),qc=(e,t)=>Wc(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>bs(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),Xc=e=>Wc(e,((e,t)=>{t.cut()})),Yc=e=>Wc(e,((e,t)=>{t.stop()})),Kc=(e,t)=>Gc(e)(t),Jc=Gc(qa()),Qc=Gc(Xa()),Zc=Gc(Ua()),ed=(e=>t=>Wc(e,t))(Na()),td=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),od=(e,t,o)=>Zc(((n,s)=>{o(n,e,t)})),nd=e=>({key:e,value:void 0}),sd=(e,t,o,n,s,r,a)=>{const i=e=>ve(e,o)?e[o]():A.none(),l=le(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ki(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:y(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...le(r,((e,t)=>Oi(e,t))),...l,revoke:C(nd,o),config:t=>{const n=Er(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:st((()=>Er(o+"-config",e,t))),initialConfig:t,state:a}}},schema:y(t),exhibit:(e,t)=>Ue(i(e),fe(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>td({}))),name:y(o),handlers:e=>i(e).map((e=>fe(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},rd=e=>la(e),ad=br([Rr("fields"),Rr("name"),Qr("active",{}),Qr("apis",{}),Qr("state",Mc),Qr("extra",{})]),id=e=>{const t=Er("Creating behaviour: "+e.name,ad,e);return((e,t,o,n,s,r)=>{const a=br(e),i=Jr(t,[(l=e,Gr("config",br(l)))]);var l;return sd(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},ld=br([Rr("branchKey"),Rr("branches"),Rr("name"),Qr("active",{}),Qr("apis",{}),Qr("state",Mc),Qr("extra",{})]),cd=e=>{const t=Er("Creating behaviour: "+e.name,ld,e);return((e,t,o,n,s,r)=>{const a=e,i=Jr(t,[Gr("config",e)]);return sd(a,i,t,o,n,s,r)})(Mr(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},dd=y(void 0),ud=(e,t)=>{const o=((e,t)=>{const o=Hc(t);return id({fields:[Rr("enabled")],name:e,active:{events:y(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:y({}),initialConfig:{},state:Mc}}},md=(e,t)=>{t.ignore||(Fo(e.element),t.onFocus(e))};var gd=Object.freeze({__proto__:null,focus:md,blur:(e,t)=>{t.ignore||Ro(e.element)},isFocused:e=>No(e.element)}),pd=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return td(o)},events:e=>Hc([Wc(Ba(),((t,o)=>{md(t,e),o.stop()}))].concat(e.stopMousedown?[Wc(pa(),((e,t)=>{t.event.prevent()}))]:[]))}),hd=[bi("onFocus"),Qr("stopMousedown",!1),Qr("ignore",!1)];const fd=id({fields:hd,name:"focusing",active:pd,apis:gd}),bd=[8],vd=[9],xd=[13],yd=[27],wd=[32],Sd=[37],Cd=[38],kd=[39],Od=[40],_d=(e,t,o)=>{const n=Y(e.slice(0,t)),s=Y(e.slice(t+1));return $(n.concat(s),o)},Td=(e,t,o)=>{const n=Y(e.slice(0,t));return $(n,o)},Ed=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return $(s.concat(n),o)},Ad=(e,t,o)=>{const n=e.slice(t+1);return $(n,o)},Md=e=>t=>{const o=t.raw;return F(e,o.which)},Dd=e=>t=>X(e,(e=>e(t))),Bd=e=>!0===e.raw.shiftKey,Id=e=>!0===e.raw.ctrlKey,Fd=k(Bd),Rd=(e,t)=>({matches:e,classification:t}),Nd=(e,t,o,n)=>{const s=ms(e.element,"."+t.highlightClass);V(s,(o=>{R(n,(e=>yt(e.element,o)))||(ss(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),Fc(o,ni())})))}))},zd=(e,t,o,n)=>{Nd(e,t,0,[n]),Ld(e,t,o,n)||(os(n.element,t.highlightClass),t.onHighlight(e,n),Fc(n,oi()))},Ld=(e,t,o,n)=>rs(n.element,t.highlightClass),Vd=(e,t,o)=>Xn(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),Hd=(e,t,o)=>{const n=ms(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Pd=(e,t,o,n)=>{const s=ms(e.element,"."+t.itemClass);return G(s,(e=>rs(e,t.highlightClass))).bind((t=>{const o=Ie(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},Ud=(e,t,o)=>{const n=ms(e.element,"."+t.itemClass);return Pe(L(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Wd=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>Nd(e,t,0,[]),dehighlight:(e,t,o,n)=>{Ld(e,t,o,n)&&(ss(n.element,t.highlightClass),t.onDehighlight(e,n),Fc(n,ni()))},highlight:zd,highlightFirst:(e,t,o)=>{Vd(e,t).each((n=>{zd(e,t,o,n)}))},highlightLast:(e,t,o)=>{Hd(e,t).each((n=>{zd(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=ms(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>Ae.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{zd(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=Ud(e,t);$(s,n).each((n=>{zd(e,t,o,n)}))},isHighlighted:Ld,getHighlighted:(e,t,o)=>Xn(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:Vd,getLast:Hd,getPrevious:(e,t,o)=>Pd(e,t,0,-1),getNext:(e,t,o)=>Pd(e,t,0,1),getCandidates:Ud}),$d=[Rr("highlightClass"),Rr("itemClass"),bi("onHighlight"),bi("onDehighlight")];const Gd=id({fields:$d,name:"highlighting",apis:Wd}),jd=(e,t,o)=>{t.exists((e=>o.exists((t=>yt(t,e)))))||Rc(e,Ja(),{prevFocus:t,newFocus:o})},qd=()=>{const e=e=>Lo(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);jd(t,n,s)}}},Xd=()=>{const e=e=>Gd.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Gd.highlight(t,e)}));const s=e(t);jd(t,n,s)}}},Yd=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,$(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([Qr("focusManager",qd()),Zr("focusInside","onFocus",kr((e=>F(["onFocus","onEnterOrSpace","onApi"],e)?Ae.value(e):Ae.error("Invalid value for focusInside")))),wi("handler",a),wi("state",t),wi("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==Ei.OnFocusMode?A.none():s(e).map((o=>Wc(Ba(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Wc(wa(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=Md(wd.concat(xd))(n.event);e.focusInside===Ei.OnEnterOrSpaceMode&&r&&Vs(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Wc(Sa(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Hc(a.toArray().concat(i))}};return a},Kd=e=>{const t=[$r("onEscape"),$r("onEnter"),Qr("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),Qr("firstTabstop",0),Qr("useTabstopAt",E),$r("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>Yn(t,e))).getOr(t);return Sn(o)>0},n=(e,t)=>t.focusManager.get(e).bind((e=>Yn(e,t.selector))),s=(e,t,n)=>{((e,t)=>{const n=ms(e.element,t.selector),s=P(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,s,r)=>{const a=P(ms(e.element,s.selector),(e=>o(s,e)));return n(e,s).bind((t=>G(a,C(yt,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,a,t,s,r)))))},a=(e,t,o)=>{const n=o.cyclic?_d:Td;return r(e,0,o,n)},i=(e,t,o)=>{const n=o.cyclic?Ed:Ad;return r(e,0,o,n)},l=y([Rd(Dd([Bd,Md(vd)]),a),Rd(Md(vd),i),Rd(Dd([Fd,Md(xd)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),c=y([Rd(Md(yd),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),Rd(Md(vd),((e,t,o)=>n(e,o).filter((e=>!o.useTabstopAt(e))).bind((n=>((e=>(e=>go(e))(e).bind(vo).exists((t=>yt(t,e))))(n)?a:i)(e,t,o)))))]);return Yd(t,Mc.init,l,c,(()=>A.some(s)))};var Jd=Kd(Ir("cyclic",T)),Qd=Kd(Ir("cyclic",E));const Zd=e=>io("input")(e)&&"radio"!==qo(e,"type")||io("textarea")(e),eu=(e,t,o)=>Zd(o)&&Md(wd)(t.event)?A.none():((e,t,o)=>(zc(e,o,Na()),A.some(!0)))(e,0,o),tu=(e,t)=>A.some(!0),ou=[Qr("execute",eu),Qr("useSpace",!1),Qr("useEnter",!0),Qr("useControlEnter",!1),Qr("useDown",!1)],nu=(e,t,o)=>o.execute(e,t,e.element);var su=Yd(ou,Mc.init,((e,t,o,n)=>{const s=o.useSpace&&!Zd(e.element)?wd:[],r=o.useEnter?xd:[],a=o.useDown?Od:[],i=s.concat(r).concat(a);return[Rd(Md(i),nu)].concat(o.useControlEnter?[Rd(Dd([Id,Md(xd)]),nu)]:[])}),((e,t,o,n)=>o.useSpace&&!Zd(e.element)?[Rd(Md(wd),tu)]:[]),(()=>A.none()));const ru=()=>{const e=Xe();return Dc({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var au=Object.freeze({__proto__:null,flatgrid:ru,init:e=>e.state(e)});const iu=e=>(t,o,n,s)=>{const r=e(t.element);return uu(r,t,o,n,s)},lu=(e,t)=>{const o=Ln(e,t);return iu(o)},cu=(e,t)=>{const o=Ln(t,e);return iu(o)},du=e=>(t,o,n,s)=>uu(e,t,o,n,s),uu=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),mu=du,gu=du,pu=du,hu=(e,t,o)=>{const n=ms(e,o);return(e=>G(e,(e=>yt(e,t))).map((t=>({index:t,candidates:e}))))(P(n,kn))},fu=(e,t)=>G(e,(e=>yt(t,e))),bu=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),vu=(e,t,o,n,s)=>bu(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Ie(r,s,0,a-1);return A.some({row:t,column:i})})),xu=(e,t,o,n,s)=>bu(e,t,n,((t,r)=>{const a=Ie(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Fe(r,0,i-1);return A.some({row:a,column:l})})),yu=[Rr("selector"),Qr("execute",eu),vi("onEscape"),Qr("captureTab",!1),Ci()],wu=(e,t,o)=>{Xn(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Su=e=>(t,o,n,s)=>hu(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Cu=(e,t,o)=>o.captureTab?A.some(!0):A.none(),ku=Su(((e,t,o,n)=>vu(e,t,o,n,-1))),Ou=Su(((e,t,o,n)=>vu(e,t,o,n,1))),_u=Su(((e,t,o,n)=>xu(e,t,o,n,-1))),Tu=Su(((e,t,o,n)=>xu(e,t,o,n,1))),Eu=y([Rd(Md(Sd),lu(ku,Ou)),Rd(Md(kd),cu(ku,Ou)),Rd(Md(Cd),mu(_u)),Rd(Md(Od),gu(Tu)),Rd(Dd([Bd,Md(vd)]),Cu),Rd(Dd([Fd,Md(vd)]),Cu),Rd(Md(wd.concat(xd)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>Yn(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Au=y([Rd(Md(yd),((e,t,o)=>o.onEscape(e,t))),Rd(Md(wd),tu)]);var Mu=Yd(yu,ru,Eu,Au,(()=>A.some(wu)));const Du=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===eo(n)&&"disabled"===qo(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return hu(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},Bu=(e,t,o,n)=>Du(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Fe(t+o,n,s);return i===e?A.from(r):a(i)})),Iu=(e,t,o,n)=>Du(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Ie(t,o,n,s);return i===e?A.none():a(i)})),Fu=[Rr("selector"),Qr("getInitial",A.none),Qr("execute",eu),vi("onEscape"),Qr("executeOnMove",!1),Qr("allowVertical",!0),Qr("allowHorizontal",!0),Qr("cycles",!0)],Ru=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>Yn(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),Nu=(e,t,o)=>{t.getInitial(e).orThunk((()=>Xn(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},zu=(e,t,o)=>(o.cycles?Iu:Bu)(e,o.selector,t,-1),Lu=(e,t,o)=>(o.cycles?Iu:Bu)(e,o.selector,t,1),Vu=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?Ru(t,o,n):A.some(!0))),Hu=y([Rd(Md(wd),tu),Rd(Md(yd),((e,t,o)=>o.onEscape(e,t)))]);var Pu=Yd(Fu,Mc.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?Sd:[]].concat(o.allowVertical?Cd:[]),r=[...o.allowHorizontal?kd:[]].concat(o.allowVertical?Od:[]);return[Rd(Md(s),Vu(lu(zu,Lu))),Rd(Md(r),Vu(cu(zu,Lu))),Rd(Md(xd),Ru),Rd(Md(wd),Ru)]}),Hu,(()=>A.some(Nu)));const Uu=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),Wu=(e,t,o,n)=>{const s=e[t].length,r=Ie(o,n,0,s-1);return Uu(e,t,r)},$u=(e,t,o,n)=>{const s=Ie(o,n,0,e.length-1),r=e[s].length,a=Fe(t,0,r-1);return Uu(e,s,a)},Gu=(e,t,o,n)=>{const s=e[t].length,r=Fe(o+n,0,s-1);return Uu(e,t,r)},ju=(e,t,o,n)=>{const s=Fe(o+n,0,e.length-1),r=e[s].length,a=Fe(t,0,r-1);return Uu(e,s,a)},qu=[Pr("selectors",[Rr("row"),Rr("cell")]),Qr("cycles",!0),Qr("previousSelector",A.none),Qr("execute",eu)],Xu=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return Xn(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},Yu=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return Yn(n,s.selectors.row).bind((e=>{const t=ms(e,s.selectors.cell);return fu(t,n).bind((t=>{const n=ms(o,s.selectors.row);return fu(n,e).bind((e=>{const o=((e,t)=>L(e,(e=>ms(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},Ku=Yu(((e,t,o)=>Wu(e,t,o,-1)),((e,t,o)=>Gu(e,t,o,-1))),Ju=Yu(((e,t,o)=>Wu(e,t,o,1)),((e,t,o)=>Gu(e,t,o,1))),Qu=Yu(((e,t,o)=>$u(e,o,t,-1)),((e,t,o)=>ju(e,o,t,-1))),Zu=Yu(((e,t,o)=>$u(e,o,t,1)),((e,t,o)=>ju(e,o,t,1))),em=y([Rd(Md(Sd),lu(Ku,Ju)),Rd(Md(kd),cu(Ku,Ju)),Rd(Md(Cd),mu(Qu)),Rd(Md(Od),gu(Zu)),Rd(Md(wd.concat(xd)),((e,t,o)=>Lo(e.element).bind((n=>o.execute(e,t,n)))))]),tm=y([Rd(Md(wd),tu)]);var om=Yd(qu,Mc.init,em,tm,(()=>A.some(Xu)));const nm=[Rr("selector"),Qr("execute",eu),Qr("moveOnTab",!1)],sm=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),rm=(e,t,o)=>{Xn(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},am=(e,t,o)=>Iu(e,o.selector,t,-1),im=(e,t,o)=>Iu(e,o.selector,t,1),lm=y([Rd(Md(Cd),pu(am)),Rd(Md(Od),pu(im)),Rd(Dd([Bd,Md(vd)]),((e,t,o,n)=>o.moveOnTab?pu(am)(e,t,o,n):A.none())),Rd(Dd([Fd,Md(vd)]),((e,t,o,n)=>o.moveOnTab?pu(im)(e,t,o,n):A.none())),Rd(Md(xd),sm),Rd(Md(wd),sm)]),cm=y([Rd(Md(wd),tu)]);var dm=Yd(nm,Mc.init,lm,cm,(()=>A.some(rm)));const um=[vi("onSpace"),vi("onEnter"),vi("onShiftEnter"),vi("onLeft"),vi("onRight"),vi("onTab"),vi("onShiftTab"),vi("onUp"),vi("onDown"),vi("onEscape"),Qr("stopSpaceKeyup",!1),$r("focusIn")];var mm=Yd(um,Mc.init,((e,t,o)=>[Rd(Md(wd),o.onSpace),Rd(Dd([Fd,Md(xd)]),o.onEnter),Rd(Dd([Bd,Md(xd)]),o.onShiftEnter),Rd(Dd([Bd,Md(vd)]),o.onShiftTab),Rd(Dd([Fd,Md(vd)]),o.onTab),Rd(Md(Cd),o.onUp),Rd(Md(Od),o.onDown),Rd(Md(Sd),o.onLeft),Rd(Md(kd),o.onRight),Rd(Md(wd),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[Rd(Md(wd),tu)]:[],Rd(Md(yd),o.onEscape)]),(e=>e.focusIn));const gm=Jd.schema(),pm=Qd.schema(),hm=Pu.schema(),fm=Mu.schema(),bm=om.schema(),vm=su.schema(),xm=dm.schema(),ym=mm.schema(),wm=cd({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:gm,cyclic:pm,flow:hm,flatgrid:fm,matrix:bm,execution:vm,menu:xm,special:ym}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ve(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:au}),Sm=Ne("alloy-premade"),Cm=e=>(Object.defineProperty(e.element.dom,Sm,{value:e.uid,writable:!0}),ia(Sm,e)),km=e=>fe(e,Sm),Om=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:ki(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),_m=e=>e.getSystem().isConnected(),Tm=e=>{Fc(e,Xa());const t=e.components();V(t,Tm)},Em=e=>{const t=e.components();V(t,Em),Fc(e,qa())},Am=(e,t)=>{e.getSystem().addToWorld(t),sn(e.element)&&Em(t)},Mm=e=>{Tm(e),e.getSystem().removeFromWorld(e)},Dm=(e,t)=>{Uo(e.element,t.element)},Bm=(e,t)=>{Im(e,t,Uo)},Im=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),sn(e.element)&&Em(t),e.syncComponents()},Fm=e=>{Tm(e),Qo(e.element),e.getSystem().removeFromWorld(e)},Rm=e=>{const t=go(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));Fm(e),t.each((e=>{e.syncComponents()}))},Nm=e=>{const t=e.components();V(t,Fm),Jo(e.element),e.syncComponents()},zm=(e,t)=>{Vm(e,t,Uo)},Lm=(e,t)=>{Vm(e,t,Ho)},Vm=(e,t,o)=>{o(e,t.element);const n=fo(t.element);V(n,(e=>{t.getByDom(e).each(Em)}))},Hm=e=>{const t=fo(e.element);V(t,(t=>{e.getByDom(t).each(Tm)})),Qo(e.element)},Pm=(e,t,o)=>{o.fold((()=>Uo(e,t)),(e=>{yt(e,t)||(Vo(e,t),Qo(e))}))},Um=(e,t,o)=>{const n=L(t,o),s=fo(e);return V(s.slice(n.length),Qo),n},Wm=(e,t,o,n)=>{const s=bo(e,t),r=n(o,s),a=((e,t,o)=>bo(e,t).map((e=>{if(o.exists((t=>!yt(t,e)))){const t=o.map(eo).getOr("span"),n=it(t);return Vo(e,n),n}return e})))(e,t,s);return Pm(e,r.element,a),r},$m=(e,t)=>{const o=So(t),n=zo(o).bind((e=>{const o=t=>yt(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=ct(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{zo(o).filter((t=>yt(t,e))).fold((()=>{Fo(e)}),b)})),s},Gm=(e,t)=>{$m((()=>{((e,t,o)=>{const n=e.components();(e=>{V(e.components(),(e=>Qo(e.element))),Jo(e.element),e.syncComponents()})(e);const s=o(t),r=K(n,s);V(r,(t=>{Tm(t),e.getSystem().removeFromWorld(t)})),V(s,(t=>{_m(t)?Dm(e,t):(e.getSystem().addToWorld(t),Dm(e,t),sn(e.element)&&Em(t))})),e.syncComponents()})(e,t,(()=>L(t,e.getSystem().build)))}),e.element)},jm=(e,t)=>{$m((()=>{((o,n)=>{const s=o.components(),r=q(n,(e=>km(e).toArray()));V(s,(e=>{F(r,e)||Mm(e)}));const a=((e,t,o)=>Um(e,t,((t,n)=>Wm(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),i=K(s,a);V(i,(e=>{_m(e)&&Mm(e)})),V(a,(e=>{_m(e)||Am(o,e)})),o.syncComponents()})(e,t)}),e.element)},qm=(e,t,o,n)=>{Mm(t);const s=Wm(e.element,o,n,e.getSystem().buildOrPatch);Am(e,s),e.syncComponents()},Xm=(e,t,o)=>{const n=e.getSystem().build(o);Im(e,n,t)},Ym=(e,t,o,n)=>{Rm(t),Xm(e,((e,t)=>((e,t,o)=>{bo(e,o).fold((()=>{Uo(e,t)}),(e=>{Vo(e,t)}))})(e,t,o)),n)},Km=(e,t)=>e.components(),Jm=(e,t,o,n,s)=>{const r=Km(e);return A.from(r[n]).map((o=>(s.fold((()=>Rm(o)),(s=>{(t.reuseDom?qm:Ym)(e,o,n,s)})),o)))};var Qm=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Xm(e,Uo,n)},prepend:(e,t,o,n)=>{Xm(e,Po,n)},remove:(e,t,o,n)=>{const s=Km(e),r=$(s,(e=>yt(n.element,e.element)));r.each(Rm)},replaceAt:Jm,replaceBy:(e,t,o,n,s)=>{const r=Km(e);return G(r,n).bind((o=>Jm(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?jm:Gm)(e,n),contents:Km});const Zm=id({fields:[na("reuseDom",!0)],name:"replacing",apis:Qm}),eg=Hc([(e=>({key:e,value:Ic({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>yt(t,e.element)&&!yt(t,o))(e,n,s)||(console.warn(Ba()+" did not get interpreted by the desired target. \nOriginator: "+si(n)+"\nTarget: "+si(s)+"\nCheck the "+Ba()+" event handlers"),!1)}})}))(Ba())]);var tg=Object.freeze({__proto__:null,events:eg});const og=y("alloy-id-"),ng=y("data-alloy-id"),sg=og(),rg=ng(),ag=(e,t)=>{Object.defineProperty(e.dom,rg,{value:t,writable:!0})},ig=e=>{const t=no(e)?e.dom[rg]:null;return A.from(t)},lg=e=>Ne(e),cg=w,dg=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+si(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},ug=dg(),mg=(e,t)=>{const o={};return ie(e,((e,n)=>{ie(e,((e,s)=>{const r=fe(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},gg=e=>e.cHandler,pg=(e,t)=>({name:e,handler:t}),hg=(e,t)=>{const o={};return V(e,(e=>{o[e.name()]=e.handlers(t)})),o},fg=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=Z(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return Ae.value(s)}catch(e){return Ae.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=(e=>(...t)=>W(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=(e=>(...t)=>W(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{V(e,(e=>{e.run.apply(void 0,t)}))}}})(L(e,(e=>e.handler))))):((e,t)=>Ae.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(L(t,(e=>e.name)),null,2)]))(o,e)},bg=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return V(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,Ae.error(j(n))):((e,t)=>0===e.length?Ae.value(t):Ae.value(Le(t,Ve.apply(void 0,e))))(o.values,t);var n})(ge(e,((e,o)=>(1===e.length?Ae.value(e[0].handler):fg(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?P(t[o],(t=>R(e,(e=>e.name===t)))).join(" > "):e[0].name;return ia(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),vg="alloy.base.behaviour",xg=vr([Br("dom","dom",{tag:"required",process:{}},vr([Rr("tag"),Qr("styles",{}),Qr("classes",[]),Qr("attributes",{}),$r("value"),$r("innerHtml")])),Rr("components"),Rr("uid"),Qr("events",{}),Qr("apis",{}),Br("eventOrder","eventOrder",(Dg={[Na()]:["disabling",vg,"toggling","typeaheadevents"],[Ba()]:[vg,"focusing","keying"],[Ua()]:[vg,"disabling","toggling","representing","tooltipping"],[Ca()]:[vg,"representing","streaming","invalidating"],[Xa()]:[vg,"representing","item-events","toolbar-button-events","tooltipping"],[pa()]:["focusing",vg,"item-type-events"],[da()]:["focusing",vg,"item-type-events"],[va()]:["item-type-events","tooltipping"],[Ra()]:["receiving","reflecting","tooltipping"]},cr(y(Dg))),Zs()),$r("domModification")]),yg=e=>e.events,wg=(e,t)=>{const o=re(e),n=re(t),s=K(n,o),r=(e=>{const o={},n={};return ue(e,((e,o)=>!be(t,o)||e!==t[o]),de(o),de(n)),{t:o,f:n}})(e).t;return{toRemove:s,toSet:r}},Sg=(e,t)=>{const o=t.filter((t=>eo(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>be(e.dom,Sm))(t))).bind((t=>((e,t)=>{try{const o=((e,t)=>{const{class:o,style:n,...s}=(e=>W(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=wg(e.attributes,s),i=fn(t),{toSet:l,toRemove:c}=wg(e.styles,i),d=ls(t),u=K(d,e.classes),m=K(e.classes,d);return V(a,(e=>Ko(t,e))),jo(t,r),as(t,m),is(t,u),V(c,(e=>vn(t,e))),un(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{Um(e,t,((t,o)=>{const n=bo(e,o);return Pm(e,t,n),t}))})(t,o)}),(e=>{tn(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==cs(o)&&ds(o,null!=n?n:"")})(),t})(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=it(e.tag);jo(t,e.attributes),as(t,e.classes),un(t,e.styles),e.innerHtml.each((e=>tn(t,e)));const o=e.domChildren;return Wo(t,o),e.value.each((e=>{ds(t,e)})),t})(e)));return ag(o,e.uid),o},Cg=e=>{const t=(e=>{const t=fe(e,"behaviours").getOr({});return q(re(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=L(t,(e=>Jr(e.name(),[Rr("config"),Qr("state",Mc)]))),n=_r("component.behaviours",vr(o),e.behaviours).fold((t=>{throw new Error(Ar(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:le(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return y(t)}))}})(e,t))(e,t)},kg=(e,t)=>{const o=()=>m,n=ye(ug),s=Tr((e=>_r("custom.definition",xg,e))(e)),r=Cg(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:L(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>td({})),td))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};V(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=mg(s,((e,t)=>({name:e,modification:t}))),a=e=>U(e,((e,t)=>({...t.modification,...e})),{}),i=U(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return td({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=Sg(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":yg(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...hg(t,e)};return mg(n,pg)})(e,o,n);return bg(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=ye(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(dg(o))},element:c,syncComponents:()=>{const e=fo(c),t=q(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},Og=e=>{const t=lt(e);return _g({element:t})},_g=e=>{const t=Er("external.component",br([Rr("element"),$r("uid")]),e),o=ye(dg()),n=t.uid.getOrThunk((()=>lg("external")));ag(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(dg((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:y("No state"),syncComponents:b,components:y([]),events:{}};return Cm(s)},Tg=lg,Eg=(e,t)=>km(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=cg(e),s=((e,t)=>{const o=fe(e,"components").getOr([]);return t.fold((()=>L(o,Ag)),(e=>L(o,((t,o)=>Eg(t,bo(e,o))))))})(n,t),r={...n,events:{...tg,...o},components:s};return Ae.value(kg(r,t))})((e=>be(e,"uid"))(e)?e:{uid:Tg(""),...e},t).getOrDie())),Ag=e=>Eg(e,A.none()),Mg=Cm;var Dg,Bg=Object.freeze({__proto__:null,block:(e,t,o,n)=>{Go(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=rd([wm.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),fd.config({})]),a=n(s,r),i=s.getSystem().build(a);Zm.append(s,Mg(i)),i.hasConfigured(wm)&&t.focus&&wm.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Zm.remove(s,i)))},unblock:(e,t,o)=>{Ko(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()},isBlocked:(e,t,o)=>o.isBlocked()}),Ig=[sa("getRoot",A.none),na("focus",!0),bi("onBlock"),bi("onUnblock")];const Fg=id({fields:Ig,name:"blocking",apis:Bg,state:Object.freeze({__proto__:null,init:()=>{const e=je((e=>e.destroy()));return Dc({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})});var Rg=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const Ng=[Rr("find")],zg=id({fields:Ng,name:"composing",apis:Rg});var Lg=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),Vg=[Nr("others",Or(Ae.value,Zs()))],Hg=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===re(t.others).length)throw new Error("Cannot find any known coupled components");return fe(e,o)},o=y({});return Dc({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(fe(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=fe(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const Pg=id({fields:Vg,name:"coupling",apis:Lg,state:Hg}),Ug=["input","button","textarea","select"],Wg=(e,t,o)=>{(t.disabled()?Yg:Kg)(e,t)},$g=(e,t)=>!0===t.useNative&&F(Ug,eo(e.element)),Gg=e=>{Go(e.element,"disabled","disabled")},jg=e=>{Ko(e.element,"disabled")},qg=e=>{Go(e.element,"aria-disabled","true")},Xg=e=>{Go(e.element,"aria-disabled","false")},Yg=(e,t,o)=>{t.disableClass.each((t=>{os(e.element,t)})),($g(e,t)?Gg:qg)(e),t.onDisabled(e)},Kg=(e,t,o)=>{t.disableClass.each((t=>{ss(e.element,t)})),($g(e,t)?jg:Xg)(e),t.onEnabled(e)},Jg=(e,t)=>$g(e,t)?(e=>Yo(e.element,"disabled"))(e):(e=>"true"===qo(e.element,"aria-disabled"))(e);var Qg=Object.freeze({__proto__:null,enable:Kg,disable:Yg,isDisabled:Jg,onLoad:Wg,set:(e,t,o,n)=>{(n?Yg:Kg)(e,t)}}),Zg=Object.freeze({__proto__:null,exhibit:(e,t)=>td({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Hc([Pc(Na(),((t,o)=>Jg(t,e))),od(e,t,Wg)])}),ep=[sa("disabled",T),Qr("useNative",!0),$r("disableClass"),bi("onDisabled"),bi("onEnabled")];const tp=id({fields:ep,name:"disabling",active:Zg,apis:Qg}),op=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},np=(e,t)=>{mn(e,(e=>({...e,position:A.some(e.position)}))(t))},sp=(e,t)=>{const o=e.element;os(o,t.transitionClass),ss(o,t.fadeOutClass),os(o,t.fadeInClass),t.onShow(e)},rp=(e,t)=>{const o=e.element;os(o,t.transitionClass),ss(o,t.fadeInClass),os(o,t.fadeOutClass),t.onHide(e)},ap=(e,t)=>e.y>=t.y,ip=(e,t)=>e.bottom<=t.bottom,lp=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),cp=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),dp=e=>e.box.x-e.win.x,up=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(y(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return Dn(e.bounds.x,o)})(o,t);return{box:Fs(n.left,n.top,Tn(e),Sn(e)),location:o.location}})),mp=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(y(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return Dn(e.x,o)})(t,o),a=Fs(r.left,r.top,t.width,t.height);n.setInitialPos({style:fn(e),position:gn(e,"position")||"static",bounds:a,location:s.location})},gp=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=Hs(e).getOr(rn()),r=Rs(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:op("absolute",fe(n.style,"left").map((e=>t.x-r.x)),fe(n.style,"top").map((e=>t.y-r.y+a)),fe(n.style,"right").map((e=>r.right-t.right)),fe(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),pp=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:op("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:op("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},hp=(e,t,o)=>{const n=e.element;return He(hn(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>up(e,t,o).filter((({box:e})=>((e,t,o)=>X(e,(e=>{switch(e){case"bottom":return ip(t,o.bounds);case"top":return ap(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>gp(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>up(e,t,o))).bind((({box:e,location:o})=>{const n=Ls(),s=dp({win:n,box:e}),r="top"===o?lp(n,s,t):cp(n,s,t);return pp(r)})))))(n,t,o):((e,t,o)=>{const n=Rs(e),s=Ls(),r=((e,t,o)=>{const n=t.win,s=t.box,r=dp(t);return se(e,(e=>{switch(e){case"bottom":return ip(s,o.bounds)?A.none():A.some(cp(n,r,o));case"top":return ap(s,o.bounds)?A.none():A.some(lp(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(mp(e,n,t,o,r),pp(r)):A.none()})(n,t,o)},fp=(e,t,o)=>{o.setDocked(!1),V(["left","right","top","bottom","position"],(t=>vn(e.element,t))),t.onUndocked(e)},bp=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),np(e.element,n),(s?t.onDocked:t.onUndocked)(e)},vp=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(as(e.element,[t.fadeOutClass]),t.onHide(e)):(a?sp:rp)(e,t))}))}))},xp=(e,t,o,n,s)=>{vp(e,t,o,n,!0),bp(e,t,o,s.positionCss)},yp=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);vp(e,t,o,n),hp(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return fp(e,t,o);case"absolute":return bp(e,t,o,s.positionCss);case"fixed":xp(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},wp=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return up(n,t,o).bind((({box:e})=>gp(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":fp(e,t,o);break;case"absolute":bp(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{is(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),yp(e,t,o)})(e,t,o)},Sp=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Rs(e),r=Ls(),a=n(r,dp({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>mp(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),pp(a)):A.none()})(t.element,s,n,e).each((e=>{xp(t,o,n,s,e)}))},Cp=Sp(lp),kp=Sp(cp);var Op=Object.freeze({__proto__:null,refresh:yp,reset:wp,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:Cp,forceDockToBottom:kp}),_p=Object.freeze({__proto__:null,events:(e,t)=>Hc([Kc(Ta(),((o,n)=>{e.contextual.each((e=>{rs(o.element,e.transitionClass)&&(is(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Wc(Ga(),((o,n)=>{yp(o,e,t)})),Wc(Za(),((o,n)=>{yp(o,e,t)})),Wc(ja(),((o,n)=>{wp(o,e,t)}))])}),Tp=[Jr("contextual",[Lr("fadeInClass"),Lr("fadeOutClass"),Lr("transitionClass"),Hr("lazyContext"),bi("onShow"),bi("onShown"),bi("onHide"),bi("onHidden")]),sa("lazyViewport",(()=>({bounds:Ls(),optScrollEnv:A.none()}))),ra("modes",["top","bottom"],or),bi("onDocked"),bi("onUndocked")];const Ep=id({fields:Tp,name:"docking",active:_p,apis:Op,state:Object.freeze({__proto__:null,init:e=>{const t=ye(!1),o=ye(!0),n=Xe(),s=ye(e.modes);return Dc({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),Ap=xe([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),Mp=e=>t=>t.translate(-e.left,-e.top),Dp=e=>t=>t.translate(e.left,e.top),Bp=e=>(t,o)=>W(e,((e,t)=>t(e)),Dn(t,o)),Ip=(e,t,o)=>e.fold(Bp([Dp(o),Mp(t)]),Bp([Mp(t)]),Bp([])),Fp=(e,t,o)=>e.fold(Bp([Dp(o)]),Bp([]),Bp([Dp(t)])),Rp=(e,t,o)=>e.fold(Bp([]),Bp([Mp(o)]),Bp([Dp(t),Mp(o)])),Np=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},zp=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(Rp,Lp),s(Fp,Vp),s(Ip,Hp))},Lp=Ap.offset,Vp=Ap.absolute,Hp=Ap.fixed,Pp=(e,t)=>{const o=qo(e,t);return u(o)?NaN:parseInt(o,10)},Up=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=Pp(o,t.leftAttr),s=Pp(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some(Dn(n,s))})(e,t).fold((()=>o),(e=>Hp(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?$p(e,t,a,s,r):Gp(e,t,a,s,r),l=Ip(a,s,r);return((e,t,o)=>{const n=e.element;Go(n,t.leftAttr,o.left+"px"),Go(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:Hp(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},Wp=(e,t,o,n)=>se(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=Fp(e,s,r),i=Fp(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:zp(e.output,t,o,n),extra:e.extra}):A.none()})),$p=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return Wp(r,o,n,s).orThunk((()=>{const e=W(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=Fp(e,s,r),i=Fp(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return Dn(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:zp(e.output,o,n,s),extra:e.extra})))}))},Gp=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return Wp(r,o,n,s)};var jp=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=lo(e.element),o=Rn(t),r=Ps(s),a=((e,t,o)=>({coord:zp(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=Np(a.coord,0,r);mn(s,i)}}});const qp=(e,t)=>aa(e,{},L(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,Br(o,o,{tag:"option",process:{}},Js((e=>qs("The field: "+o+" is forbidden. "+n))));var o,n})).concat([Ir("dump",w)])),Xp=e=>e.dump,Yp=(e,t)=>({...rd(t),...e.dump}),Kp=qp,Jp=Yp,Qp=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[Pr("parts",e)]:[]).concat([Rr("uid"),Qr("dom",{}),Qr("components",[]),Si("originalSpec"),Qr("debug.sketcher",{})]).concat(t))(n,s);return Er(e+" [SpecSchema]",br(r.concat(t)),o)},Zp=(e,t,o,n,s)=>{const r=eh(s),a=il(o),i=vl(o),l=Qp(e,t,r,a,[i]),c=cl(0,l,o);return n(l,dl(e,l,c.internals()),r,c.externals())},eh=e=>(e=>be(e,"uid"))(e)?e:{...e,uid:lg("uid")},th=br([Rr("name"),Rr("factory"),Rr("configFields"),Qr("apis",{}),Qr("extraApis",{})]),oh=br([Rr("name"),Rr("factory"),Rr("configFields"),Rr("partFields"),Qr("apis",{}),Qr("extraApis",{})]),nh=e=>{const t=Er("Sketcher for "+e.name,th,e),o=le(t.apis,Om),n=le(t.extraApis,((e,t)=>Oi(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=eh(n);return o(Qp(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},sh=e=>{const t=Er("Sketcher for "+e.name,oh,e),o=sl(t.name,t.partFields),n=le(t.apis,Om),s=le(t.extraApis,((e,t)=>Oi(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>Zp(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},rh=nh({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:Xp(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[Qr("components",[]),qp("containerBehaviours",[]),Qr("events",{}),Qr("domModification",{}),Qr("eventOrder",{})]}),ah="data-initial-z-index",ih=(e,t)=>{e.getSystem().addToGui(t),(e=>{go(e.element).filter(no).each((t=>{hn(t,"z-index").each((e=>{Go(t,ah,e)})),dn(t,"z-index",gn(e.element,"z-index"))}))})(t)},lh=e=>{(e=>{go(e.element).filter(no).each((e=>{Xo(e,ah).fold((()=>vn(e,"z-index")),(t=>dn(e,"z-index",t))),Ko(e,ah)}))})(e),e.getSystem().removeFromGui(e)},ch=(e,t,o)=>e.getSystem().build(rh.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var dh=Jr("snaps",[Rr("getSnapPoints"),bi("onSensor"),Rr("leftAttr"),Rr("topAttr"),Qr("lazyViewport",Ls),Qr("mustSnap",!1)]);const uh=[Qr("useFixed",T),Rr("blockerClass"),Qr("getTarget",w),Qr("onDrag",b),Qr("repositionTarget",!0),Qr("onDrop",b),sa("getBounds",Ls),dh],mh=e=>{return(t=hn(e,"left"),o=hn(e,"top"),n=hn(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?Hp:Lp)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=In(e);return Vp(t.left,t.top)}));var t,o,n},gh=(e,t)=>({bounds:e.getBounds(),height:Cn(t.element),width:En(t.element)}),ph=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>gh(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=lo(e.element),a=Rn(r),i=Ps(s),l=mh(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=Fp(t,o,n),i=Fe(a.left,r.x,r.x+r.width-s.width),l=Fe(a.top,r.y,r.y+r.height-s.height),c=Vp(i,l);return t.fold((()=>{const e=Rp(c,o,n);return Lp(e.left,e.top)}),y(c),(()=>{const e=Ip(c,o,n);return Hp(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>Lp(e+a,t+i)),((e,t)=>Vp(e+a,t+i)),((e,t)=>Hp(e+a,t+i))));var t,a,i;const l=Ip(e,n,s);return Hp(l.left,l.top)}),(t=>{const a=Up(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=Np(c,0,i);mn(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},hh=(e,t,o,n)=>{t.each(lh),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;Ko(o,t.leftAttr),Ko(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},fh=e=>(t,o)=>{const n=e=>{o.setStartData(gh(t,e))};return Hc([Wc(Ga(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var bh=Object.freeze({__proto__:null,getData:e=>A.from(Dn(e.x,e.y)),getDelta:(e,t)=>Dn(t.left-e.left,t.top-e.top)});const vh=(e,t,o)=>[Wc(pa(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>hh(n,A.some(l),e,t),a=_i(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),ph(n,e,t,bh,o)}},l=ch(n,e.blockerClass,(e=>Hc([Wc(pa(),e.forceDrop),Wc(ba(),e.drop),Wc(ha(),((t,o)=>{e.move(o.event)})),Wc(fa(),e.delayDrop)]))(i));o(n),ih(n,l)}))],xh=[...uh,wi("dragger",{handlers:fh(vh)})];var yh=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some(Dn(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>Dn(t.left-e.left,t.top-e.top)});const wh=(e,t,o)=>{const n=Xe(),s=o=>{hh(o,n.get(),e,t),n.clear()};return[Wc(da(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{ph(r,e,t,yh,o)}},c=ch(r,e.blockerClass,(e=>Hc([Wc(da(),e.forceDrop),Wc(ma(),e.drop),Wc(ga(),e.drop),Wc(ua(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),ih(r,c)})),Wc(ua(),((o,n)=>{n.stop(),ph(o,e,t,yh,n.event)})),Wc(ma(),((e,t)=>{t.stop(),s(e)})),Wc(ga(),s)]},Sh=xh,Ch=[...uh,wi("dragger",{handlers:fh(wh)})],kh=[...uh,wi("dragger",{handlers:fh(((e,t,o)=>[...vh(e,t,o),...wh(e,t,o)]))})];var Oh=Object.freeze({__proto__:null,mouse:Sh,touch:Ch,mouseOrTouch:kh}),_h=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=y({});return Dc({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const Th=cd({branchKey:"mode",branches:Oh,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:_h,apis:jp}),Eh=["input","textarea"],Ah=e=>{const t=eo(e);return F(Eh,t)},Mh=(e,t)=>{const o=t.getRoot(e).getOr(e.element);ss(o,t.invalidClass),t.notify.each((t=>{Ah(e.element)&&Go(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{tn(e,t.validHtml)})),t.onValid(e)}))},Dh=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);os(s,t.invalidClass),t.notify.each((t=>{Ah(e.element)&&Go(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{tn(e,n)})),t.onInvalid(e,n)}))},Bh=(e,t,o)=>t.validator.fold((()=>_e(Ae.value(!0))),(t=>t.validate(e))),Ih=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),Bh(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(Dh(e,t,0,o),Ae.error(o))),(o=>(Mh(e,t),Ae.value(o)))):Ae.error("No longer in system"))));var Fh=Object.freeze({__proto__:null,markValid:Mh,markInvalid:Dh,query:Bh,run:Ih,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return rs(o,t.invalidClass)}}),Rh=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Hc([Wc(t.onEvent,(t=>{Ih(t,e).get(w)}))].concat(t.validateOnLoad?[Jc((t=>{Ih(t,e).get(b)}))]:[])))).getOr({})}),Nh=[Rr("invalidClass"),Qr("getRoot",A.none),Jr("notify",[Qr("aria","alert"),Qr("getContainer",A.none),Qr("validHtml",""),bi("onValid"),bi("onInvalid"),bi("onValidate")]),Jr("validator",[Rr("validate"),Qr("onEvent","input"),Qr("validateOnLoad",!0)])];const zh=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},Lh=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var Vh=Object.freeze({__proto__:null,onLoad:zh,onUnload:Lh,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),Hh=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Jc(((o,n)=>{zh(o,e,t)})),Qc(((o,n)=>{Lh(o,e,t)}))]:[od(e,t,zh)];return Hc(o)}});const Ph=()=>{const e=ye(null);return Dc({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},Uh=()=>{const e=ye({}),t=ye({});return Dc({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>fe(e.get(),o).orThunk((()=>fe(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};V(o,(e=>{r[e.value]=e,fe(e,"meta").each((t=>{fe(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var Wh=Object.freeze({__proto__:null,memory:Ph,dataset:Uh,manual:()=>Dc({readState:b}),init:e=>e.store.manager.state(e)});const $h=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var Gh=[$r("initialValue"),Rr("getFallbackEntry"),Rr("getDataKey"),Rr("setValue"),wi("manager",{setValue:$h,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{$h(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:Uh})],jh=[Rr("getValue"),Qr("setValue",b),$r("initialValue"),wi("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:Mc.init})],qh=[$r("initialValue"),wi("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:Ph})],Xh=[Zr("store",{mode:"memory"},Mr("mode",{memory:qh,manual:jh,dataset:Gh})),bi("onSetValue"),Qr("resetOnDom",!1)];const Yh=id({fields:Xh,name:"representing",active:Hh,apis:Vh,extra:{setValueFrom:(e,t)=>{const o=Yh.getValue(t);Yh.setValue(e,o)}},state:Wh}),Kh=id({fields:Nh,name:"invalidating",active:Rh,apis:Fh,extra:{validation:e=>t=>{const o=Yh.getValue(t);return _e(e(o))}}});var Jh=Object.freeze({__proto__:null,exhibit:(e,t)=>td({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const Qh=xe([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Zh=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>op(e,u,m,h,h)),(()=>op(e,h,m,g,h)),(()=>op(e,u,h,h,p)),(()=>op(e,h,h,g,p)),(()=>op(e,u,m,h,h)),(()=>op(e,u,h,h,p)),(()=>op(e,u,m,h,h)),(()=>op(e,h,m,g,h)))},ef=(e,t)=>e.fold((()=>{const e=t.rect;return op("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>Zh("absolute",t,e,o,n,s)),((e,o,n,s)=>Zh("fixed",t,e,o,n,s))),tf=(e,t)=>{const o=C(Is,t),n=e.fold(o,o,(()=>{const e=Rn();return Is(t).translate(-e.left,-e.top)})),s=En(t),r=Cn(t);return Fs(n.left,n.top,s,r)},of=(e,t)=>t.fold((()=>e.fold(Ls,Ls,Fs)),(t=>e.fold(y(t),y(t),(()=>{const o=nf(e,t.x,t.y);return Fs(o.left,o.top,t.width,t.height)})))),nf=(e,t,o)=>{const n=Dn(t,o);return e.fold(y(n),y(n),(()=>{const e=Rn();return n.translate(-e.left,-e.top)}))};Qh.none;const sf=Qh.relative,rf=Qh.fixed,af=xe([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),lf=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Fe(i,e.y,e.bottom):Fe(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Fs(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Fs(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Fe(a,o,d),g=Fe(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Fs(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=y(t.bottom-o.y),s=y(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=y(t.right-o.x),i=y(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),x={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?af.fit(x):af.nofit(x,m,g,f)},cf=["top","bottom","right","left"],df="data-alloy-transition-timer",uf=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>X(t,(t=>rs(e,t))))(e,t.classes))(e,n)){dn(e,"position",o.position);const a=tf(t,e),l=ef(t,{...s,rect:a}),c=J(cf,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return pe(t,((t,n)=>!((e,t,o=S)=>Ue(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(mn(e,c),i&&((e,t)=>{as(e,t.classes),Xo(e,df).each((t=>{clearTimeout(parseInt(t,10)),Ko(e,df)})),((e,t)=>{const o=qe(),n=qe();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return yt(t.target,e)&&ot(n)&&F(cf,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===Ta())&&(clearTimeout(s),Ko(e,df),is(e,t.classes))}},l=Mo(e,Ea(),(t=>{a(t)&&(l.unbind(),o.set(Mo(e,Ta(),i)),n.set(Mo(e,_a(),i)))})),c=(e=>{const t=t=>{const o=gn(e,t).split(/\s*,\s*/);return P(o,tt)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ze(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return W(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),Go(e,df,s)}))})(e,t)})(e,n),xn(e))}else is(e,n.classes)},mf=(e,t,o)=>void 0===e[t]?o:e[t],gf=(e,t,o,n)=>{const s=((e,t,o,n)=>{vn(t,"max-height"),vn(t,"max-width");const s=(r=t,{width:Math.ceil(An(r)),height:Cn(r)});var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=lf(m,a,i,r);return g.fold(y(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:af.nofit(l,c,d,u)))};return W(t,((e,t)=>{const o=C(l,t);return e.fold(y(e),o)}),af.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Ol(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=ef(o.origin,t);o.transition.each((s=>{uf(e,o.origin,n,s,t,o.lastPlacement)})),np(e,n)})(t,s,n),((e,t)=>{((e,t)=>{Go(e,tc,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;is(e,o.off),as(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},pf=w,hf=()=>Jr("layouts",[Rr("onLtr"),Rr("onRtl"),$r("onBottomLtr"),$r("onBottomRtl")]),ff=(e,t,o,n,s,r,a)=>{const i=a.map(Ec).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return Ln(d,u)(e)};var bf=[Rr("hotspot"),$r("bubble"),Qr("overrides",{}),hf(),wi("placement",((e,t,o)=>{const n=t.hotspot,s=tf(o,n.element),r=ff(e.element,t,Zl(),ec(),Jl(),Ql(),A.some(t.hotspot.element));return A.some(pf({anchorBox:s,bubble:t.bubble.getOr(Sl()),overrides:t.overrides,layouts:r}))}))],vf=[Rr("x"),Rr("y"),Qr("height",0),Qr("width",0),Qr("bubble",Sl()),Qr("overrides",{}),hf(),wi("placement",((e,t,o)=>{const n=nf(o,t.x,t.y),s=Fs(n.left,n.top,t.width,t.height),r=ff(e.element,t,Yl(),Kl(),Yl(),Kl(),A.none());return A.some(pf({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const xf=xe([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),yf=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),wf=e=>e.fold(w,w),Sf=e=>W(e,((e,t)=>e.translate(t.left,t.top)),Dn(0,0)),Cf=e=>{const t=L(e,wf);return Sf(t)},kf=xf.screen,Of=xf.absolute,_f=(e,t,o)=>{const n=lo(e.element),s=Rn(n),r=((e,t,o)=>{const n=mo(o.root).dom;return A.from(n.frameElement).map(ct).filter((t=>{const o=lo(t),n=lo(e.element);return yt(o,n)})).map(In)})(e,0,o).getOr(s);return Of(r,s.left,s.top)},Tf=(e,t,o,n)=>{const s=kf(Dn(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},Ef=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>Cf(r),l=()=>Cf(r),c=()=>(e=>{const t=L(e,yf);return Sf(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?Jl():Zl(),m=o.showAbove?Ql():ec(),g=ff(s,o,u,m,u,m,A.none());var p,h,f,b;return pf({anchorBox:d,bubble:o.bubble.getOr(Sl()),overrides:o.overrides,layouts:g})}));var Af=[Rr("node"),Rr("root"),$r("bubble"),hf(),Qr("overrides",{}),Qr("showAbove",!1),wi("placement",((e,t,o)=>{const n=_f(e,0,t);return t.node.filter(sn).bind((s=>{const r=s.dom.getBoundingClientRect(),a=Tf(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return Ef(a,n,t,o,i)}))}))];const Mf=(e,t)=>({element:e,offset:t}),Df=(e,t)=>so(e)?Mf(e,t):((e,t)=>{const o=fo(e);if(0===o.length)return Mf(e,t);if(t<o.length)return Mf(o[t],0);{const e=o[o.length-1],t=so(e)?(e=>zn.get(e))(e).length:fo(e).length;return Mf(e,t)}})(e,t),Bf=e=>void 0!==e.foffset,If=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(ks))(e)))().map((e=>{if(Bf(e)){const t=Df(e.start,e.soffset),o=Df(e.finish,e.foffset);return Ss.range(t.element,t.offset,o.element,o.offset)}return e}));var Ff=[$r("getSelection"),Rr("root"),$r("bubble"),hf(),Qr("overrides",{}),Qr("showAbove",!1),wi("placement",((e,t,o)=>{const n=mo(t.root).dom,s=_f(e,0,t),r=If(n,t).bind((e=>{if(Bf(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(mt):A.none()})(ht(e,t)))(n,Ss.exactFromRange(e)).orThunk((()=>{const t=lt("\ufeff");Vo(e.start,t);const o=Os(n,Ss.exact(t,0,t,1));return Qo(t),o}));return t.bind((e=>Tf(e.left,e.top,e.width,e.height)))}{const t=le(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return Tf(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=If(n,t).bind((e=>Bf(e)?no(e.start)?A.some(e.start):po(e.start):A.some(e.firstCell))).getOr(e.element);return Ef(r,s,t,o,a)}))];const Rf="link-layout",Nf=e=>e.x+e.width,zf=(e,t)=>e.x-t.width,Lf=(e,t)=>e.y-t.height+e.height,Vf=e=>e.y,Hf=(e,t,o)=>Cl(Nf(e),Vf(e),o.southeast(),Ol(),"southeast",Il(e,{left:0,top:2}),Rf),Pf=(e,t,o)=>Cl(zf(e,t),Vf(e),o.southwest(),_l(),"southwest",Il(e,{right:1,top:2}),Rf),Uf=(e,t,o)=>Cl(Nf(e),Lf(e,t),o.northeast(),Tl(),"northeast",Il(e,{left:0,bottom:3}),Rf),Wf=(e,t,o)=>Cl(zf(e,t),Lf(e,t),o.northwest(),El(),"northwest",Il(e,{right:1,bottom:3}),Rf),$f=()=>[Hf,Pf,Uf,Wf],Gf=()=>[Pf,Hf,Wf,Uf];var jf=[Rr("item"),hf(),Qr("overrides",{}),wi("placement",((e,t,o)=>{const n=tf(o,t.item.element),s=ff(e.element,t,$f(),Gf(),$f(),Gf(),A.none());return A.some(pf({anchorBox:n,bubble:Sl(),overrides:t.overrides,layouts:s}))}))],qf=Mr("type",{selection:Ff,node:Af,hotspot:bf,submenu:jf,makeshift:vf});const Xf=[Wr("classes",or),oa("mode","all",["all","layout","placement"])],Yf=[Qr("useFixed",T),$r("getBounds")],Kf=[Nr("anchor",qf),Jr("transition",Xf)],Jf=(e,t,o,n,s,r)=>{const a=Er("placement.info",vr(Kf),s),i=a.anchor,l=n.element,c=o.get(n.uid);$m((()=>{dn(l,"position","fixed");const s=hn(l,"visibility");dn(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return rf(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=In(e.element),o=e.element.dom.getBoundingClientRect();return sf(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=mf(a,"maxHeightFunction",Cc()),c=mf(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:of(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return gf(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{vn(l,"visibility")}),(e=>{dn(l,"visibility",e)})),hn(l,"left").isNone()&&hn(l,"top").isNone()&&hn(l,"right").isNone()&&hn(l,"bottom").isNone()&&He(hn(l,"position"),"fixed")&&vn(l,"position")}),l)};var Qf=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();Jf(e,t,o,n,s,r)},positionWithinBounds:Jf,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;V(["position","left","right","top","bottom"],(e=>vn(s,e))),(e=>{Ko(e,tc)})(s),o.clear(n.uid)}});const Zf=id({fields:Yf,name:"positioning",active:Jh,apis:Qf,state:Object.freeze({__proto__:null,init:()=>{let e={};return Dc({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>fe(e,t)})}})});var eb=Object.freeze({__proto__:null,events:e=>Hc([Wc(Ra(),((t,o)=>{const n=e.channels,s=re(n),r=o,a=((e,t)=>t.universal?e:P(e,(e=>F(t.channels,e))))(s,r);V(a,(e=>{const o=n[e],s=o.schema,a=Er("channel["+e+"] data\nReceiver: "+si(t.element),s,r.data);o.onReceive(t,a)}))}))])}),tb=[Nr("channels",Or(Ae.value,br([xi("onReceive"),Qr("schema",Zs())])))];const ob=id({fields:tb,name:"receiving",active:eb});var nb=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?jm:Gm)(o,r)}))};return Hc([Wc(Ra(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;F(s.channels,n)&&o(t,s.data)}})),Jc(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),sb=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),rb=[Rr("channel"),$r("renderComponents"),$r("updateState"),$r("initialData"),na("reuseDom",!0)];const ab=id({fields:rb,name:"reflecting",active:nb,apis:sb,state:Object.freeze({__proto__:null,init:()=>{const e=ye(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),ib=(e,t,o,n)=>{o.get().each((t=>{Nm(e)}));const s=t.getAttachPoint(e);Bm(s,e);const r=e.getSystem().build(n);return Bm(e,r),o.set(r),r},lb=(e,t,o,n)=>{const s=ib(e,t,o,n);return t.onOpen(e,s),s},cb=(e,t,o)=>{o.get().each((n=>{Nm(e),Rm(e),t.onClose(e,n),o.clear()}))},db=(e,t,o)=>o.isOpen(),ub=(e,t,o)=>{const n=t.getAttachPoint(e);dn(e.element,"position",Zf.getMode(n)),((e,t,o)=>{hn(e.element,t).fold((()=>{Ko(e.element,o)}),(t=>{Go(e.element,o,t)})),dn(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},mb=(e,t,o)=>{(e=>R(["top","left","right","bottom"],(t=>hn(e,t).isSome())))(e.element)||vn(e.element,"position"),((e,t,o)=>{Xo(e.element,o).fold((()=>vn(e.element,t)),(o=>dn(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var gb=Object.freeze({__proto__:null,cloak:ub,decloak:mb,open:lb,openWhileCloaked:(e,t,o,n,s)=>{ub(e,t),lb(e,t,o,n),s(),mb(e,t)},close:cb,isOpen:db,isPartOf:(e,t,o,n)=>db(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>ib(e,t,o,n)))}),pb=Object.freeze({__proto__:null,events:(e,t)=>Hc([Wc(Ha(),((o,n)=>{cb(o,e,t)}))])}),hb=[bi("onOpen"),bi("onClose"),Rr("isPartOf"),Rr("getAttachPoint"),Qr("cloakVisibilityAttr","data-precloak-visibility")],fb=Object.freeze({__proto__:null,init:()=>{const e=Xe(),t=y("not-implemented");return Dc({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const bb=id({fields:hb,name:"sandboxing",active:pb,apis:gb,state:fb}),vb=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),xb=e=>e.dimension.property,yb=(e,t)=>e.dimension.getDimension(t),wb=(e,t)=>{const o=vb(e,t);is(o,[t.shrinkingClass,t.growingClass])},Sb=(e,t)=>{ss(e.element,t.openClass),os(e.element,t.closedClass),dn(e.element,xb(t),"0px"),xn(e.element)},Cb=(e,t)=>{ss(e.element,t.closedClass),os(e.element,t.openClass),vn(e.element,xb(t))},kb=(e,t,o,n)=>{o.setCollapsed(),dn(e.element,xb(t),yb(t,e.element)),wb(e,t),Sb(e,t),t.onStartShrink(e),t.onShrunk(e)},Ob=(e,t,o,n)=>{const s=n.getOrThunk((()=>yb(t,e.element)));o.setCollapsed(),dn(e.element,xb(t),s),xn(e.element);const r=vb(e,t);ss(r,t.growingClass),os(r,t.shrinkingClass),Sb(e,t),t.onStartShrink(e)},_b=(e,t,o)=>{const n=yb(t,e.element);("0px"===n?kb:Ob)(e,t,o,A.some(n))},Tb=(e,t,o)=>{const n=vb(e,t),s=rs(n,t.shrinkingClass),r=yb(t,e.element);Cb(e,t);const a=yb(t,e.element);(s?()=>{dn(e.element,xb(t),r),xn(e.element)}:()=>{Sb(e,t)})(),ss(n,t.shrinkingClass),os(n,t.growingClass),Cb(e,t),dn(e.element,xb(t),a),o.setExpanded(),t.onStartGrow(e)},Eb=(e,t,o)=>{const n=vb(e,t);return!0===rs(n,t.growingClass)},Ab=(e,t,o)=>{const n=vb(e,t);return!0===rs(n,t.shrinkingClass)};var Mb=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){vn(e.element,xb(t));const o=yb(t,e.element);dn(e.element,xb(t),o)}},grow:(e,t,o)=>{o.isExpanded()||Tb(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&_b(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&kb(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:Eb,isShrinking:Ab,isTransitioning:(e,t,o)=>Eb(e,t)||Ab(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?_b:Tb)(e,t,o)},disableTransitions:wb,immediateGrow:(e,t,o)=>{o.isExpanded()||(Cb(e,t),dn(e.element,xb(t),yb(t,e.element)),wb(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),Db=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return td(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:ia(t.dimension.property,"0px")})},events:(e,t)=>Hc([Kc(Ta(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(wb(o,e),t.isExpanded()&&vn(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),Bb=[Rr("closedClass"),Rr("openClass"),Rr("shrinkingClass"),Rr("growingClass"),$r("getAnimationRoot"),bi("onShrunk"),bi("onStartShrink"),bi("onGrown"),bi("onStartGrow"),Qr("expanded",!1),Nr("dimension",Mr("property",{width:[wi("property","width"),wi("getDimension",(e=>Tn(e)+"px"))],height:[wi("property","height"),wi("getDimension",(e=>Sn(e)+"px"))]}))];const Ib=id({fields:Bb,name:"sliding",active:Db,apis:Mb,state:Object.freeze({__proto__:null,init:e=>{const t=ye(e.expanded);return Dc({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:C(t.set,!1),setExpanded:C(t.set,!0),readState:()=>"expanded: "+t.get()})}})});var Fb=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Hc([Wc(e.event,o),Qc((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Wc(e,(()=>t.cancel()))])).getOr([])))}});const Rb=e=>{const t=ye(null);return Dc({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var Nb=Object.freeze({__proto__:null,throttle:Rb,init:e=>e.stream.streams.state(e)}),zb=[Nr("stream",Mr("mode",{throttle:[Rr("delay"),Qr("stopEvent",!0),wi("streams",{setup:(e,t)=>{const o=e.stream,n=nt(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:Rb})]})),Qr("event","input"),$r("cancelEvent"),xi("onStream")];const Lb=id({fields:zb,name:"streaming",active:Fb,state:Nb});var Vb=Object.freeze({__proto__:null,exhibit:(e,t)=>td({attributes:la([{key:t.tabAttr,value:"true"}])})}),Hb=[Qr("tabAttr","data-alloy-tabstop")];const Pb=id({fields:Hb,name:"tabstopping",active:Vb}),Ub=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?os(e.element,t):ss(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},Wb=(e,t,o)=>{Ub(e,t,o,!o.get())},$b=(e,t,o)=>{Ub(e,t,o,t.selected)};var Gb=Object.freeze({__proto__:null,onLoad:$b,toggle:Wb,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{Ub(e,t,o,!0)},off:(e,t,o)=>{Ub(e,t,o,!1)},set:Ub}),jb=Object.freeze({__proto__:null,exhibit:()=>td({}),events:(e,t)=>{const o=(n=e,s=t,r=Wb,ed((e=>{r(e,n,s)})));var n,s,r;const a=od(e,t,$b);return Hc(j([e.toggleOnExecute?[o]:[],[a]]))}});const qb=(e,t,o)=>{Go(e.element,"aria-expanded",o)};var Xb=[Qr("selected",!1),$r("toggleClass"),Qr("toggleOnExecute",!0),bi("onToggled"),Zr("aria",{mode:"none"},Mr("mode",{pressed:[Qr("syncWithExpanded",!1),wi("update",((e,t,o)=>{Go(e.element,"aria-pressed",o),t.syncWithExpanded&&qb(e,0,o)}))],checked:[wi("update",((e,t,o)=>{Go(e.element,"aria-checked",o)}))],expanded:[wi("update",qb)],selected:[wi("update",((e,t,o)=>{Go(e.element,"aria-selected",o)}))],none:[wi("update",b)]}))];const Yb=id({fields:Xb,name:"toggling",active:jb,apis:Gb,state:(Kb=!1,{init:()=>{const e=ye(Kb);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(Kb),readState:()=>e.get()}}})});var Kb;const Jb=Ne("tooltip.exclusive"),Qb=Ne("tooltip.show"),Zb=Ne("tooltip.hide"),ev=Ne("tooltip.immediateHide"),tv=Ne("tooltip.immediateShow"),ov=(e,t,o)=>{e.getSystem().broadcastOn([Jb],{})};var nv=Object.freeze({__proto__:null,hideAllExclusive:ov,immediateOpenClose:(e,t,o,n)=>Fc(e,n?tv:ev),isEnabled:(e,t,o)=>o.isEnabled(),setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Zm.set(e,n)}))},setEnabled:(e,t,o,n)=>o.setEnabled(n)}),sv=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{n.getSystem().isConnected()&&(Rm(n),e.onHide(o,n),t.clearTooltip())})),t.clearTimer()},n=o=>{if(!t.isShowing()&&t.isEnabled()){ov(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Hc("normal"===e.mode?[Wc(va(),(e=>{Fc(o,Qb)})),Wc(fa(),(e=>{Fc(o,Zb)}))]:[]),behaviours:rd([Zm.config({})])});t.setTooltip(s),Bm(n,s),e.onShow(o,s),Zf.position(n,s,{anchor:e.anchor(o)})}},s=o=>{t.getTooltip().each((t=>{const n=e.lazySink(o).getOrDie();Zf.position(n,t,{anchor:e.anchor(o)})}))};return Hc(j([[Zc((t=>{e.onSetup(t)})),Wc(Qb,(o=>{t.resetTimer((()=>{n(o)}),e.delayForShow())})),Wc(Zb,(n=>{t.resetTimer((()=>{o(n)}),e.delayForHide())})),Wc(tv,(e=>{t.resetTimer((()=>{n(e)}),0)})),Wc(ev,(e=>{t.resetTimer((()=>{o(e)}),0)})),Wc(Ra(),((e,t)=>{const n=t;n.universal||F(n.channels,Jb)&&o(e)})),Qc((e=>{o(e)}))],(()=>{switch(e.mode){case"normal":return[Wc(xa(),(e=>{Fc(e,tv)})),Wc(Ia(),(e=>{Fc(e,ev)})),Wc(va(),(e=>{Fc(e,Qb)})),Wc(fa(),(e=>{Fc(e,Zb)}))];case"follow-highlight":return[Wc(oi(),((e,t)=>{Fc(e,Qb)})),Wc(ni(),(e=>{Fc(e,Zb)}))];case"children-normal":return[Wc(xa(),((o,n)=>{Lo(o.element).each((r=>{bt(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{Fc(o,tv)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Wc(Ia(),(e=>{Lo(e.element).fold((()=>{Fc(e,ev)}),b)})),Wc(va(),(o=>{Xn(o.element,"[data-mce-tooltip]:hover").each((n=>{t.getTooltip().fold((()=>{Fc(o,Qb)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Wc(fa(),(e=>{Xn(e.element,"[data-mce-tooltip]:hover").fold((()=>{Fc(e,Zb)}),b)}))];default:return[Wc(xa(),((o,n)=>{Lo(o.element).each((r=>{bt(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{Fc(o,tv)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Wc(Ia(),(e=>{Lo(e.element).fold((()=>{Fc(e,ev)}),b)}))]}})()]))}}),rv=[Rr("lazySink"),Rr("tooltipDom"),Qr("exclusive",!0),Qr("tooltipComponents",[]),sa("delayForShow",y(300)),sa("delayForHide",y(100)),sa("onSetup",b),oa("mode","normal",["normal","follow-highlight","children-keyboard-focus","children-normal"]),Qr("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:y([jl,Gl,Pl,Wl,Ul,$l]),onRtl:y([jl,Gl,Pl,Wl,Ul,$l])},bubble:wl(0,-2,{})}))),bi("onHide"),bi("onShow")],av=Object.freeze({__proto__:null,init:()=>{const e=ye(!0),t=Xe(),o=Xe(),n=()=>{t.on(clearTimeout)},s=y("not-implemented");return Dc({getTooltip:o.get,isShowing:o.isSet,setTooltip:o.set,clearTooltip:o.clear,clearTimer:n,resetTimer:(e,o)=>{n(),t.set(setTimeout(e,o))},readState:s,isEnabled:()=>e.get(),setEnabled:t=>e.set(t)})}});const iv=id({fields:rv,name:"tooltipping",active:sv,state:av,apis:nv}),lv=id({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Hc([Pc(Aa(),E)]),exhibit:()=>td({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),cv=e=>{const t=at(e),o=fo(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return W(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:en(t)};return{tag:eo(t),classes:s,attributes:n,...r}},dv=e=>{const t=(e=>void 0!==e.uid)(e)&&ve(e,"uid")?e.uid:lg("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}},uv=xl,mv=ol,gv=y("dismiss.popups"),pv=y("reposition.popups"),hv=y("mouse.released"),fv=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=Xe(),o=ye(!1),n=_i((t=>{e.triggerEvent(Va(),t),o.set(!0)}),400),s=la([{key:da(),value:e=>(Ti(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:ua(),value:e=>(n.cancel(),Ti(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:ma(),value:s=>(n.cancel(),t.get().filter((e=>yt(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(La(),s))))}]);return{fireIfReady:(e,t)=>fe(s,t).bind((t=>t(e)))}})(o),s=L(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>Mo(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=Xe(),a=Mo(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(Fa(),e)}),0))})),i=Mo(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===bd[0]&&!F(["input","textarea"],eo(e.target))&&!ps(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=Mo(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=Xe(),d=Mo(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(Ia(),e)}),0))}));return{unbind:()=>{V(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},bv=(e,t)=>{const o=fe(e,"target").getOr(t);return ye(o)},vv=xe([{stopped:[]},{resume:["element"]},{complete:[]}]),xv=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=ye(!1),n=ye(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),vv.complete())),(e=>{const o=e.descHandler;return gg(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),vv.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),vv.complete()):go(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),vv.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),vv.resume(n))))}))},yv=(e,t,o,n,s,r)=>xv(e,t,o,n,s,r).fold(E,(n=>yv(e,t,o,n,s,r)),T),wv=(e,t,o,n,s)=>{const r=bv(o,n);return yv(e,t,o,n,r,s)},Sv=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{ie(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:C.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{ie(e,((e,o)=>{be(e,t)&&delete e[t]}))},filterByType:t=>fe(e,t).map((e=>ge(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>fe(e,o).bind((e=>bs(n,(t=>((e,t)=>ig(t).bind((t=>fe(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{ig(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return ig(t).getOrThunk((()=>((e,t)=>{const o=Ne(sg+"uid-");return ag(t,o),o})(0,e.element)))})(n);ve(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+si(s.element)+"\nCannot use it for: "+si(e.element)+"\nThe conflicting element is"+(sn(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>fe(t,e)}},Cv=e=>{const t=t=>go(e.element).fold(E,(e=>yt(t,e))),o=Sv(),n=(e,n)=>o.find(t,e,n),s=fv(e.element,{triggerEvent:(e,t)=>li(e,t.target,(o=>((e,t,o,n)=>wv(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:y("real"),triggerEvent:(e,t,o)=>{li(e,t,(s=>wv(n,e,o,t,s)))},triggerFocus:(e,t)=>{ig(e).fold((()=>{Fo(e)}),(o=>{li(Ba(),e,(o=>(((e,t,o,n,s)=>{const r=bv(o,n);xv(e,t,o,n,r,s)})(n,Ba(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:Ag,buildOrPatch:Eg,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),so(e.element)||(o.register(e),V(e.components(),a),r.triggerEvent(Ua(),e.element,{target:e.element}))},i=e=>{so(e.element)||(V(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{Bm(e,t)},c=e=>{Rm(e)},d=e=>{const t=o.filter(Ra());V(t,(t=>{const o=t.descHandler;gg(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t)=>{const o=(e=>{const t=ye(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return V(e,(e=>{const t=e.descHandler;gg(t)(o)})),o.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>Ae.error(new Error('Could not find component with uid: "'+e+'" in system.'))),Ae.value),h=e=>{const t=ig(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Qo(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},kv=()=>{const e=(e,t)=>{t.stop(),Nc(e)};return[Wc(Oa(),e),Wc(La(),e),Xc(da()),Xc(pa())]},Ov=e=>Hc(j([e.map((e=>ed(((t,o)=>{e(t),o.stop()})))).toArray(),kv()])),_v=nh({name:"Button",factory:e=>{const t=Ov(e.action),o=e.dom.tag,n=t=>fe(e.dom,"attributes").bind((e=>fe(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:Jp(e.buttonBehaviours,[fd.config({}),wm.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[Qr("uid",void 0),Rr("dom"),Qr("components",[]),Kp("buttonBehaviours",[fd,wm]),$r("action"),$r("role"),Qr("eventOrder",{})]}),Tv=y([Qr("shell",!1),Rr("makeItem"),Qr("setupItem",b),Kp("listBehaviours",[Zm])]),Ev=Zi({name:"items",overrides:()=>({behaviours:rd([Zm.config({})])})}),Av=y([Ev]),Mv=sh({name:y("CustomList")(),configFields:Tv(),partFields:Av(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Zm.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Yp(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):ul(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Zm.contents(n),r=o.length,a=r-s.length,i=a>0?N(a,(()=>e.makeItem())):[],l=s.slice(r);V(l,(e=>Zm.remove(n,e))),V(i,(e=>Zm.append(n,e)));const c=Zm.contents(n);V(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),Dv="aria-controls",Bv=()=>{const e=Ne(Dv);return{id:e,link:t=>{Go(t,Dv,e)},unlink:e=>{Ko(e,Dv)}}},Iv=(e,t)=>gs(t,(t=>yt(t,e.element)),T)||((e,t)=>(e=>Wn(e,(e=>{if(!no(e))return!1;const t=qo(e,"id");return void 0!==t&&t.indexOf(Dv)>-1})).bind((e=>{const t=qo(e,"id"),o=So(e);return Xn(o,`[${Dv}="${t}"]`)})))(t).exists((t=>Iv(e,t))))(e,t),Fv="alloy.item-hover",Rv="alloy.item-focus",Nv="alloy.item-toggled",zv=e=>{(Lo(e.element).isNone()||fd.isFocused(e))&&(fd.isFocused(e)||fd.focus(e),Rc(e,Fv,{item:e}))},Lv=e=>{Rc(e,Rv,{item:e})},Vv=y(Fv),Hv=y(Rv),Pv=y(Nv),Uv=e=>e.role.fold((()=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem")),w);var Wv=[Rr("data"),Rr("components"),Rr("dom"),Qr("hasSubmenu",!1),$r("toggling"),$r("role"),Kp("itemBehaviours",[Yb,fd,wm,Yh]),Qr("ignoreFocus",!1),Qr("domModification",{}),wi("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:Uv(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:Jp(e.itemBehaviours,[e.toggling.fold(Yb.revoke,(t=>Yb.config(((e,t)=>({aria:{mode:t?"selected":"checked"},...me(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Rc(e,Nv,{item:e,state:t})})(t,o)}}))(t,e.role.exists((e=>"option"===e)))))),fd.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{Lv(e)}}),wm.config({mode:"execution"}),Yh.config({store:{mode:"memory",initialValue:e.data}}),ud("item-type-events",[...kv(),Wc(va(),zv),Wc(za(),fd.focus)])]),components:e.components,eventOrder:e.eventOrder}))),Qr("eventOrder",{})],$v=[Rr("dom"),Rr("components"),wi("builder",(e=>({dom:e.dom,components:e.components,events:Hc([Yc(za())])})))];const Gv=y("item-widget"),jv=y([Ji({name:"widget",overrides:e=>({behaviours:rd([Yh.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),qv=[Rr("uid"),Rr("data"),Rr("components"),Rr("dom"),Qr("autofocus",!1),Qr("ignoreFocus",!1),Kp("widgetBehaviours",[Yh,fd,wm]),Qr("domModification",{}),vl(jv()),wi("builder",(e=>{const t=cl(Gv(),e,jv()),o=dl(Gv(),e,t.internals()),n=t=>ul(t,e,"widget").map((e=>(wm.focusIn(e),e))),s=(t,o)=>Zd(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Hc([ed(((e,t)=>{n(e).each((e=>{t.stop()}))})),Wc(va(),zv),Wc(za(),((t,o)=>{e.autofocus?n(t):fd.focus(t)}))]),behaviours:Jp(e.widgetBehaviours,[Yh.config({store:{mode:"memory",initialValue:e.data}}),fd.config({ignore:e.ignoreFocus,onFocus:e=>{Lv(e)}}),wm.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:dd(),onLeft:s,onRight:s,onEscape:(t,o)=>fd.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(fd.focus(t),A.some(!0))})])}}))],Xv=Mr("type",{widget:qv,item:Wv,separator:$v}),Yv=y([el({factory:{sketch:e=>{const t=Er("menu.spec item",Xv,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>be(t,"uid")?t:{...t,uid:lg("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),Kv=y([qr("role"),Rr("value"),Rr("items"),Rr("dom"),Rr("components"),Qr("eventOrder",{}),qp("menuBehaviours",[Gd,Yh,zg,wm]),Zr("movement",{mode:"menu",moveOnTab:!0},Mr("mode",{grid:[Ci(),wi("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[wi("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),Rr("rowSelector"),Qr("previousSelector",A.none)],menu:[Qr("moveOnTab",!0),wi("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),Nr("markers",mi()),Qr("fakeFocus",!1),Qr("focusManager",qd()),bi("onHighlight"),bi("onDehighlight"),Qr("showMenuRole",!0)]),Jv=y("alloy.menu-focus"),Qv=sh({name:"Menu",configFields:Kv(),partFields:Yv(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Yp(e.menuBehaviours,[Gd.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),Yh.config({store:{mode:"memory",initialValue:e.value}}),zg.config({find:A.some}),wm.config(e.movement.config(e,e.movement))]),events:Hc([Wc(Hv(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Gd.highlight(e,o),t.stop(),Rc(e,Jv(),{menu:e,item:o})}))})),Wc(Vv(),((e,t)=>{const o=t.event.item;Gd.highlight(e,o)})),Wc(Pv(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===qo(o.element,"role")&&((e,t)=>{const o=ms(e.element,'[role="menuitemradio"][aria-checked="true"]');V(o,(o=>{yt(o,t.element)||e.getSystem().getByDom(o).each((e=>{Yb.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,...e.showMenuRole?{domModification:{attributes:{role:e.role.getOr("menu")}}}:{}})}),Zv=(e,t,o,n)=>fe(o,n).bind((n=>fe(e,n).bind((n=>{const s=Zv(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),ex=e=>"prepared"===e.type?A.some(e.menu):A.none(),tx=()=>{const e=ye({}),t=ye({}),o=ye({}),n=Xe(),s=ye({}),r=e=>a(e).bind(ex),a=e=>fe(t.get(),e),i=t=>fe(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};ie(e,((e,t)=>{V(e,(e=>{o[e]=t}))}));const n=t,s=ce(t,((e,t)=>({k:e,v:t}))),r=le(s,((e,t)=>[t].concat(Zv(o,n,s,t))));return le(o,(e=>fe(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>fe(e.get(),t).map((e=>{const n=fe(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>fe(o.get(),e),collapse:e=>fe(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return K(re(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=P(i(t).toArray(),(e=>r(e).isSome()));return fe(o.get(),t).bind((t=>{const o=Y(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(q(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>pe(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>He(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},ox=ex,nx=Ne("tiered-menu-item-highlight"),sx=Ne("tiered-menu-item-dehighlight"),rx=y("collapse-item"),ax=nh({name:"TieredMenu",configFields:[yi("onExecute"),yi("onEscape"),xi("onOpenMenu"),xi("onOpenSubmenu"),bi("onRepositionMenu"),bi("onCollapseMenu"),Qr("highlightOnOpen",Ac.HighlightMenuAndItem),Pr("data",[Rr("primary"),Rr("menus"),Rr("expansions")]),Qr("fakeFocus",!1),bi("onHighlightItem"),bi("onDehighlightItem"),bi("onHover"),pi(),Rr("dom"),Qr("navigateOnHover",!0),Qr("stayInDom",!1),qp("tmenuBehaviours",[wm,Gd,zg,Zm]),Qr("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=Xe(),n=tx(),s=e=>Yh.getValue(e).value,r=t=>le(e.data.menus,((e,t)=>q(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Gd.highlight,i=(t,o)=>{a(t,o),Gd.getHighlighted(o).orThunk((()=>Gd.getFirst(o))).each((n=>{e.fakeFocus?Gd.highlight(o,n):zc(t,n.element,za())}))},l=(e,t)=>Pe(L(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));V(s,(o=>{is(o.element,[e.markers.backgroundMenu]),e.stayInDom||Zm.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=ms(t.element,`.${e.markers.item}`),a=P(r,(e=>"true"===qo(e,"aria-haspopup")));return V(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);ie(r,((e,t)=>{const o=F(n,t);Go(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return V(a,(t=>{os(t.element,e.markers.backgroundMenu)})),sn(r.element)||Zm.append(t,Mg(r)),is(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(tp)&&tp.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return sn(l.element)||Zm.append(t,Mg(l)),e.onOpenSubmenu(t,o,l,Y(s)),r===m.HighlightSubmenu?(Gd.highlightFirst(l),u(t,n,s)):(Gd.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>Yn(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Hc([Wc(Jv(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Gd.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),ed(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Jc(((t,o)=>{(t=>{const o=((t,o,n)=>le(n,((n,s)=>{const r=()=>Qv.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Rc(e,nx,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Rc(e,sx,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?Xd():qd()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Zm.append(t,Mg(o)),e.onOpenMenu(t,o),e.highlightOnOpen===Ac.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===Ac.HighlightJustMenu&&a(t,o)}))})),Wc(nx,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),Wc(sx,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[Wc(Vv(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Gd.getHighlighted(e).bind(Gd.getHighlighted),x={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=he(n.getMenus()),r=Pe(L(o,ox));return n.getTriggeringPath(t,(e=>((e,t,o)=>se(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Gd.getCandidates(e);return $(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===qo(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Yp(e.tmenuBehaviours,[wm.config({mode:"special",onRight:h(((e,t)=>Zd(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>Zd(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{zc(e,t.element,za())}))}}),Gd.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),zg.config({find:e=>Gd.getHighlighted(e)}),Zm.config({})]),eventOrder:e.eventOrder,apis:x,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:ia(e,t),expansions:{}}),collapseItem:e=>({value:Ne(rx()),meta:{text:e}})}}),ix=y("sink"),lx=y(Zi({name:ix(),overrides:y({dom:{tag:"div"},behaviours:rd([Zf.config({useFixed:E})]),events:Hc([Xc(wa()),Xc(pa()),Xc(Oa())])})})),cx=br([Qr("isExtraPart",T),Jr("fireEventInstead",[Qr("event",Ya())])]),dx=e=>{const t=Er("Dismissal",cx,e);return{[gv()]:{schema:br([Rr("target")]),onReceive:(e,o)=>{bb.isOpen(e)&&(bb.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>bb.close(e)),(t=>Fc(e,t.event))))}}}},ux=br([Jr("fireEventInstead",[Qr("event",Ka())]),Hr("doReposition")]),mx=e=>{const t=Er("Reposition",ux,e);return{[pv()]:{onReceive:e=>{bb.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>Fc(e,t.event)))}}}},gx=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},px=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=bx(n,e);return i.map((t=>t.bind((t=>{const i=t.menus[t.primary];return A.from(i).each((t=>{e.listRole.each((e=>{t.role=e}))})),A.from(ax.sketch({...r.menu(),uid:lg(""),data:t,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();Zf.position(n,t,{anchor:o}),bb.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();Zf.position(n,o,{anchor:{type:"submenu",item:t}}),bb.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();Zf.position(s,t,{anchor:o}),V(n,(e=>{Zf.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(fd.focus(n),bb.close(s),A.some(!0))}))}))))})(e,t,gx(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{bb.isOpen(n)&&bb.close(n)}),(e=>{bb.cloak(n),bb.open(n,e),r(n)})),n)))},hx=(e,t,o,n,s,r,a)=>(bb.close(n),_e(n)),fx=(e,t,o,n,s,r)=>{const a=Pg.getCoupled(o,"sandbox");return(bb.isOpen(a)?hx:px)(e,t,o,a,n,s,r)},bx=(e,t)=>e.getSystem().getByUid(t.uid+"-"+ix()).map((e=>()=>Ae.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>Ae.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),vx=e=>{bb.getState(e).each((e=>{ax.repositionMenus(e)}))},xx=(e,t,o)=>{const n=Bv(),s=bx(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id}},behaviours:Jp(e.sandboxBehaviours,[Yh.config({store:{mode:"memory",initialValue:t}}),bb.config({onOpen:(s,r)=>{const a=gx(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=zg.getCurrent(t).getOr(t),s=Tn(e.element);o?dn(n.element,"min-width",s+"px"):((e,t)=>{On.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,r)=>{n.unlink(t.element),s().getOr(r).element.dom.dispatchEvent(new window.FocusEvent("focusout")),void 0!==o&&void 0!==o.onClose&&o.onClose(e,r)},isPartOf:(e,o,n)=>Iv(o,n)||Iv(t,n),getAttachPoint:()=>s().getOrDie()}),zg.config({find:e=>bb.getState(e).bind((e=>zg.getCurrent(e)))}),ob.config({channels:{...dx({isExtraPart:T}),...mx({doReposition:vx})}})])}},yx=e=>{const t=Pg.getCoupled(e,"sandbox");vx(t)},wx=()=>[Qr("sandboxClasses",[]),Kp("sandboxBehaviours",[zg,ob,bb,Yh])],Sx=y([Rr("dom"),Rr("fetch"),bi("onOpen"),vi("onExecute"),Qr("getHotspot",A.some),Qr("getAnchorOverrides",y({})),hf(),qp("dropdownBehaviours",[Yb,Pg,wm,fd]),Rr("toggleClass"),Qr("eventOrder",{}),$r("lazySink"),Qr("matchWidth",!1),Qr("useMinWidth",!1),$r("role"),$r("listRole")].concat(wx())),Cx=y([Qi({schema:[pi(),Qr("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),lx()]),kx=sh({name:"Dropdown",configFields:Sx(),partFields:Cx(),factory:(e,t,o,n)=>{const s=e=>{bb.getState(e).each((e=>{ax.highlightPrimary(e)}))},r=(t,o,s)=>fx(e,w,t,n,o,s),a={expand:e=>{Yb.isOn(e)||r(e,b,Ac.HighlightNone).get(b)},open:e=>{Yb.isOn(e)||r(e,b,Ac.HighlightMenuAndItem).get(b)},refetch:t=>Pg.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,Ac.HighlightMenuAndItem).map(b)),(o=>px(e,w,t,o,n,b,Ac.HighlightMenuAndItem).map(b))),isOpen:Yb.isOn,close:e=>{Yb.isOn(e)&&r(e,b,Ac.HighlightMenuAndItem).get(b)},repositionMenus:e=>{Yb.isOn(e)&&yx(e)}},i=(e,t)=>(Nc(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:Yp(e.dropdownBehaviours,[Yb.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),Pg.config({others:{sandbox:t=>xx(e,t,{onOpen:()=>Yb.on(t),onClose:()=>Yb.off(t)})}}),wm.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(kx.isOpen(e)){const t=Pg.getCoupled(e,"sandbox");s(t)}else kx.open(e);return A.some(!0)},onEscape:(e,t)=>kx.isOpen(e)?(kx.close(e),A.some(!0)):A.none()}),fd.config({})]),events:Ov(A.some((e=>{r(e,s,Ac.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[Na()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":e.listRole.getOr("true"),...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:fe(e.dom,"attributes").bind((e=>fe(e,"type"))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),Ox="form",_x=[qp("formBehaviours",[Yh])],Tx=e=>"<alloy.field."+e+">",Ex=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Yp(e.formBehaviours,[Yh.config({store:{mode:"manual",getValue:t=>{const o=pl(t,e);return le(o,((e,t)=>e().bind((e=>{return o=zg.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+si(e.element)),o.fold((()=>Ae.error(n)),Ae.value);var o,n})).map(Yh.getValue)))},setValue:(t,o)=>{ie(o,((o,n)=>{ul(t,e,n).each((e=>{zg.getCurrent(e).each((e=>{Yh.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>ul(t,e,o).bind(zg.getCurrent)}}),Ax={getField:Om(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),al(Ox,Tx(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>Ji({name:e,pname:Tx(e)})));return Zp(Ox,_x,s,Ex,o)}},Mx=y([Rr("dom"),Qr("shell",!0),qp("toolbarBehaviours",[Zm])]),Dx=y([Zi({name:"groups",overrides:()=>({behaviours:rd([Zm.config({})])})})]),Bx=sh({name:"Toolbar",configFields:Mx(),partFields:Dx(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Zm.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Yp(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):ul(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Zm.set(e,o)}))},refresh:b},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),Ix=y([hi(["toggledClass"]),Rr("lazySink"),Hr("fetch"),Yr("getBounds"),Jr("fireDismissalEventInstead",[Qr("event",Ya())]),hf(),bi("onToggled")]),Fx=y([Qi({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:rd([Yb.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),Qi({factory:Bx,schema:Mx(),name:"toolbar",overrides:e=>({toolbarBehaviours:rd([wm.config({mode:"cyclic",onEscape:t=>(ul(t,e,"button").each(fd.focus),A.none())})])})})]),Rx=Xe(),Nx=(e,t)=>{const o=Pg.getCoupled(e,"toolbarSandbox");bb.isOpen(o)?bb.close(o):bb.open(o,t.toolbar())},zx=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();Zf.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:Oc()}}},s)},Lx=(e,t,o,n,s)=>{Bx.setGroups(t,s),zx(e,t,o,n),Yb.on(e)},Vx=sh({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({..._v.sketch({...n.button(),action:e=>{Nx(e,n)},buttonBehaviours:Jp({dump:n.button().buttonBehaviours},[Pg.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=Bv();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:rd([wm.config({mode:"special",onEscape:e=>(bb.close(e),A.some(!0))}),bb.config({onOpen:(s,r)=>{const a=Rx.get().getOr(!1);o.fetch().get((s=>{Lx(e,r,o,t.layouts,s),n.link(e.element),a||wm.focusIn(r)}))},onClose:()=>{Yb.off(e),Rx.get().getOr(!1)||fd.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>Iv(o,n)||Iv(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),ob.config({channels:{...dx({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...mx({doReposition:()=>{bb.getState(Pg.getCoupled(e,"toolbarSandbox")).each((n=>{zx(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{bb.getState(Pg.getCoupled(t,"toolbarSandbox")).each((s=>{Lx(t,s,e,o.layouts,n)}))},reposition:t=>{bb.getState(Pg.getCoupled(t,"toolbarSandbox")).each((n=>{zx(t,n,e,o.layouts)}))},toggle:e=>{Nx(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{Rx.set(!0),Nx(e,t),Rx.clear()})(e,n)},getToolbar:e=>bb.getState(Pg.getCoupled(e,"toolbarSandbox")),isOpen:e=>bb.isOpen(Pg.getCoupled(e,"toolbarSandbox"))}}),configFields:Ix(),partFields:Fx(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),Hx=y([Qr("prefix","form-field"),qp("fieldBehaviours",[zg,Yh])]),Px=y([Zi({schema:[Rr("dom")],name:"label"}),Zi({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Rr("text")],name:"aria-descriptor"}),Ji({factory:{sketch:e=>{const t=((e,t)=>{const o={};return ie(e,((e,n)=>{F(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[Rr("factory")],name:"field"})]),Ux=sh({name:"FormField",configFields:Hx(),partFields:Px(),factory:(e,t,o,n)=>{const s=Yp(e.fieldBehaviours,[zg.config({find:t=>ul(t,e,"field")}),Yh.config({store:{mode:"manual",getValue:e=>zg.getCurrent(e).bind(Yh.getValue),setValue:(e,t)=>{zg.getCurrent(e).each((e=>{Yh.setValue(e,t)}))}}})]),r=Hc([Jc(((t,o)=>{const n=gl(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=Ne(e.prefix);n.label().each((e=>{Go(e.element,"for",o),Go(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=Ne(e.prefix);Go(o.element,"id",n),Go(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>ul(t,e,"field"),getLabel:t=>ul(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}}),Wx=y([Qr("field1Name","field1"),Qr("field2Name","field2"),xi("onLockedChange"),hi(["lockClass"]),Qr("locked",!1),Kp("coupledFieldBehaviours",[zg,Yh]),sa("onInput",b)]),$x=(e,t)=>Ji({factory:Ux,name:e,overrides:e=>({fieldBehaviours:rd([ud("coupled-input-behaviour",[Wc(Ca(),(o=>{((e,t,o)=>ul(e,t,o).bind(zg.getCurrent))(o,e,t).each((t=>{ul(o,e,"lock").each((n=>{Yb.isOn(n)&&e.onLockedChange(o,t,n),e.onInput(o)}))}))}))])])})}),Gx=y([$x("field1","field2"),$x("field2","field1"),Ji({factory:_v,schema:[Rr("dom")],name:"lock",overrides:e=>({buttonBehaviours:rd([Yb.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),jx=sh({name:"FormCoupledInputs",configFields:Wx(),partFields:Gx(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Jp(e.coupledFieldBehaviours,[zg.config({find:A.some}),Yh.config({store:{mode:"manual",getValue:t=>{const o=fl(t,e,["field1","field2"]);return{[e.field1Name]:Yh.getValue(o.field1()),[e.field2Name]:Yh.getValue(o.field2())}},setValue:(t,o)=>{const n=fl(t,e,["field1","field2"]);ve(o,e.field1Name)&&Yh.setValue(n.field1(),o[e.field1Name]),ve(o,e.field2Name)&&Yh.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>ul(t,e,"field1"),getField2:t=>ul(t,e,"field2"),getLock:t=>ul(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),qx=nh({name:"HtmlSelect",configFields:[Rr("options"),qp("selectBehaviours",[fd,Yh]),Qr("selectClasses",[]),Qr("selectAttributes",{}),$r("data")],factory:(e,t)=>{const o=L(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>ia("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:Yp(e.selectBehaviours,[fd.config({}),Yh.config({store:{mode:"manual",getValue:e=>cs(e.element),setValue:(t,o)=>{const n=te(e.options);$(e.options,(e=>e.value===o)).isSome()?ds(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>ds(t.element,e.value)))},...n}})])}}}),Xx=nh({name:"InlineView",configFields:[Rr("lazySink"),bi("onShow"),bi("onHide"),Yr("onEscape"),qp("inlineBehaviours",[bb,Yh,ob]),Jr("fireDismissalEventInstead",[Qr("event",Ya())]),Jr("fireRepositionEventInstead",[Qr("event",Ka())]),Qr("getRelated",A.none),Qr("isExtraPart",T),Qr("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();bb.openWhileCloaked(t,o,(()=>Zf.positionWithinBounds(r,t,n,s()))),Yh.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>Zl(),onRtl:()=>ec()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return ax.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(bb.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{Zf.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();Zf.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();Zf.positionWithinBounds(a,t,o,s()),V(n,(e=>{const t=i(e.triggeringPath);Zf.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);bb.open(t,r),Yh.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{bb.isOpen(t)&&Yh.getValue(t).each((o=>{switch(o.mode){case"menu":bb.getState(t).each(ax.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();Zf.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{bb.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{bb.isOpen(e)&&(Yh.setValue(e,A.none()),bb.close(e))},getContent:e=>bb.getState(e),reposition:s,isOpen:bb.isOpen};return{uid:e.uid,dom:e.dom,behaviours:Yp(e.inlineBehaviours,[bb.config({isPartOf:(t,o,n)=>Iv(o,n)||((t,o)=>e.getRelated(t).exists((e=>Iv(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),Yh.config({store:{mode:"memory",initialValue:A.none()}}),ob.config({channels:{...dx({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...mx({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}}),Yx=y([ta("type","text"),$r("data"),Qr("inputAttributes",{}),Qr("inputStyles",{}),Qr("tag","input"),Qr("inputClasses",[]),bi("onSetValue"),sa("fromInputValue",w),sa("toInputValue",w),Qr("styles",{}),Qr("eventOrder",{}),qp("inputBehaviours",[Yh,fd]),Qr("selectOnFocus",!0)]),Kx=e=>rd([fd.config({onFocus:e.selectOnFocus?t=>{const o=t.element,n=cs(o);"range"!==e.type&&o.dom.setSelectionRange(0,n.length)}:b})]),Jx=e=>({...Kx(e),...Yp(e.inputBehaviours,[Yh.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:t=>e.fromInputValue(cs(t.element)),setValue:(t,o)=>{cs(t.element)!==o&&ds(t.element,e.toInputValue(o))}},onSetValue:e.onSetValue})])}),Qx=e=>({tag:e.tag,attributes:{type:e.type,...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Zx=nh({name:"Input",configFields:Yx(),factory:(e,t)=>({uid:e.uid,dom:Qx(e),components:[],behaviours:Jx(e),eventOrder:e.eventOrder})}),ey=sl(Gv(),jv()),ty=y([Rr("lazySink"),$r("dragBlockClass"),sa("getBounds",Ls),Qr("useTabstopAt",E),Qr("firstTabstop",0),Qr("eventOrder",{}),qp("modalBehaviours",[wm]),vi("onExecute"),yi("onEscape")]),oy={sketch:w},ny=y([Zi({name:"draghandle",overrides:(e,t)=>({behaviours:rd([Th.config({mode:"mouse",getTarget:e=>Gn(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),Ji({schema:[Rr("dom")],name:"title"}),Ji({factory:oy,schema:[Rr("dom")],name:"close"}),Ji({factory:oy,schema:[Rr("dom")],name:"body"}),Zi({factory:oy,schema:[Rr("dom")],name:"footer"}),Qi({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[Qr("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),Qr("components",[])],name:"blocker"})]),sy=sh({name:"ModalDialog",configFields:ty(),partFields:ny(),factory:(e,t,o,n)=>{const s=Xe(),r=Ne("modal-events"),a={...e.eventOrder,[qa()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])},i=Jt();return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([Mg(t)]),behaviours:rd([fd.config({}),ud("dialog-blocker-events",[Kc(xa(),(()=>{Fg.isBlocked(t)||wm.focusIn(t)}))])])});Bm(o,a),wm.focusIn(t)},hide:e=>{s.clear(),go(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{Rm(e)}))}))},getBody:t=>ml(t,e,"body"),getFooter:t=>ul(t,e,"footer"),setIdle:e=>{Fg.unblock(e)},setBusy:(e,t)=>{Fg.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Yp(e.modalBehaviours,[Zm.config({}),wm.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),Fg.config({getRoot:s.get}),ud(r,[Jc((t=>{const o=ml(t,e,"title").element,n=(e=>e.dom.textContent)(o);i.os.isMacOS()&&g(n)?Go(t.element,"aria-label",n):((e,t)=>{const o=Xo(e,"id").fold((()=>{const e=Ne("dialog-label");return Go(t,"id",e),e}),w);Go(e,"aria-labelledby",o)})(t.element,o)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),ry=Zi({schema:[Rr("dom")],name:"label"}),ay=e=>Zi({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Hc([$c(da(),((t,o,n)=>e(t,n)),[t]),$c(pa(),((t,o,n)=>e(t,n)),[t]),$c(ha(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),iy=ay("top-left"),ly=ay("top"),cy=ay("top-right"),dy=ay("right"),uy=ay("bottom-right"),my=ay("bottom"),gy=ay("bottom-left"),py=ay("left"),hy=Ji({name:"thumb",defaults:y({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Hc([jc(da(),e,"spectrum"),jc(ua(),e,"spectrum"),jc(ma(),e,"spectrum"),jc(pa(),e,"spectrum"),jc(ha(),e,"spectrum"),jc(ba(),e,"spectrum")])})}),fy=e=>Bd(e.event),by=Ji({schema:[Ir("mouseIsDown",(()=>ye(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:rd([wm.config({mode:"special",onLeft:(o,n)=>t.onLeft(o,e,fy(n)),onRight:(o,n)=>t.onRight(o,e,fy(n)),onUp:(o,n)=>t.onUp(o,e,fy(n)),onDown:(o,n)=>t.onDown(o,e,fy(n))}),Pb.config({}),fd.config({})]),events:Hc([Wc(da(),o),Wc(ua(),o),Wc(pa(),o),Wc(ha(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}});var vy=[ry,py,dy,ly,my,iy,cy,gy,uy,hy,by];const xy=y("slider.change.value"),yy=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>Dn(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>Dn(e.clientX,e.clientY))):A.none()}},wy=e=>e.model.minX,Sy=e=>e.model.minY,Cy=e=>e.model.minX-1,ky=e=>e.model.minY-1,Oy=e=>e.model.maxX,_y=e=>e.model.maxY,Ty=e=>e.model.maxX+1,Ey=e=>e.model.maxY+1,Ay=(e,t,o)=>t(e)-o(e),My=e=>Ay(e,Oy,wy),Dy=e=>Ay(e,_y,Sy),By=e=>My(e)/2,Iy=e=>Dy(e)/2,Fy=(e,t)=>t?e.stepSize*e.speedMultiplier:e.stepSize,Ry=e=>e.snapToGrid,Ny=e=>e.snapStart,zy=e=>e.rounded,Ly=(e,t)=>void 0!==e[t+"-edge"],Vy=e=>Ly(e,"left"),Hy=e=>Ly(e,"right"),Py=e=>Ly(e,"top"),Uy=e=>Ly(e,"bottom"),Wy=e=>e.model.value.get(),$y=(e,t)=>({x:e,y:t}),Gy=(e,t)=>{Rc(e,xy(),{value:t})},jy=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),qy=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),Xy=(e,t,o)=>Math.max(t,Math.min(o,e)),Yy=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=Xy(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return Xy(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},Ky=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},Jy="top",Qy="right",Zy="bottom",ew="left",tw=e=>e.element.dom.getBoundingClientRect(),ow=(e,t)=>e[t],nw=e=>{const t=tw(e);return ow(t,ew)},sw=e=>{const t=tw(e);return ow(t,Qy)},rw=e=>{const t=tw(e);return ow(t,Jy)},aw=e=>{const t=tw(e);return ow(t,Zy)},iw=e=>{const t=tw(e);return ow(t,"width")},lw=e=>{const t=tw(e);return ow(t,"height")},cw=(e,t,o)=>(e+t)/2-o,dw=(e,t)=>{const o=tw(e),n=tw(t),s=ow(o,ew),r=ow(o,Qy),a=ow(n,ew);return cw(s,r,a)},uw=(e,t)=>{const o=tw(e),n=tw(t),s=ow(o,Jy),r=ow(o,Zy),a=ow(n,Jy);return cw(s,r,a)},mw=(e,t)=>{Rc(e,xy(),{value:t})},gw=(e,t,o)=>{const n={min:wy(t),max:Oy(t),range:My(t),value:o,step:Fy(t),snap:Ry(t),snapStart:Ny(t),rounded:zy(t),hasMinEdge:Vy(t),hasMaxEdge:Hy(t),minBound:nw(e),maxBound:sw(e),screenRange:iw(e)};return Yy(n)},pw=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?qy:jy)(Wy(o),wy(o),Oy(o),Fy(o,n));return mw(t,s),A.some(s)})(e,t,o,n).map(E),hw=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=iw(e),a=n.bind((t=>A.some(dw(t,e)))).getOr(0),i=s.bind((t=>A.some(dw(t,e)))).getOr(r),l={min:wy(t),max:Oy(t),range:My(t),value:o,hasMinEdge:Vy(t),hasMaxEdge:Hy(t),minBound:nw(e),minOffset:0,maxBound:sw(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return Ky(l)})(t,r,o,n,s);return nw(t)-nw(e)+a},fw=pw(-1),bw=pw(1),vw=A.none,xw=A.none,yw={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{Gy(e,Ty(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{Gy(e,Cy(t))}))};var ww=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=gw(e,t,o);return mw(e,n),n},setToMin:(e,t)=>{const o=wy(t);mw(e,o)},setToMax:(e,t)=>{const o=Oy(t);mw(e,o)},findValueOfOffset:gw,getValueFromEvent:e=>yy(e).map((e=>e.left)),findPositionOfValue:hw,setPositionFromValue:(e,t,o,n)=>{const s=Wy(o),r=hw(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Tn(t.element)/2;dn(t.element,"left",r-a+"px")},onLeft:fw,onRight:bw,onUp:vw,onDown:xw,edgeActions:yw});const Sw=(e,t)=>{Rc(e,xy(),{value:t})},Cw=(e,t,o)=>{const n={min:Sy(t),max:_y(t),range:Dy(t),value:o,step:Fy(t),snap:Ry(t),snapStart:Ny(t),rounded:zy(t),hasMinEdge:Py(t),hasMaxEdge:Uy(t),minBound:rw(e),maxBound:aw(e),screenRange:lw(e)};return Yy(n)},kw=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?qy:jy)(Wy(o),Sy(o),_y(o),Fy(o,n));return Sw(t,s),A.some(s)})(e,t,o,n).map(E),Ow=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=lw(e),a=n.bind((t=>A.some(uw(t,e)))).getOr(0),i=s.bind((t=>A.some(uw(t,e)))).getOr(r),l={min:Sy(t),max:_y(t),range:Dy(t),value:o,hasMinEdge:Py(t),hasMaxEdge:Uy(t),minBound:rw(e),minOffset:0,maxBound:aw(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return Ky(l)})(t,r,o,n,s);return rw(t)-rw(e)+a},_w=A.none,Tw=A.none,Ew=kw(-1),Aw=kw(1),Mw={"top-left":A.none(),top:A.some(((e,t)=>{Gy(e,ky(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{Gy(e,Ey(t))})),"bottom-left":A.none(),left:A.none()};var Dw=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=Cw(e,t,o);return Sw(e,n),n},setToMin:(e,t)=>{const o=Sy(t);Sw(e,o)},setToMax:(e,t)=>{const o=_y(t);Sw(e,o)},findValueOfOffset:Cw,getValueFromEvent:e=>yy(e).map((e=>e.top)),findPositionOfValue:Ow,setPositionFromValue:(e,t,o,n)=>{const s=Wy(o),r=Ow(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=Sn(t.element)/2;dn(t.element,"top",r-a+"px")},onLeft:_w,onRight:Tw,onUp:Ew,onDown:Aw,edgeActions:Mw});const Bw=(e,t)=>{Rc(e,xy(),{value:t})},Iw=(e,t)=>({x:e,y:t}),Fw=(e,t)=>(o,n,s)=>((e,t,o,n,s)=>{const r=e>0?qy:jy,a=t?Wy(n).x:r(Wy(n).x,wy(n),Oy(n),Fy(n,s)),i=t?r(Wy(n).y,Sy(n),_y(n),Fy(n,s)):Wy(n).y;return Bw(o,Iw(a,i)),A.some(a)})(e,t,o,n,s).map(E),Rw=Fw(-1,!1),Nw=Fw(1,!1),zw=Fw(-1,!0),Lw=Fw(1,!0),Vw={"top-left":A.some(((e,t)=>{Gy(e,$y(Cy(t),ky(t)))})),top:A.some(((e,t)=>{Gy(e,$y(By(t),ky(t)))})),"top-right":A.some(((e,t)=>{Gy(e,$y(Ty(t),ky(t)))})),right:A.some(((e,t)=>{Gy(e,$y(Ty(t),Iy(t)))})),"bottom-right":A.some(((e,t)=>{Gy(e,$y(Ty(t),Ey(t)))})),bottom:A.some(((e,t)=>{Gy(e,$y(By(t),Ey(t)))})),"bottom-left":A.some(((e,t)=>{Gy(e,$y(Cy(t),Ey(t)))})),left:A.some(((e,t)=>{Gy(e,$y(Cy(t),Iy(t)))}))};var Hw=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=gw(e,t,o.left),s=Cw(e,t,o.top),r=Iw(n,s);return Bw(e,r),r},setToMin:(e,t)=>{const o=wy(t),n=Sy(t);Bw(e,Iw(o,n))},setToMax:(e,t)=>{const o=Oy(t),n=_y(t);Bw(e,Iw(o,n))},getValueFromEvent:e=>yy(e),setPositionFromValue:(e,t,o,n)=>{const s=Wy(o),r=hw(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=Ow(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Tn(t.element)/2,l=Sn(t.element)/2;dn(t.element,"left",r-i+"px"),dn(t.element,"top",a-l+"px")},onLeft:Rw,onRight:Nw,onUp:zw,onDown:Lw,edgeActions:Vw});const Pw=sh({name:"Slider",configFields:[Qr("stepSize",1),Qr("speedMultiplier",10),Qr("onChange",b),Qr("onChoose",b),Qr("onInit",b),Qr("onDragStart",b),Qr("onDragEnd",b),Qr("snapToGrid",!1),Qr("rounded",!0),$r("snapStart"),Nr("model",Mr("mode",{x:[Qr("minX",0),Qr("maxX",100),Ir("value",(e=>ye(e.mode.minX))),Rr("getInitialValue"),wi("manager",ww)],y:[Qr("minY",0),Qr("maxY",100),Ir("value",(e=>ye(e.mode.minY))),Rr("getInitialValue"),wi("manager",Dw)],xy:[Qr("minX",0),Qr("maxX",100),Qr("minY",0),Qr("maxY",100),Ir("value",(e=>ye({x:e.mode.minX,y:e.mode.minY}))),Rr("getInitialValue"),wi("manager",Hw)]})),qp("sliderBehaviours",[wm,Yh]),Ir("mouseIsDown",(()=>ye(!1)))],partFields:vy,factory:(e,t,o,n)=>{const s=t=>ml(t,e,"thumb"),r=t=>ml(t,e,"spectrum"),a=t=>ul(t,e,"left-edge"),i=t=>ul(t,e,"right-edge"),l=t=>ul(t,e,"top-edge"),c=t=>ul(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&ul(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)},b=t=>{ul(t,e,"spectrum").map(wm.focusIn)};return{uid:e.uid,dom:e.dom,components:t,behaviours:Yp(e.sliderBehaviours,[wm.config({mode:"special",focusIn:b}),Yh.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),ob.config({channels:{[hv()]:{onReceive:p}}})]),events:Hc([Wc(xy(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),Jc(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Wc(da(),h),Wc(ma(),f),Wc(pa(),((e,t)=>{b(e),h(e,t)})),Wc(ba(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),Uw="container",Ww=[qp("slotBehaviours",[])],$w=e=>"<alloy.field."+e+">",Gw=(e,t)=>{const o=t=>hl(e),n=(t,o)=>(n,s)=>ul(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==qo(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;dn(o,"display","none"),Go(o,"aria-hidden","true"),Rc(e,Qa(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{V(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;vn(o,"display"),Ko(o,"aria-hidden"),Rc(e,Qa(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>ul(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:Xp(e.slotBehaviours),apis:c}},jw=le({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>Om(e))),qw={...jw,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),al(Uw,$w(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>Ji({name:e,pname:$w(e)})));return Zp(Uw,Ww,s,Gw,o)}},Xw=y([Rr("toggleClass"),Rr("fetch"),xi("onExecute"),Qr("getHotspot",A.some),Qr("getAnchorOverrides",y({})),hf(),xi("onItemExecute"),$r("lazySink"),Rr("dom"),bi("onOpen"),qp("splitDropdownBehaviours",[Pg,wm,fd]),Qr("matchWidth",!1),Qr("useMinWidth",!1),Qr("eventOrder",{}),$r("role"),$r("listRole")].concat(wx())),Yw=Ji({factory:_v,schema:[Rr("dom")],name:"arrow",defaults:()=>({buttonBehaviours:rd([fd.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(Nc)},buttonBehaviours:rd([Yb.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),Kw=Ji({factory:_v,schema:[Rr("dom")],name:"button",defaults:()=>({buttonBehaviours:rd([fd.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),Jw=y([Yw,Kw,Zi({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Rr("text")],name:"aria-descriptor"}),Qi({schema:[pi()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),lx()]),Qw=sh({name:"SplitDropdown",configFields:Xw(),partFields:Jw(),factory:(e,t,o,n)=>{const s=e=>{zg.getCurrent(e).each((e=>{Gd.highlightFirst(e),wm.focusIn(e)}))},r=t=>{fx(e,w,t,n,s,Ac.HighlightMenuAndItem).get(b)},a=t=>{const o=ml(t,e,"button");return Nc(o),A.some(!0)},i={...Hc([Jc(((t,o)=>{ul(t,e,"aria-descriptor").each((e=>{const o=Ne("aria");Go(e.element,"id",o),Go(t.element,"aria-describedby",o)}))}))]),...Ov(A.some(r))},l={repositionMenus:e=>{Yb.isOn(e)&&yx(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[Na()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:Yp(e.splitDropdownBehaviours,[Pg.config({others:{sandbox:t=>{const o=ml(t,e,"arrow");return xx(e,t,{onOpen:()=>{Yb.on(o),Yb.on(t)},onClose:()=>{Yb.off(o),Yb.off(t)}})}}}),wm.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),A.some(!0))}),fd.config({}),Yb.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),Zw=(e,t,o)=>({within:e,extra:t,withinWidth:o}),eS=(e,t,o)=>{const n=W(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(y(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=P(n,(e=>e.finish<=t)),r=U(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},tS=e=>L(e,(e=>e.element)),oS=(e,t)=>{const o=L(t,(e=>Mg(e)));Bx.setGroups(e,o)},nS=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=ml(e,t,"primary"),r=Pg.getCoupled(e,"overflowGroup");dn(s.element,"visibility","hidden");const a=n.concat([r]),i=se(a,(e=>Lo(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),oS(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=eS(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>eS(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=tS(e.concat(t));return Zw(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=tS(e).concat([o]);return Zw(s,tS(t),n)})(r,a,n,i):((e,t,o)=>Zw(tS(e),[],o))(r,0,i)})(Tn(s.element),t.builtGroups.get(),(e=>Math.ceil(e.element.dom.getBoundingClientRect().width)),r);0===l.extra.length?(Zm.remove(s,r),o([])):(oS(s,l.within),o(l.extra)),vn(s.element,"visibility"),xn(s.element),i.each(fd.focus)},sS=y([qp("splitToolbarBehaviours",[Pg]),Ir("builtGroups",(()=>ye([])))]),rS=y([hi(["overflowToggledClass"]),Yr("getOverflowBounds"),Rr("lazySink"),Ir("overflowGroups",(()=>ye([]))),bi("onOpened"),bi("onClosed")].concat(sS())),aS=y([Ji({factory:Bx,schema:Mx(),name:"primary"}),Qi({schema:Mx(),name:"overflow"}),Qi({name:"overflow-button"}),Qi({name:"overflow-group"})]),iS=y([Rr("items"),hi(["itemSelector"]),qp("tgroupBehaviours",[wm])]),lS=y([el({name:"items",unit:"item"})]),cS=sh({name:"ToolbarGroup",configFields:iS(),partFields:lS(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Yp(e.tgroupBehaviours,[wm.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),dS=e=>L(e,(e=>Mg(e))),uS=(e,t,o)=>{nS(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{Vx.setGroups(e,dS(n))}))}))},mS=sh({name:"SplitFloatingToolbar",configFields:rS(),partFields:aS(),factory:(e,t,o,n)=>{const s=dv(Vx.sketch({fetch:()=>Oe((t=>{t(dS(e.overflowGroups.get()))})),layouts:{onLtr:()=>[Ul,Pl],onRtl:()=>[Pl,Ul],onBottomLtr:()=>[$l,Wl],onBottomRtl:()=>[Wl,$l]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Yp(e.splitToolbarBehaviours,[Pg.config({others:{overflowGroup:()=>cS.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(L(o,t.getSystem().build)),uS(t,s,e)},refresh:t=>uS(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{Vx.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(Vx.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(Vx.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{Vx.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(Vx.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),gS=y([hi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),bi("onOpened"),bi("onClosed")].concat(sS())),pS=y([Ji({factory:Bx,schema:Mx(),name:"primary"}),Ji({factory:Bx,schema:Mx(),name:"overflow",overrides:e=>({toolbarBehaviours:rd([Ib.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{ul(t,e,"overflow-button").each((e=>{Yb.off(e)})),e.onClosed(t)},onGrown:t=>{e.onOpened(t)},onStartGrow:t=>{ul(t,e,"overflow-button").each(Yb.on)}}),wm.config({mode:"acyclic",onEscape:t=>(ul(t,e,"overflow-button").each(fd.focus),A.some(!0))})])})}),Qi({name:"overflow-button",overrides:e=>({buttonBehaviours:rd([Yb.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])})}),Qi({name:"overflow-group"})]),hS=(e,t,o)=>{ul(e,t,"overflow-button").each((n=>{ul(e,t,"overflow").each((s=>{if(fS(e,t),Ib.hasShrunk(s)){const e=t.onOpened;t.onOpened=n=>{o||wm.focusIn(s),e(n),t.onOpened=e}}else{const e=t.onClosed;t.onClosed=s=>{o||fd.focus(n),e(s),t.onClosed=e}}Ib.toggleGrow(s)}))}))},fS=(e,t)=>{ul(e,t,"overflow").each((o=>{nS(e,t,(e=>{const t=L(e,(e=>Mg(e)));Bx.setGroups(o,t)})),ul(e,t,"overflow-button").each((e=>{Ib.hasGrown(o)&&Yb.on(e)})),Ib.refresh(o)}))},bS=sh({name:"SplitSlidingToolbar",configFields:gS(),partFields:pS(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:Yp(e.splitToolbarBehaviours,[Pg.config({others:{overflowGroup:e=>cS.sketch({...n["overflow-group"](),items:[_v.sketch({...n["overflow-button"](),action:t=>{Fc(e,s)}})]})}}),ud("toolbar-toggle-events",[Wc(s,(t=>{hS(t,e,!1)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=L(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),fS(t,e)},refresh:t=>fS(t,e),toggle:t=>{hS(t,e,!1)},toggleWithoutFocusing:t=>{hS(t,e,!0)},isOpen:t=>((e,t)=>ul(e,t,"overflow").map(Ib.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),vS=nh({name:"TabButton",configFields:[Qr("uid",void 0),Rr("value"),Br("dom","dom",cr((()=>({attributes:{role:"tab",id:Ne("aria"),"aria-selected":"false"}}))),Zs()),$r("action"),Qr("domModification",{}),qp("tabButtonBehaviours",[fd,wm,Yh]),Rr("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:Ov(e.action),behaviours:Yp(e.tabButtonBehaviours,[fd.config({}),wm.config({mode:"execution",useSpace:!0,useEnter:!0}),Yh.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),xS=y([Rr("tabs"),Rr("dom"),Qr("clickToDismiss",!1),qp("tabbarBehaviours",[Gd,wm]),hi(["tabClass","selectedClass"])]),yS=el({factory:vS,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Gd.dehighlight(e,t),Rc(e,ti(),{tabbar:e,button:t})},o=(e,t)=>{Gd.highlight(e,t),Rc(e,ei(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Gd.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),wS=y([yS]),SS=sh({name:"Tabbar",configFields:xS(),partFields:wS(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Yp(e.tabbarBehaviours,[Gd.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{Go(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{Go(t.element,"aria-selected","false")}}),wm.config({mode:"flow",getInitial:e=>Gd.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),CS=nh({name:"Tabview",configFields:[qp("tabviewBehaviours",[Zm])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:Yp(e.tabviewBehaviours,[Zm.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),kS=y([Qr("selectFirst",!0),bi("onChangeTab"),bi("onDismissTab"),Qr("tabs",[]),qp("tabSectionBehaviours",[])]),OS=Ji({factory:SS,schema:[Rr("dom"),Pr("markers",[Rr("tabClass"),Rr("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),_S=Ji({factory:CS,name:"tabview"}),TS=y([OS,_S]),ES=sh({name:"TabSection",configFields:kS(),partFields:TS(),factory:(e,t,o,n)=>{const s=(t,o)=>{ul(t,e,"tabbar").each((e=>{o(e).each(Nc)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:Xp(e.tabSectionBehaviours),events:Hc(j([e.selectFirst?[Jc(((e,t)=>{s(e,Gd.getFirst)}))]:[],[Wc(ei(),((t,o)=>{(t=>{const o=Yh.getValue(t);ul(t,e,"tabview").each((n=>{$(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();Xo(t.element,"id").each((e=>{Go(n.element,"aria-labelledby",e)})),Zm.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Wc(ti(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>ul(t,e,"tabview").map((e=>Zm.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Gd.getCandidates(e);return $(o,(e=>Yh.getValue(e)===t)).filter((t=>!Gd.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),AS=(e,t,o)=>{const n=Yh.getValue(o);Yh.setValue(t,n),DS(t)},MS=(e,t)=>{const o=e.element,n=cs(o),s=o.dom;"number"!==qo(o,"type")&&t(s,n)},DS=e=>{MS(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},BS=y("alloy.typeahead.itemexecute"),IS=y([$r("lazySink"),Rr("fetch"),Qr("minChars",5),Qr("responseTime",1e3),bi("onOpen"),Qr("getHotspot",A.some),Qr("getAnchorOverrides",y({})),Qr("layouts",A.none()),Qr("eventOrder",{}),aa("model",{},[Qr("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),Qr("selectsOver",!0),Qr("populateFromBrowse",!0)]),bi("onSetValue"),vi("onExecute"),bi("onItemExecute"),Qr("inputClasses",[]),Qr("inputAttributes",{}),Qr("inputStyles",{}),Qr("matchWidth",!0),Qr("useMinWidth",!1),Qr("dismissOnBlur",!0),hi(["openClass"]),$r("initialData"),$r("listRole"),qp("typeaheadBehaviours",[fd,Yh,Lb,wm,Yb,Pg]),Ir("lazyTypeaheadComp",(()=>ye(A.none))),Ir("previewing",(()=>ye(!0)))].concat(Yx()).concat(wx())),FS=y([Qi({schema:[pi()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=Yh.getValue(t),s=e.getDisplayText(n),r=Yh.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{AS(0,t,o),((e,t)=>{MS(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Gd.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&AS(e.model,t,n),Xo(n.element,"id").each((e=>Go(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Rc(e,BS(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&AS(e.model,t,o)}))}})})]),RS=sh({name:"Typeahead",configFields:IS(),partFields:FS(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=Pg.getCoupled(t,"sandbox");if(bb.isOpen(r))zg.getCurrent(r).each((e=>{Gd.getHighlighted(e).fold((()=>{s(e)}),(()=>{Vc(r,e.element,"keydown",o)}))}));else{const o=e=>{zg.getCurrent(e).each(s)};px(e,a(t),t,r,n,o,Ac.HighlightMenuAndItem).get(b)}},r=Kx(e),a=e=>t=>t.map((t=>{const o=he(t.menus),n=q(o,(e=>P(e.items,(e=>"item"===e.type))));return Yh.getState(e).update(L(n,(e=>e.data))),t})),i=e=>zg.getCurrent(e),l="typeaheadevents",c=[fd.config({}),Yh.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>cs(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{ds(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>ia("initialValue",e))).getOr({})}}),Lb.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=Pg.getCoupled(t,"sandbox");if(fd.isFocused(t)&&cs(t.element).length>=e.minChars){const o=i(s).bind((e=>Gd.getHighlighted(e).map(Yh.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Gd.highlightFirst(t)}),(e=>{Gd.highlightBy(t,(t=>Yh.getValue(t).value===e.value)),Gd.getHighlighted(t).orThunk((()=>(Gd.highlightFirst(t),A.none())))}))}))};px(e,a(t),t,s,n,r,Ac.HighlightJustMenu).get(b)}},cancelEvent:Pa()}),wm.config({mode:"special",onDown:(e,t)=>(s(e,t,Gd.highlightFirst),A.some(!0)),onEscape:e=>{const t=Pg.getCoupled(e,"sandbox");return bb.isOpen(t)?(bb.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Gd.highlightLast),A.some(!0)),onEnter:t=>{const o=Pg.getCoupled(t,"sandbox"),n=bb.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Gd.getHighlighted(e))).map((e=>(Rc(t,BS(),{item:e}),!0)));{const s=Yh.getValue(t);return Fc(t,Pa()),e.onExecute(o,t,s),n&&bb.close(o),A.some(!0)}}}),Yb.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),Pg.config({others:{sandbox:t=>xx(e,t,{onOpen:()=>Yb.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>Ko(e.element,"aria-activedescendant"))),Yb.off(t)}})}}),ud(l,[Jc((t=>{e.lazyTypeaheadComp.set(A.some(t))})),Qc((t=>{e.lazyTypeaheadComp.set(A.none())})),ed((t=>{const o=b;fx(e,a(t),t,n,o,Ac.HighlightMenuAndItem).get(b)})),Wc(BS(),((t,o)=>{const n=Pg.getCoupled(t,"sandbox");AS(e.model,t,o.event.item),Fc(t,Pa()),e.onItemExecute(t,n,o.event.item,Yh.getValue(t)),bb.close(n),DS(t)}))].concat(e.dismissOnBlur?[Wc(Ia(),(e=>{const t=Pg.getCoupled(e,"sandbox");Lo(t.element).isNone()&&bb.close(t)}))]:[]))],d={[Xa()]:[Yh.name(),Lb.name(),l],...e.eventOrder};return{uid:e.uid,dom:Qx(Le(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...Yp(e.typeaheadBehaviours,c)},eventOrder:d}}});var NS,zS,LS=tinymce.util.Tools.resolve("tinymce.ThemeManager"),VS=tinymce.util.Tools.resolve("tinymce.util.Delay"),HS=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),PS=tinymce.util.Tools.resolve("tinymce.EditorManager"),US=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(NS||(NS={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(zS||(zS={}));const WS=e=>t=>t.options.get(e),$S=e=>t=>A.from(e(t)),GS=e=>{const t=US.deviceType.isPhone(),o=US.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:HS.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),N(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:zS.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!US.deviceType.isTouch()}),n("sidebar_show",{processor:"string"}),n("help_accessibility",{processor:"boolean",default:e.hasPlugin("help")}),n("default_font_stack",{processor:"string[]",default:[]})},jS=WS("readonly"),qS=WS("disabled"),XS=WS("height"),YS=WS("width"),KS=$S(WS("min_width")),JS=$S(WS("min_height")),QS=$S(WS("max_width")),ZS=$S(WS("max_height")),eC=$S(WS("style_formats")),tC=WS("style_formats_merge"),oC=WS("style_formats_autohide"),nC=WS("content_langs"),sC=WS("removed_menuitems"),rC=WS("toolbar_mode"),aC=WS("toolbar_groups"),iC=WS("toolbar_location"),lC=WS("fixed_toolbar_container"),cC=WS("fixed_toolbar_container_target"),dC=WS("toolbar_persist"),uC=WS("toolbar_sticky_offset"),mC=WS("menubar"),gC=WS("toolbar"),pC=WS("file_picker_callback"),hC=WS("file_picker_validator_handler"),fC=WS("font_size_input_default_unit"),bC=WS("file_picker_types"),vC=WS("typeahead_urls"),xC=WS("anchor_top"),yC=WS("anchor_bottom"),wC=WS("draggable_modal"),SC=WS("statusbar"),CC=WS("elementpath"),kC=WS("branding"),OC=WS("resize"),_C=WS("paste_as_text"),TC=WS("sidebar_show"),EC=WS("promotion"),AC=WS("help_accessibility"),MC=WS("default_font_stack"),DC=WS("skin"),BC=e=>!1===e.options.get("skin"),IC=e=>!1!==e.options.get("menubar"),FC=e=>{const t=e.options.get("skin_url");if(BC(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return PS.baseURL+"/skins/ui/"+t}},RC=e=>e.options.get("line_height_formats").split(" "),NC=e=>{const t=gC(e),o=r(t),n=l(t)&&t.length>0;return!LC(e)&&(n||o||!0===t)},zC=e=>{const t=N(9,(t=>e.options.get("toolbar"+(t+1)))),o=P(t,r);return $e(o.length>0,o)},LC=e=>zC(e).fold((()=>{const t=gC(e);return f(t,r)&&t.length>0}),E),VC=e=>iC(e)===zS.bottom,HC=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=lC(e))&&void 0!==t?t:"";if(o.length>0)return Xn(rn(),o);const n=cC(e);return g(n)?A.some(ct(n)):A.none()},PC=e=>e.inline&&HC(e).isSome(),UC=e=>HC(e).getOrThunk((()=>Co(So(ct(e.getElement()))))),WC=e=>e.inline&&!IC(e)&&!NC(e)&&!LC(e),$C=e=>(e.options.get("toolbar_sticky")||e.inline)&&!PC(e)&&!WC(e),GC=e=>!PC(e)&&"split"===e.options.get("ui_mode"),jC=e=>{const t=e.options.get("menu");return le(t,(e=>({...e,items:e.items})))};var qC=Object.freeze({__proto__:null,get ToolbarMode(){return NS},get ToolbarLocation(){return zS},register:GS,getSkinUrl:FC,getSkinUrlOption:e=>A.from(e.options.get("skin_url")),isReadOnly:jS,isDisabled:qS,getSkin:DC,isSkinDisabled:BC,getHeightOption:XS,getWidthOption:YS,getMinWidthOption:KS,getMinHeightOption:JS,getMaxWidthOption:QS,getMaxHeightOption:ZS,getUserStyleFormats:eC,shouldMergeStyleFormats:tC,shouldAutoHideStyleFormats:oC,getLineHeightFormats:RC,getContentLanguages:nC,getRemovedMenuItems:sC,isMenubarEnabled:IC,isMultipleToolbars:LC,isToolbarEnabled:NC,isToolbarPersist:dC,getMultipleToolbarsOption:zC,getUiContainer:UC,useFixedContainer:PC,isSplitUiMode:GC,getToolbarMode:rC,isDraggableModal:wC,isDistractionFree:WC,isStickyToolbar:$C,getStickyToolbarOffset:uC,getToolbarLocation:iC,isToolbarLocationBottom:VC,getToolbarGroups:aC,getMenus:jC,getMenubar:mC,getToolbar:gC,getFilePickerCallback:pC,getFilePickerTypes:bC,useTypeaheadUrls:vC,getAnchorTop:xC,getAnchorBottom:yC,getFilePickerValidatorHandler:hC,getFontSizeInputDefaultUnit:fC,useStatusBar:SC,useElementPath:CC,promotionEnabled:EC,useBranding:kC,getResize:OC,getPasteAsText:_C,getSidebarShow:TC,useHelpAccessibility:AC,getDefaultFontStack:MC});const XC=["visible","hidden","clip"],YC=e=>et(e).length>0&&!F(XC,e),KC=e=>{if(oo(e)){const t=gn(e,"overflow-x"),o=gn(e,"overflow-y");return YC(t)||YC(o)}return!1},JC=e=>e.plugins.fullscreen&&e.plugins.fullscreen.isFullscreen(),QC=(e,t)=>GC(e)?((e,t)=>{const o=us(t,KC),n=0===o.length?ko(t).map(Oo).map((e=>us(e,KC))).getOr([]):o;return te(n).map((t=>({element:t,others:n.slice(1),isFullscreen:()=>JC(e)})))})(e,t):A.none(),ZC=e=>{const t=[...L(e.others,Rs),Ls()];return e.isFullscreen()?Ls():((e,t)=>W(t,((e,t)=>zs(e,t)),e))(Rs(e.element),t)},{entries:ek,setPrototypeOf:tk,isFrozen:ok,getPrototypeOf:nk,getOwnPropertyDescriptor:sk}=Object;let{freeze:rk,seal:ak,create:ik}=Object,{apply:lk,construct:ck}="undefined"!=typeof Reflect&&Reflect;rk||(rk=function(e){return e}),ak||(ak=function(e){return e}),lk||(lk=function(e,t,o){return e.apply(t,o)}),ck||(ck=function(e,t){return new e(...t)});const dk=Ok(Array.prototype.forEach),uk=Ok(Array.prototype.lastIndexOf),mk=Ok(Array.prototype.pop),gk=Ok(Array.prototype.push),pk=Ok(Array.prototype.splice),hk=Ok(String.prototype.toLowerCase),fk=Ok(String.prototype.toString),bk=Ok(String.prototype.match),vk=Ok(String.prototype.replace),xk=Ok(String.prototype.indexOf),yk=Ok(String.prototype.trim),wk=Ok(Object.prototype.hasOwnProperty),Sk=Ok(RegExp.prototype.test),Ck=(kk=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return ck(kk,t)});var kk;function Ok(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return lk(e,t,n)}}function _k(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:hk;tk&&tk(e,null);let n=t.length;for(;n--;){let s=t[n];if("string"==typeof s){const e=o(s);e!==s&&(ok(t)||(t[n]=e),s=e)}e[s]=!0}return e}function Tk(e){for(let t=0;t<e.length;t++)wk(e,t)||(e[t]=null);return e}function Ek(e){const t=ik(null);for(const[o,n]of ek(e))wk(e,o)&&(Array.isArray(n)?t[o]=Tk(n):n&&"object"==typeof n&&n.constructor===Object?t[o]=Ek(n):t[o]=n);return t}function Ak(e,t){for(;null!==e;){const o=sk(e,t);if(o){if(o.get)return Ok(o.get);if("function"==typeof o.value)return Ok(o.value)}e=nk(e)}return function(){return null}}const Mk=rk(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Dk=rk(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Bk=rk(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ik=rk(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Fk=rk(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Rk=rk(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Nk=rk(["#text"]),zk=rk(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Lk=rk(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Vk=rk(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Hk=rk(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Pk=ak(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Uk=ak(/<%[\w\W]*|[\w\W]*%>/gm),Wk=ak(/\$\{[\w\W]*/gm),$k=ak(/^data-[\-\w.\u00B7-\uFFFF]+$/),Gk=ak(/^aria-[\-\w]+$/),jk=ak(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),qk=ak(/^(?:\w+script|data):/i),Xk=ak(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Yk=ak(/^html$/i),Kk=ak(/^[a-z][.\w]*(-[.\w]+)+$/i);var Jk=Object.freeze({__proto__:null,ARIA_ATTR:Gk,ATTR_WHITESPACE:Xk,CUSTOM_ELEMENT:Kk,DATA_ATTR:$k,DOCTYPE_NAME:Yk,ERB_EXPR:Uk,IS_ALLOWED_URI:jk,IS_SCRIPT_OR_DATA:qk,MUSTACHE_EXPR:Pk,TMPLIT_EXPR:Wk});const Qk=function(){return"undefined"==typeof window?null:window};var Zk=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Qk();const o=t=>e(t);if(o.version="3.2.4",o.removed=[],!t||!t.document||9!==t.document.nodeType||!t.Element)return o.isSupported=!1,o;let{document:n}=t;const s=n,r=s.currentScript,{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:c,NodeFilter:d,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:g,trustedTypes:p}=t,h=c.prototype,f=Ak(h,"cloneNode"),b=Ak(h,"remove"),v=Ak(h,"nextSibling"),x=Ak(h,"childNodes"),y=Ak(h,"parentNode");if("function"==typeof i){const e=n.createElement("template");e.content&&e.content.ownerDocument&&(n=e.content.ownerDocument)}let w,S="";const{implementation:C,createNodeIterator:k,createDocumentFragment:O,getElementsByTagName:_}=n,{importNode:T}=s;let E={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};o.isSupported="function"==typeof ek&&"function"==typeof y&&C&&void 0!==C.createHTMLDocument;const{MUSTACHE_EXPR:A,ERB_EXPR:M,TMPLIT_EXPR:D,DATA_ATTR:B,ARIA_ATTR:I,IS_SCRIPT_OR_DATA:F,ATTR_WHITESPACE:R,CUSTOM_ELEMENT:N}=Jk;let{IS_ALLOWED_URI:z}=Jk,L=null;const V=_k({},[...Mk,...Dk,...Bk,...Fk,...Nk]);let H=null;const P=_k({},[...zk,...Lk,...Vk,...Hk]);let U=Object.seal(ik(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),W=null,$=null,G=!0,j=!0,q=!1,X=!0,Y=!1,K=!0,J=!1,Q=!1,Z=!1,ee=!1,te=!1,oe=!1,ne=!0,se=!1,re=!0,ae=!1,ie={},le=null;const ce=_k({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let de=null;const ue=_k({},["audio","video","img","source","image","track"]);let me=null;const ge=_k({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),pe="http://www.w3.org/1998/Math/MathML",he="http://www.w3.org/2000/svg",fe="http://www.w3.org/1999/xhtml";let be=fe,ve=!1,xe=null;const ye=_k({},[pe,he,fe],fk);let we=_k({},["mi","mo","mn","ms","mtext"]),Se=_k({},["annotation-xml"]);const Ce=_k({},["title","style","font","a","script"]);let ke=null;const Oe=["application/xhtml+xml","text/html"];let _e=null,Te=null;const Ee=n.createElement("form"),Ae=function(e){return e instanceof RegExp||e instanceof Function},Me=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Te||Te!==e){if(e&&"object"==typeof e||(e={}),e=Ek(e),ke=-1===Oe.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,_e="application/xhtml+xml"===ke?fk:hk,L=wk(e,"ALLOWED_TAGS")?_k({},e.ALLOWED_TAGS,_e):V,H=wk(e,"ALLOWED_ATTR")?_k({},e.ALLOWED_ATTR,_e):P,xe=wk(e,"ALLOWED_NAMESPACES")?_k({},e.ALLOWED_NAMESPACES,fk):ye,me=wk(e,"ADD_URI_SAFE_ATTR")?_k(Ek(ge),e.ADD_URI_SAFE_ATTR,_e):ge,de=wk(e,"ADD_DATA_URI_TAGS")?_k(Ek(ue),e.ADD_DATA_URI_TAGS,_e):ue,le=wk(e,"FORBID_CONTENTS")?_k({},e.FORBID_CONTENTS,_e):ce,W=wk(e,"FORBID_TAGS")?_k({},e.FORBID_TAGS,_e):{},$=wk(e,"FORBID_ATTR")?_k({},e.FORBID_ATTR,_e):{},ie=!!wk(e,"USE_PROFILES")&&e.USE_PROFILES,G=!1!==e.ALLOW_ARIA_ATTR,j=!1!==e.ALLOW_DATA_ATTR,q=e.ALLOW_UNKNOWN_PROTOCOLS||!1,X=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Y=e.SAFE_FOR_TEMPLATES||!1,K=!1!==e.SAFE_FOR_XML,J=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,oe=e.RETURN_TRUSTED_TYPE||!1,Z=e.FORCE_BODY||!1,ne=!1!==e.SANITIZE_DOM,se=e.SANITIZE_NAMED_PROPS||!1,re=!1!==e.KEEP_CONTENT,ae=e.IN_PLACE||!1,z=e.ALLOWED_URI_REGEXP||jk,be=e.NAMESPACE||fe,we=e.MATHML_TEXT_INTEGRATION_POINTS||we,Se=e.HTML_INTEGRATION_POINTS||Se,U=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Ae(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(U.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Ae(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(U.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(U.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Y&&(j=!1),te&&(ee=!0),ie&&(L=_k({},Nk),H=[],!0===ie.html&&(_k(L,Mk),_k(H,zk)),!0===ie.svg&&(_k(L,Dk),_k(H,Lk),_k(H,Hk)),!0===ie.svgFilters&&(_k(L,Bk),_k(H,Lk),_k(H,Hk)),!0===ie.mathMl&&(_k(L,Fk),_k(H,Vk),_k(H,Hk))),e.ADD_TAGS&&(L===V&&(L=Ek(L)),_k(L,e.ADD_TAGS,_e)),e.ADD_ATTR&&(H===P&&(H=Ek(H)),_k(H,e.ADD_ATTR,_e)),e.ADD_URI_SAFE_ATTR&&_k(me,e.ADD_URI_SAFE_ATTR,_e),e.FORBID_CONTENTS&&(le===ce&&(le=Ek(le)),_k(le,e.FORBID_CONTENTS,_e)),re&&(L["#text"]=!0),J&&_k(L,["html","head","body"]),L.table&&(_k(L,["tbody"]),delete W.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw Ck('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw Ck('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');w=e.TRUSTED_TYPES_POLICY,S=w.createHTML("")}else void 0===w&&(w=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let o=null;const n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(o=t.getAttribute(n));const s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,r)),null!==w&&"string"==typeof S&&(S=w.createHTML(""));rk&&rk(e),Te=e}},De=_k({},[...Dk,...Bk,...Ik]),Be=_k({},[...Fk,...Rk]),Ie=function(e){gk(o.removed,{element:e});try{y(e).removeChild(e)}catch(t){b(e)}},Fe=function(e,t){try{gk(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){gk(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(ee||te)try{Ie(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Re=function(e){let t=null,o=null;if(Z)e="<remove></remove>"+e;else{const t=bk(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===ke&&be===fe&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const s=w?w.createHTML(e):e;if(be===fe)try{t=(new g).parseFromString(s,ke)}catch(e){}if(!t||!t.documentElement){t=C.createDocument(be,"template",null);try{t.documentElement.innerHTML=ve?S:s}catch(e){}}const r=t.body||t.documentElement;return e&&o&&r.insertBefore(n.createTextNode(o),r.childNodes[0]||null),be===fe?_.call(t,J?"html":"body")[0]:J?t.documentElement:r},Ne=function(e){return k.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT|d.SHOW_PROCESSING_INSTRUCTION|d.SHOW_CDATA_SECTION,null)},ze=function(e){return e instanceof m&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof u)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Le=function(e){return"function"==typeof l&&e instanceof l};function Ve(e,t,n){dk(e,(e=>{e.call(o,t,n,Te)}))}const He=function(e){let t=null;if(Ve(E.beforeSanitizeElements,e,null),ze(e))return Ie(e),!0;const n=_e(e.nodeName);if(Ve(E.uponSanitizeElement,e,{tagName:n,allowedTags:L}),e.hasChildNodes()&&!Le(e.firstElementChild)&&Sk(/<[/\w]/g,e.innerHTML)&&Sk(/<[/\w]/g,e.textContent))return Ie(e),!0;if(7===e.nodeType)return Ie(e),!0;if(K&&8===e.nodeType&&Sk(/<[/\w]/g,e.data))return Ie(e),!0;if(!L[n]||W[n]){if(!W[n]&&Ue(n)){if(U.tagNameCheck instanceof RegExp&&Sk(U.tagNameCheck,n))return!1;if(U.tagNameCheck instanceof Function&&U.tagNameCheck(n))return!1}if(re&&!le[n]){const t=y(e)||e.parentNode,o=x(e)||e.childNodes;if(o&&t)for(let n=o.length-1;n>=0;--n){const s=f(o[n],!0);s.__removalCount=(e.__removalCount||0)+1,t.insertBefore(s,v(e))}}return Ie(e),!0}return e instanceof c&&!function(e){let t=y(e);t&&t.tagName||(t={namespaceURI:be,tagName:"template"});const o=hk(e.tagName),n=hk(t.tagName);return!!xe[e.namespaceURI]&&(e.namespaceURI===he?t.namespaceURI===fe?"svg"===o:t.namespaceURI===pe?"svg"===o&&("annotation-xml"===n||we[n]):Boolean(De[o]):e.namespaceURI===pe?t.namespaceURI===fe?"math"===o:t.namespaceURI===he?"math"===o&&Se[n]:Boolean(Be[o]):e.namespaceURI===fe?!(t.namespaceURI===he&&!Se[n])&&!(t.namespaceURI===pe&&!we[n])&&!Be[o]&&(Ce[o]||!De[o]):!("application/xhtml+xml"!==ke||!xe[e.namespaceURI]))}(e)?(Ie(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!Sk(/<\/no(script|embed|frames)/i,e.innerHTML)?(Y&&3===e.nodeType&&(t=e.textContent,dk([A,M,D],(e=>{t=vk(t,e," ")})),e.textContent!==t&&(gk(o.removed,{element:e.cloneNode()}),e.textContent=t)),Ve(E.afterSanitizeElements,e,null),!1):(Ie(e),!0)},Pe=function(e,t,o){if(ne&&("id"===t||"name"===t)&&(o in n||o in Ee))return!1;if(j&&!$[t]&&Sk(B,t));else if(G&&Sk(I,t));else if(!H[t]||$[t]){if(!(Ue(e)&&(U.tagNameCheck instanceof RegExp&&Sk(U.tagNameCheck,e)||U.tagNameCheck instanceof Function&&U.tagNameCheck(e))&&(U.attributeNameCheck instanceof RegExp&&Sk(U.attributeNameCheck,t)||U.attributeNameCheck instanceof Function&&U.attributeNameCheck(t))||"is"===t&&U.allowCustomizedBuiltInElements&&(U.tagNameCheck instanceof RegExp&&Sk(U.tagNameCheck,o)||U.tagNameCheck instanceof Function&&U.tagNameCheck(o))))return!1}else if(me[t]);else if(Sk(z,vk(o,R,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==xk(o,"data:")||!de[e])if(q&&!Sk(F,vk(o,R,"")));else if(o)return!1;return!0},Ue=function(e){return"annotation-xml"!==e&&bk(e,N)},We=function(e){Ve(E.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||ze(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:H,forceKeepAttr:void 0};let s=t.length;for(;s--;){const r=t[s],{name:a,namespaceURI:i,value:l}=r,c=_e(a);let d="value"===a?l:yk(l);const u=d;if(n.attrName=c,n.attrValue=d,n.keepAttr=!0,n.forceKeepAttr=void 0,Ve(E.uponSanitizeAttribute,e,n),d=n.attrValue,!se||"id"!==c&&"name"!==c||(Fe(a,e),d="user-content-"+d),K&&Sk(/((--!?|])>)|<\/(style|title)/i,d)){Fe(a,e);continue}if(n.forceKeepAttr)continue;if(!n.keepAttr){Fe(a,e);continue}if(!X&&Sk(/\/>/i,d)){Fe(a,e);continue}Y&&dk([A,M,D],(e=>{d=vk(d,e," ")}));const m=_e(e.nodeName);if(Pe(m,c,d)){if(w&&"object"==typeof p&&"function"==typeof p.getAttributeType)if(i);else switch(p.getAttributeType(m,c)){case"TrustedHTML":d=w.createHTML(d);break;case"TrustedScriptURL":d=w.createScriptURL(d)}if(d!==u)try{i?e.setAttributeNS(i,a,d):e.setAttribute(a,d),ze(e)?Ie(e):mk(o.removed)}catch(e){}}else Fe(a,e)}Ve(E.afterSanitizeAttributes,e,null)},$e=function e(t){let o=null;const n=Ne(t);for(Ve(E.beforeSanitizeShadowDOM,t,null);o=n.nextNode();)Ve(E.uponSanitizeShadowNode,o,null),He(o),We(o),o.content instanceof a&&e(o.content);Ve(E.afterSanitizeShadowDOM,t,null)};return o.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,r=null,i=null,c=null;if(ve=!e,ve&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Le(e)){if("function"!=typeof e.toString)throw Ck("toString is not a function");if("string"!=typeof(e=e.toString()))throw Ck("dirty is not a string, aborting")}if(!o.isSupported)return e;if(Q||Me(t),o.removed=[],"string"==typeof e&&(ae=!1),ae){if(e.nodeName){const t=_e(e.nodeName);if(!L[t]||W[t])throw Ck("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)n=Re("\x3c!----\x3e"),r=n.ownerDocument.importNode(e,!0),1===r.nodeType&&"BODY"===r.nodeName||"HTML"===r.nodeName?n=r:n.appendChild(r);else{if(!ee&&!Y&&!J&&-1===e.indexOf("<"))return w&&oe?w.createHTML(e):e;if(n=Re(e),!n)return ee?null:oe?S:""}n&&Z&&Ie(n.firstChild);const d=Ne(ae?e:n);for(;i=d.nextNode();)He(i),We(i),i.content instanceof a&&$e(i.content);if(ae)return e;if(ee){if(te)for(c=O.call(n.ownerDocument);n.firstChild;)c.appendChild(n.firstChild);else c=n;return(H.shadowroot||H.shadowrootmode)&&(c=T.call(s,c,!0)),c}let u=J?n.outerHTML:n.innerHTML;return J&&L["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&Sk(Yk,n.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+u),Y&&dk([A,M,D],(e=>{u=vk(u,e," ")})),w&&oe?w.createHTML(u):u},o.setConfig=function(){Me(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Q=!0},o.clearConfig=function(){Te=null,Q=!1},o.isValidAttribute=function(e,t,o){Te||Me({});const n=_e(e),s=_e(t);return Pe(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&gk(E[e],t)},o.removeHook=function(e,t){if(void 0!==t){const o=uk(E[e],t);return-1===o?void 0:pk(E[e],o,1)[0]}return mk(E[e])},o.removeHooks=function(e){E[e]=[]},o.removeAllHooks=function(){E={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},o}();const eO=e=>Zk().sanitize(e);var tO=tinymce.util.Tools.resolve("tinymce.util.I18n");const oO={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-disc":!0,"list-bull-default":!0,"list-bull-square":!0},nO="temporary-placeholder",sO=e=>()=>fe(e,nO).getOr("!not found!"),rO=(e,t)=>{const o=e.toLowerCase();if(tO.isRtl()){const e=((e,t)=>Ze(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return be(t,e)?e:o}return o},aO=(e,t)=>fe(t,rO(e,t)),iO=(e,t)=>{const o=t();return aO(e,o).getOrThunk(sO(o))},lO=()=>ud("add-focusable",[Jc((e=>{qn(e.element,"svg").each((e=>Go(e,"focusable","false")))}))]),cO=(e,t,o,n)=>{var s,r;const a=(e=>!!tO.isRtl()&&be(oO,e))(t)?["tox-icon--flip"]:[],i=fe(o,rO(t,o)).or(n).getOrThunk(sO(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:rd([...null!==(r=e.behaviours)&&void 0!==r?r:[],lO()])}},dO=(e,t,o,n=A.none())=>cO(t,e,o(),n),uO={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},mO=nh({name:"Notification",factory:e=>{const t=Ne("notification-text"),o=dv({dom:cv(`<p id=${t}>${eO(e.backstageProvider.translate(e.text))}</p>`),behaviours:rd([Zm.config({})])}),n=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),s=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),r=dv({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(0)]},s(0)],behaviours:rd([Zm.config({})])}),a={updateProgress:(e,t)=>{e.getSystem().isConnected()&&r.getOpt(e).each((e=>{Zm.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(t)]},s(t)])}))},updateText:(e,t)=>{if(e.getSystem().isConnected()){const n=o.get(e);Zm.set(n,[Og(t)])}}},i=j([e.icon.toArray(),[e.level],A.from(uO[e.level]).toArray()]),l=dv(_v.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":e.backstageProvider.translate("Close")}},components:[dO("close",{tag:"span",classes:["tox-icon"]},e.iconProvider)],buttonBehaviours:rd([Pb.config({}),iv.config({...e.backstageProvider.tooltips.getConfig({tooltipText:e.backstageProvider.translate("Close")})})]),action:t=>{e.onAction(t)}})),c=((e,t,o)=>{const n=o(),s=$(e,(e=>be(n,rO(e,n))));return cO({tag:"div",classes:["tox-notification__icon"]},s.getOr(nO),n,A.none())})(i,0,e.iconProvider),d=[c,{dom:{tag:"div",classes:["tox-notification__body"]},components:[o.asSpec()],behaviours:rd([Zm.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert","aria-labelledby":t},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},behaviours:rd([Pb.config({}),fd.config({}),wm.config({mode:"special",onEscape:t=>(e.onAction(t),A.some(!0))})]),components:d.concat(e.progress?[r.asSpec()]:[]).concat([l.asSpec()]),apis:a}},configFields:[oa("level","info",["success","error","warning","warn","info"]),Rr("progress"),$r("icon"),Rr("onAction"),Rr("text"),Rr("iconProvider"),Rr("backstageProvider")],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var gO=(e,t,o,n)=>{const s=t.backstage.shared,r=()=>ct(""===e.queryCommandValue("ToggleView")?e.getContentAreaContainer():e.getContainer()),a=()=>{const e=Rs(r());return A.some(e)},i=e=>{a().each((t=>{V(e,(e=>{vn(e.element,"width"),Tn(e.element)>t.width&&dn(e.element,"width",t.width+"px")}))}))};return{open:(t,l,c)=>{const d=()=>{n.on((t=>{l();const o=c();(e=>{Zm.remove(e,u),m()})(t),((t,o)=>{0===fo(t.element).length?((t,o)=>{Xx.hide(t),n.clear(),o&&e.focus()})(t,o):((e,t)=>{t&&wm.focusIn(e)})(t,o)})(t,o)}))},u=Ag(mO.sketch({text:t.text,level:F(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,onAction:d,iconProvider:s.providers.icons,backstageProvider:s.providers}));if(n.isSet()){const e=Mg(u);n.on((t=>{Zm.append(t,e),Xx.reposition(t),u.hasConfigured(Ep)&&Ep.refresh(t),i(t.components())}))}else{const t=Ag(Xx.sketch({dom:{tag:"div",classes:["tox-notifications-container"],attributes:{"aria-label":"Notifications",role:"region"}},lazySink:s.getSink,fireDismissalEventInstead:{},...s.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}},inlineBehaviours:rd([wm.config({mode:"cyclic",selector:".tox-notification, .tox-notification a, .tox-notification button"}),Zm.config({}),...$C(e)&&s.header.isPositionedAtTop()?[]:[Ep.config({contextual:{lazyContext:()=>A.some(Rs(r())),fadeInClass:"tox-notification-container-dock-fadein",fadeOutClass:"tox-notification-container-dock-fadeout",transitionClass:"tox-notification-container-dock-transition"},modes:["top"],lazyViewport:t=>QC(e,t.element).map((e=>({bounds:ZC(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:In(e.element).top})}))).getOrThunk((()=>({bounds:Ls(),optScrollEnv:A.none()})))})]])})),i=Mg(u),l={maxHeightFunction:kc()},c={...s.anchors.banner(),overrides:l};n.set(t),o.add(t),Xx.showWithinBounds(t,i,{anchor:c},a)}h(t.timeout)&&t.timeout>0&&VS.setEditorTimeout(e,(()=>{d()}),t.timeout);const m=()=>{n.on((e=>{Xx.reposition(e),e.hasConfigured(Ep)&&Ep.refresh(e),i(e.components())}))};return{close:d,reposition:m,text:e=>{mO.updateText(u,e)},settings:t,getEl:()=>u.element.dom,progressBar:{value:e=>{mO.updateProgress(u,e)}}}},close:e=>{e.close()},getArgs:e=>e.settings}};var pO;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(pO||(pO={}));var hO=pO;const fO="tox-menu-nav__js",bO="tox-collection__item",vO="tox-swatch",xO={normal:fO,color:vO},yO="tox-collection__item--enabled",wO="tox-collection__item-icon",SO="tox-collection__item-label",CO="tox-collection__item-caret",kO="tox-collection__item--active",OO="tox-collection__item-container",_O="tox-collection__item-container--row",TO=e=>fe(xO,e).getOr(fO),EO=e=>"color"===e?"tox-swatches":"tox-menu",AO=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:EO(e),tieredMenu:"tox-tiered-menu"}),MO=e=>{const t=AO(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:TO(e)}},DO=(e,t,o)=>{const n=AO(o);return{tag:"div",classes:j([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},BO=[Qv.parts.items({})],IO=(e,t,o)=>{const n=AO(o);return{dom:{tag:"div",classes:j([[n.tieredMenu]])},markers:MO(o)}},FO=Ne("refetch-trigger-event"),RO=Ne("redirect-menu-item-interaction"),NO="tox-menu__searcher",zO=e=>Xn(e.element,`.${NO}`).bind((t=>e.getSystem().getByDom(t).toOptional())),LO=zO,VO=e=>({fetchPattern:Yh.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),HO=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Rc(e,RO,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[bO]},components:[Zx.sketch({inputClasses:[NO,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:rd([ud(n,[Wc(Ca(),(e=>{Fc(e,FO)})),Wc(wa(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),wm.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,wm.name()]}})]}},PO="tox-collection--results__js",UO=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:Ne("aria-item-search-result-id"),"aria-selected":"false"}}}:e},WO=(e,t)=>o=>{const n=z(o,t);return L(n,(t=>({dom:e,components:t})))},$O=(e,t)=>{const o=[];let n=[];return V(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(be(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),L(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},GO=(e,t,o)=>Qv.parts.items({preprocess:n=>{const s=L(n,o);return"auto"!==e&&e>1?WO({tag:"div",classes:["tox-collection__group"]},e)(s):$O(s,((e,o)=>"separator"===t[o].type))}}),jO=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[GO(e,t,w)]}),qO=e=>R(e,(e=>"icon"in e&&void 0!==e.icon)),XO=e=>(console.error(Ar(e)),console.log(e),A.none()),YO=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Qv.parts.items({preprocess:e=>$O(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},KO=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Qv.parts.items({preprocess:"auto"!==e?WO({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("imageselector"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-image-selector-menu"]},components:[{dom:{tag:"div",classes:["tox-image-selector"]},components:[Qv.parts.items({preprocess:"auto"!==e?WO({tag:"div",classes:["tox-image-selector__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=jO(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?jO(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=Ne("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[HO({i18n:tO.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],PO],attributes:{id:n}},components:[GO(e,t,UO)]}]}})(n,o,s.searchMode):((e,t)=>{const o=Ne("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",PO].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:o}},components:[GO(e,t,UO)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Qv.parts.items({preprocess:WO({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:DO(t,n,s.menuType),components:BO,items:o}},JO=Lr("type"),QO=Lr("name"),ZO=Lr("label"),e_=Lr("text"),t_=Lr("title"),o_=Lr("icon"),n_=Lr("url"),s_=Lr("value"),r_=Hr("fetch"),a_=Hr("getSubmenuItems"),i_=Hr("onAction"),l_=Hr("onItemAction"),c_=sa("onSetup",(()=>b)),d_=qr("name"),u_=qr("text"),m_=qr("role"),g_=qr("icon"),p_=qr("tooltip"),h_=qr("label"),f_=qr("shortcut"),b_=Yr("select"),v_=na("active",!1),x_=na("borderless",!1),y_=na("enabled",!0),w_=na("primary",!1),S_=e=>Qr("columns",e),C_=Qr("meta",{}),k_=sa("onAction",b),O_=e=>ta("type",e),__=e=>Br("name","name",ir((()=>Ne(`${e}-name`))),or),T_=[JO,e_,Vr("level",["info","warn","error","success"]),o_,Qr("url","")],E_=vr(T_),A_=[JO,e_,y_,__("button"),g_,x_,Xr("buttonType",["primary","secondary","toolbar"]),w_,ta("context","mode:design")],M_=vr(A_),D_=[JO,QO],B_=D_.concat([h_]),I_=D_.concat([ZO,y_,ta("context","mode:design")]),F_=vr(I_),R_=nr,N_=B_.concat([S_("auto"),ta("context","mode:design")]),z_=vr(N_),L_=Sr([s_,e_,o_]),V_=B_.concat([ta("storageKey","default"),ta("context","mode:design")]),H_=vr(V_),P_=or,U_=vr(B_),W_=or,$_=D_.concat([ta("tag","textarea"),Lr("scriptId"),Lr("scriptUrl"),Yr("onFocus"),Zr("settings",void 0,ar)]),G_=D_.concat([ta("tag","textarea"),Hr("init")]),j_=kr((e=>_r("customeditor.old",br(G_),e).orThunk((()=>_r("customeditor.new",br($_),e))))),q_=or,X_=[y_,u_,m_,f_,Br("value","value",ir((()=>Ne("menuitem-value"))),Zs()),C_,ta("context","mode:design")];const Y_=vr([JO,QO].concat(X_)),K_=nr,J_=[__("button"),g_,oa("align","end",["start","end"]),w_,y_,Xr("buttonType",["primary","secondary"]),ta("context","mode:design")],Q_=[...J_,e_],Z_=[Vr("type",["submit","cancel","custom"]),...Q_],eT=[Vr("type",["menu"]),u_,p_,g_,Wr("items",Y_),...J_],tT=[...J_,Vr("type",["togglebutton"]),p_,g_,u_,na("active",!1)],oT=Mr("type",{submit:Z_,cancel:Z_,custom:Z_,menu:eT,togglebutton:tT}),nT=B_.concat([ta("context","mode:design")]),sT=vr(nT),rT=xr(Qs),aT=e=>[JO,zr("columns"),e],iT=[JO,Lr("html"),oa("presets","presentation",["presentation","document"]),sa("onInit",b),na("stretched",!1)],lT=vr(iT),cT=B_.concat([na("border",!1),na("sandboxed",!0),na("streamContent",!1),na("transparent",!0)]),dT=vr(cT),uT=or,mT=vr(D_.concat([qr("height")])),gT=vr([Lr("url"),jr("zoom"),jr("cachedWidth"),jr("cachedHeight")]),pT=B_.concat([qr("inputMode"),qr("placeholder"),na("maximized",!1),y_,ta("context","mode:design")]),hT=vr(pT),fT=or,bT=e=>[JO,ZO,e,oa("align","start",["start","center","end"]),qr("for")],vT=[e_,s_],xT=[e_,Wr("items",Dr(0,(()=>yT)))],yT=yr([vr(vT),vr(xT)]),wT=B_.concat([Wr("items",yT),y_,ta("context","mode:design")]),ST=vr(wT),CT=or,kT=B_.concat([Ur("items",[e_,s_]),ea("size",1),y_,ta("context","mode:design")]),OT=vr(kT),_T=or,TT=B_.concat([na("constrain",!0),y_,ta("context","mode:design")]),ET=vr(TT),AT=vr([Lr("width"),Lr("height")]),MT=D_.concat([ZO,ea("min",0),ea("max",0)]),DT=vr(MT),BT=tr,IT=[JO,Wr("header",or),Wr("cells",xr(or))],FT=vr(IT),RT=B_.concat([qr("placeholder"),na("maximized",!1),y_,ta("context","mode:design")]),NT=vr(RT),zT=or,LT=[ta("buttonType","default"),qr("text"),qr("tooltip"),qr("icon"),Zr("search",!1,yr([nr,vr([qr("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),Hr("fetch"),sa("onSetup",(()=>b)),ta("context","mode:design")],VT=vr([JO,...LT]),HT=e=>_r("menubutton",VT,e),PT=[Vr("type",["directory","leaf"]),t_,Lr("id"),Gr("menu",VT),qr("customStateIcon"),qr("customStateIconTooltip")],UT=vr(PT),WT=PT.concat([Wr("children",Dr(0,(()=>Cr("type",{directory:$T,leaf:UT}))))]),$T=vr(WT),GT=Cr("type",{directory:$T,leaf:UT}),jT=[JO,Wr("items",GT),Yr("onLeafAction"),Yr("onToggleExpand"),ra("defaultExpandedIds",[],or),qr("defaultSelectedId")],qT=vr(jT),XT=B_.concat([oa("filetype","file",["image","media","file"]),y_,qr("picker_text"),ta("context","mode:design")]),YT=vr(XT),KT=vr([s_,C_]),JT=e=>Br("items","items",{tag:"required",process:{}},xr(kr((t=>_r(`Checking item of ${e}`,QT,t).fold((e=>Ae.error(Ar(e))),(e=>Ae.value(e))))))),QT=hr((()=>{return Cr("type",{alertbanner:E_,bar:vr((e=JT("bar"),[JO,e])),button:M_,checkbox:F_,colorinput:H_,colorpicker:U_,dropzone:sT,grid:vr(aT(JT("grid"))),iframe:dT,input:hT,listbox:ST,selectbox:OT,sizeinput:ET,slider:DT,textarea:NT,urlinput:YT,customeditor:j_,htmlpanel:lT,imagepreview:mT,collection:z_,label:vr(bT(JT("label"))),table:FT,tree:qT,panel:eE});var e})),ZT=[JO,Qr("classes",[]),Wr("items",QT)],eE=vr(ZT),tE=[__("tab"),t_,Wr("items",QT)],oE=[JO,Ur("tabs",tE)],nE=vr(oE),sE=Q_,rE=oT,aE=vr([Lr("title"),Nr("body",Cr("type",{panel:eE,tabpanel:nE})),ta("size","normal"),ra("buttons",[],rE),Qr("initialData",{}),sa("onAction",b),sa("onChange",b),sa("onSubmit",b),sa("onClose",b),sa("onCancel",b),sa("onTabChange",b)]),iE=vr([Vr("type",["cancel","custom"]),...sE]),lE=vr([Lr("title"),Lr("url"),jr("height"),jr("width"),Kr("buttons",iE),sa("onAction",b),sa("onCancel",b),sa("onClose",b),sa("onMessage",b)]),cE=e=>a(e)?[e].concat(q(he(e),cE)):l(e)?q(e,cE):[],dE=e=>r(e.type)&&r(e.name),uE={checkbox:R_,colorinput:P_,colorpicker:W_,dropzone:rT,input:fT,iframe:uT,imagepreview:gT,selectbox:_T,sizeinput:AT,slider:BT,listbox:CT,size:AT,textarea:zT,urlinput:KT,customeditor:q_,collection:L_,togglemenuitem:K_},mE=e=>{const t=(e=>P(cE(e),dE))(e),o=q(t,(e=>(e=>A.from(uE[e.type]))(e).fold((()=>[]),(t=>[Nr(e.name,t)]))));return vr(o)},gE=e=>{var t;return{internalDialog:Tr(_r("dialog",aE,e)),dataValidator:mE(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},pE={open:(e,t)=>{const o=gE(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Tr(_r("dialog",lE,t))),redial:e=>gE(e)},hE=vr([JO,u_]),fE=vr([O_("autocompleteitem"),v_,y_,C_,s_,u_,g_]);vr([JO,Lr("trigger"),ea("minChars",1),S_(1),ea("maxResults",10),Yr("matches"),r_,i_,ra("highlightOn",[],or)]);const bE=[y_,p_,g_,u_,c_,ta("context","mode:design")],vE=vr([JO,i_,f_].concat(bE)),xE=e=>_r("toolbarbutton",vE,e),yE=[v_].concat(bE),wE=vr(yE.concat([JO,i_,f_])),SE=e=>_r("ToggleButton",wE,e),CE=[sa("predicate",T),oa("scope","node",["node","editor"]),oa("position","selection",["node","selection","line"])],kE=bE.concat([O_("contextformbutton"),ta("align","end"),w_,i_,Ir("original",w)]),OE=yE.concat([O_("contextformbutton"),ta("align","end"),w_,i_,Ir("original",w)]),_E=bE.concat([O_("contextformbutton")]),TE=yE.concat([O_("contextformtogglebutton")]),EE=[h_,Wr("commands",Mr("type",{contextformbutton:kE,contextformtogglebutton:OE})),Gr("launch",Mr("type",{contextformbutton:_E,contextformtogglebutton:TE})),sa("onInput",b),sa("onSetup",b)],AE=[...CE,...EE,Vr("type",["contextform"]),sa("initValue",y("")),qr("placeholder")],ME=[...CE,...EE,Vr("type",["contextsliderform"]),sa("initValue",y(0)),sa("min",y(0)),sa("max",y(100))],DE=[...CE,...EE,Vr("type",["contextsizeinputform"]),sa("initValue",y({width:"",height:""}))],BE=Mr("type",{contextform:AE,contextsliderform:ME,contextsizeinputform:DE}),IE=bE.concat([O_("contexttoolbarbutton")]),FE=vr([O_("contexttoolbar"),Jr("launch",IE),Nr("items",yr([or,Sr([qr("name"),qr("label"),Wr("items",or)])]))].concat(CE)),RE=e=>({name:e.name.getOrUndefined(),label:e.label.getOrUndefined(),items:e.items}),NE=[JO,Lr("src"),qr("alt"),ra("classes",[],or)],zE=vr(NE),LE=[JO,e_,d_,ra("classes",["tox-collection__item-label"],or)],VE=vr(LE),HE=hr((()=>Cr("type",{cardimage:zE,cardtext:VE,cardcontainer:PE}))),PE=vr([JO,ta("direction","horizontal"),ta("align","left"),ta("valign","middle"),Wr("items",HE)]),UE=vr([JO,h_,Wr("items",HE),c_,k_].concat(X_)),WE=vr([JO,v_,g_,h_].concat(X_)),$E=[JO,Lr("fancytype"),k_],GE=[Qr("initData",{})].concat($E),jE=[Yr("select"),aa("initData",{},[na("allowCustomColors",!0),ta("storageKey","default"),Kr("colors",Zs())])].concat($E),qE=[Yr("select"),Pr("initData",[zr("columns"),ra("items",[],Zs())])].concat($E),XE=Mr("fancytype",{inserttable:GE,colorswatch:jE,imageselect:qE}),YE=vr([JO,v_,n_,h_,p_].concat(X_)),KE=vr([JO,v_,o_,ZO,p_,s_].concat(X_)),JE=vr([JO,c_,k_,g_].concat(X_)),QE=vr([JO,a_,c_,g_].concat(X_)),ZE=vr([JO,g_,v_,c_,i_].concat(X_)),eA=vr([g_,p_,sa("onShow",b),sa("onHide",b),c_]),tA=vr([JO,Nr("items",yr([Sr([QO,Wr("items",or)]),or]))].concat(bE)),oA=vr([JO,p_,g_,u_,b_,r_,c_,oa("presets","normal",["normal","color","listpreview"]),S_(1),i_,l_,ta("context","mode:design")]),nA=[u_,g_,qr("tooltip"),oa("buttonType","secondary",["primary","secondary"]),na("borderless",!1),Hr("onAction"),ta("context","mode:design")],sA={button:[...nA,e_,Vr("type",["button"])],togglebutton:[...nA,na("active",!1),Vr("type",["togglebutton"])]},rA=[Vr("type",["group"]),ra("buttons",[],Mr("type",sA))],aA=Mr("type",{...sA,group:rA}),iA=vr([ra("buttons",[],aA),Hr("onShow"),Hr("onHide")]),lA=(e,t,o)=>{const n=ms(e.element,"."+o);if(n.length>0){const e=G(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},cA=e=>((e,t)=>rd([ud(e,t)]))(Ne("unnamed-events"),e),dA=e=>tp.config({disabled:e,disableClass:"tox-collection__item--state-disabled"}),uA=e=>tp.config({disabled:e}),mA=e=>tp.config({disabled:e,disableClass:"tox-tbtn--disabled"}),gA=e=>tp.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),pA=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},hA=(e,t)=>Jc((o=>{p(e.onBeforeSetup)&&e.onBeforeSetup(o),pA(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),fA=(e,t)=>Qc((o=>pA(e,o)(t.get()))),bA=(e,t,o)=>Qc((n=>(o.set(Yh.getValue(n)),pA(e,n)(t.get())))),vA="silver.uistate",xA="setDisabled",yA="init",wA=["switchmode",yA],SA=(e,t)=>{const o=e.mainUi.outerContainer,n=[e.mainUi.mothership,...e.uiMotherships];t===xA&&V(n,(e=>{e.broadcastOn([gv()],{target:o.element})})),V(n,(e=>{e.broadcastOn([vA],t)}))},CA=(e,t)=>{e.on("init SwitchMode",(e=>{SA(t,e.type)})),e.on("DisabledStateChange",(o=>{if(!o.isDefaultPrevented()){const n=o.state?xA:yA;SA(t,n),o.state||e.nodeChanged()}})),e.on("NodeChange",(o=>{const n=e.ui.isEnabled()?o.type:xA;SA(t,n)})),jS(e)&&e.mode.set("readonly")},kA=e=>ob.config({channels:{[vA]:{onReceive:(t,o)=>{if(o===xA||"setEnabled"===o)return void tp.set(t,o===xA);const{contextType:n,shouldDisable:s}=e();("mode"!==n||F(wA,o))&&tp.set(t,s)}}}}),OA=(e,t)=>ed(((o,n)=>{pA(e,o)(e.onAction),e.triggersSubmenu||t!==hO.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&Fc(o,Ha()),n.stop())})),_A={[Na()]:["disabling","alloy.base.behaviour","toggling","item-events"]},TA=Pe,EA=(e,t,o,n)=>{const s=ye(b);return{type:"item",dom:t.dom,components:TA(t.optComponents),data:e.data,eventOrder:_A,hasSubmenu:e.triggersSubmenu,itemBehaviours:rd([ud("item-events",[OA(e,o),hA(e,s),fA(e,s)]),dA((()=>!e.enabled||n.checkUiComponentContext(e.context).shouldDisable)),kA((()=>n.checkUiComponentContext(e.context))),Zm.config({})].concat(e.itemBehaviours))}},AA=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),MA=(e,t)=>{var o,n;const s=it("div");return os(s,"tox-image-selector-loading-spinner"),{dom:{tag:e.tag,attributes:null!==(o=e.attributes)&&void 0!==o?o:{},classes:e.classes},components:[{dom:{tag:"div",classes:["tox-image-selector-image-wrapper"]},components:[{dom:{tag:"img",attributes:{src:t},classes:["tox-image-selector-image-img"]}}]},...e.checkMark.toArray()],behaviours:rd([...null!==(n=e.behaviours)&&void 0!==n?n:[],ud("render-image-events",[Jc((e=>{var t;t=e.element,os(t,"tox-image-selector-loading-spinner-wrapper"),Uo(t,s),Xn(e.element,"img").each((t=>{on(t).catch((e=>{console.error(e)})).finally((()=>{(e=>{ss(e,"tox-image-selector-loading-spinner-wrapper"),Qo(s)})(e.element)}))}))}))])])}},DA=e=>{const t=US.os.isMacOS()||US.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=L(n,(e=>{const t=e.toLowerCase().trim();return be(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},BA=(e,t,o=[wO])=>dO(e,{tag:"div",classes:o},t),IA=e=>({dom:{tag:"div",classes:[SO]},components:[Og(tO.translate(e))]}),FA=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),RA=(e,t)=>({dom:{tag:"div",classes:[SO]},components:[{dom:{tag:e.tag,styles:e.styles},components:[Og(tO.translate(t))]}]}),NA=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[Og(DA(e))]}),zA=e=>BA("checkmark",e,["tox-collection__item-checkmark"]),LA=(e,t)=>{const o=e.map((e=>({attributes:{id:Ne("menu-item"),"aria-label":tO.translate(e)}}))).getOr({});return{tag:"div",classes:[fO,bO].concat(t),...o}},VA=e=>({dom:{tag:"label"},components:[Og(e)]}),HA=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.value,s=e.iconContent.map((e=>((e,t,o)=>{const n=t();return aO(e,n).or(o).getOrThunk(sO(n))})(e,t.icons,o))),r=e.ariaLabel.map((e=>({"aria-label":t.translate(e),"data-mce-name":e}))).getOr({});return{dom:(()=>{const e=vO,t=s.getOr(""),o={tag:"div",attributes:r,classes:[e]};return"custom"===n?{...o,tag:"button",classes:[...o.classes,"tox-swatches__picker-btn"],innerHtml:t}:"remove"===n?{...o,classes:[...o.classes,"tox-swatch--remove"],innerHtml:t}:g(n)?{...o,attributes:{...o.attributes,"data-mce-color":n},styles:{"background-color":n},innerHtml:t}:o})(),optComponents:[]}})(e,t,n):"img"===e.presets?(e=>{var t,o;return{dom:LA(e.ariaLabel,["tox-collection__item-image-selector"]),optComponents:[A.some((t=e.iconContent.getOrDie(),o={tag:"div",classes:["tox-collection__item-image"],checkMark:e.checkMark},MA(o,t))),e.labelContent.map(VA)]}})(e):((e,t,o,n)=>{const s={tag:"div",classes:[wO]},r=o?e.iconContent.map((e=>dO(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>IA),(e=>be(e,"style")?C(RA,e.style):IA)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(FA(e,[SO]))));return{dom:LA(e.ariaLabel,[]),optComponents:[r,l,e.shortcutContent.map(NA),a,e.caret,e.labelContent.map(VA)]}})(e,t,o,n),PA=(e,t,o)=>fe(e,"tooltipWorker").map((e=>[iv.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:kc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{iv.setComponents(t,[_g({element:ct(e)})])}))}})])).getOrThunk((()=>o.map((e=>[iv.config({...t.providers.tooltips.getConfig({tooltipText:e}),mode:"follow-highlight"})])).getOr([]))),UA=(e,t)=>{const o=(e=>HS.DOM.encode(e))(tO.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},WA=(e,t)=>L(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":_O,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[OO,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,WA(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>F(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return FA(UA(e.text,n),e.classes)}})),$A=(e,t,o,n,s,r,a,i=!0)=>{const l=HA({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),labelContent:e.label,ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some(zA(a.icons)):A.none(),caret:A.none(),value:e.value},a,i),c=e.text.filter(y(!t)).map((e=>iv.config(a.tooltips.getConfig({tooltipText:a.translate(e)}))));return Le(EA({context:e.context,data:AA(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Yb.set(e,t)},isActive:()=>Yb.isOn(e),isEnabled:()=>!tp.isDisabled(e),setEnabled:t=>tp.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[...c.toArray()]},l,r,a),{toggling:{toggleClass:yO,toggleOnExecute:!1,selected:e.active,exclusive:!0}})},GA=e=>({value:YA(e)}),jA=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,qA=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,XA=e=>jA.test(e)||qA.test(e),YA=e=>Ke(e,"#").toUpperCase(),KA=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},JA=e=>{const t=KA(e.red)+KA(e.green)+KA(e.blue);return GA(t)},QA=(e,t,o)=>({hue:e,saturation:t,value:o}),ZA=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,QA(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,QA(Math.round(t),Math.round(100*o),Math.round(100*n)))},eM=Math.min,tM=Math.max,oM=Math.round,nM=/^\s*rgb\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*\)\s*$/i,sM=/^\s*rgba\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*((?:\d?\.\d+|\d+)%?)\s*\)\s*$/i,rM=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),aM=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},iM=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=tM(0,eM(r,1)),a=tM(0,eM(a,1)),0===r)return t=o=n=oM(255*a),rM(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=oM(255*(t+d)),o=oM(255*(o+d)),n=oM(255*(n+d)),rM(t,o,n,1)},lM=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(jA,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=qA.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return rM(o,n,s,1)},cM=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return rM(s,r,a,i)},dM=e=>{const t=nM.exec(e);if(null!==t)return A.some(cM(t[1],t[2],t[3],"1"));const o=sM.exec(e);return null!==o?A.some(cM(o[1],o[2],o[3],o[4])):A.none()},uM=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,mM=rM(255,0,0,1),gM=e=>JA(iM(e)),pM=(e,t)=>{e.dispatch("ResizeContent",t)},hM=(e,t)=>{e.dispatch("TextColorChange",t)},fM=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),bM=e=>{e.dispatch("ContextToolbarClose")},vM=(e,t)=>()=>{e(),t()},xM=(e,t=E)=>wM(e,"NodeChange",(o=>{o.setEnabled(e.selection.isEditable()&&t())})),yM=(e,t)=>o=>{const n=xM(e)(o),s=((e,t)=>o=>{const n=qe(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},wM=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},SM=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},CM=(e,t)=>()=>e.execCommand(t);var kM=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const OM={},_M=e=>fe(OM,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=kM.getItem(t);if(m(o)){const e=kM.getItem("tinymce-custom-colors");kM.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=kM.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{((e,t)=>{const o=I(e,t);return-1===o?A.none():A.some(o)})(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),kM.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return OM[e]=n,n})),TM=(e,t)=>{_M(e).add(t)},EM="forecolor",AM="hilitecolor",MM=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:e[o],icon:"checkmark",type:"choiceitem"});return t},DM=e=>t=>t.options.get(e),BM="#000000",IM=(e,t)=>t===EM&&e.options.isSet("color_map_foreground")?DM("color_map_foreground")(e):t===AM&&e.options.isSet("color_map_background")?DM("color_map_background")(e):e.options.isSet("color_map_raw")?DM("color_map_raw")(e):DM("color_map")(e),FM=(e,t="default")=>Math.max(5,Math.ceil(Math.sqrt(IM(e,t).length))),RM=(e,t)=>{const o=DM("color_cols")(e),n=FM(e,t);return o===FM(e)?n:o},NM=(e,t="default")=>Math.round(t===EM?DM("color_cols_foreground")(e):t===AM?DM("color_cols_background")(e):DM("color_cols")(e)),zM=DM("custom_colors"),LM=DM("color_default_foreground"),VM=DM("color_default_background"),HM=(e,t)=>{const o=ct(e.selection.getStart()),n="hilitecolor"===t?bs(o,(e=>{if(no(e)){const t=gn(e,"background-color");return $e((e=>dM(e).exists((e=>0!==e.alpha)))(t),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):gn(o,"color");return dM(n).map((e=>"#"+JA(e).value))},PM=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},UM=(e,t,o,n)=>{"custom"===o?JM(e)((o=>{o.each((o=>{TM(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),HM(e,t).getOr(BM)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},WM=(e,t,o)=>e.concat((e=>L(_M(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(PM(o))),$M=(e,t,o)=>n=>{n(WM(e,t,o))},GM=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},jM=(e,t)=>{e.setTooltip(t)},qM=(e,t)=>o=>{const n=HM(e,t);return He(n,o.toUpperCase())},XM=(e,t,o)=>{if(ot(o))return"forecolor"===t?"Text color":"Background color";const n="forecolor"===t?"Text color {0}":"Background color {0}",s=WM(IM(e,t),t,!1),r=$(s,(e=>e.value===o)).getOr({text:""}).text;return e.translate([n,e.translate(r)])},YM=(e,t,o,n)=>{e.ui.registry.addSplitButton(t,{tooltip:XM(e,o,n.get()),presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:qM(e,o),columns:NM(e,o),fetch:$M(IM(e,o),o,zM(e)),onAction:t=>{UM(e,o,n.get(),b)},onItemAction:(s,r)=>{UM(e,o,r,(o=>{n.set(o),hM(e,{name:t,color:o})}))},onSetup:s=>{GM(s,t,n.get());const r=n=>{n.name===t&&(GM(s,n.name,n.color),jM(s,XM(e,o,n.color)))};return e.on("TextColorChange",r),vM(xM(e)(s),(()=>{e.off("TextColorChange",r)}))}})},KM=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:n=>(jM(n,XM(e,o,s.get())),GM(n,t,s.get()),xM(e)(n)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:qM(e,o),initData:{storageKey:o},onAction:n=>{UM(e,o,n.value,(o=>{s.set(o),hM(e,{name:t,color:o})}))}}]})},JM=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},QM=(e,t,o,n,s,r,a,i)=>{const l=qO(t),c=ZM(t,o,n,"color"!==s?"normal":"color",r,a,i);return KO(e,l,c,n,{menuType:s})},ZM=(e,t,o,n,s,r,a)=>Pe(L(e,(i=>{return"choiceitem"===i.type?(l=i,_r("choicemenuitem",WE,l)).fold(XO,(i=>A.some($A(i,1===o,n,t,r(i.value),s,a,qO(e))))):"imageitem"===i.type?(e=>_r("imagemenuitem",YE,e))(i).fold(XO,(e=>A.some(((e,t,o,n,s)=>{const r=HA({presets:"img",textContent:A.none(),htmlContent:A.none(),ariaLabel:e.tooltip,iconContent:A.some(e.url),labelContent:e.label,shortcutContent:A.none(),checkMark:A.some(zA(s.icons)),caret:A.none(),value:e.value},s,!0),a=e.tooltip.map((e=>iv.config(s.tooltips.getConfig({tooltipText:s.translate(e)}))));return Le(EA({context:e.context,data:AA(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Yb.set(e,t)},isActive:()=>Yb.isOn(e),isEnabled:()=>!tp.isDisabled(e),setEnabled:t=>tp.set(e,!t)}),onAction:o=>{t(e.value),o.setActive(!0)},onSetup:e=>(e.setActive(o),b),triggersSubmenu:!1,itemBehaviours:[...a.toArray()]},r,n,s),{toggling:{toggleClass:yO,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(e,t,r(e.value),s,a)))):"resetimage"===i.type?(e=>_r("resetimageitem",KE,e))(i).fold(XO,(i=>A.some($A({...i,type:"choiceitem",text:i.tooltip,icon:A.some(i.icon),label:A.some(i.label)},1===o,n,t,r(i.value),s,a,qO(e))))):A.none();var l}))),eD=(e,t)=>{const o=MO(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+{color:"tox-swatches__row",imageselector:"tox-image-selector__row",listpreview:"tox-collection__group",normal:"tox-collection__group"}[t],previousSelector:e=>"color"===t?Xn(e.element,"[aria-checked=true]"):A.none()}},tD=Ne("cell-over"),oD=Ne("cell-execute"),nD=(e,t,o)=>{const n=o=>Rc(o,oD,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return Ag({dom:{tag:"div",attributes:{role:"button","aria-label":o}},behaviours:rd([ud("insert-table-picker-cell",[Wc(va(),fd.focus),Wc(Na(),n),Wc(Oa(),s),Wc(La(),s)]),Yb.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),fd.config({onFocus:o=>Rc(o,tD,{row:e,col:t})})])})},sD=e=>q(e,(e=>L(e,Mg))),rD=(e,t)=>Og(`${t}x${e}`),aD={inserttable:(e,t)=>{const o=(e=>(t,o)=>e.shared.providers.translate(["{0} columns, {1} rows",o,t]))(t),n=(e=>{const t=[];for(let o=0;o<10;o++){const n=[];for(let t=0;t<10;t++){const s=e(o+1,t+1);n.push(nD(o,t,s))}t.push(n)}return t})(o),s=rD(0,0),r=dv({dom:{tag:"span",classes:["tox-insert-table-picker__label"]},components:[s],behaviours:rd([Zm.config({})])});return{type:"widget",data:{value:Ne("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ey.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:sD(n).concat(r.asSpec()),behaviours:rd([ud("insert-table-picker",[Jc((e=>{Zm.set(r.get(e),[s])})),qc(tD,((e,t,o)=>{const{row:s,col:a}=o.event;((e,t,o)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)Yb.set(e[n][s],n<=t&&s<=o)})(n,s,a),Zm.set(r.get(e),[rD(s+1,a+1)])})),qc(oD,((t,o,n)=>{const{row:s,col:r}=n.event;Fc(t,Ha()),e.onAction({numRows:s+1,numColumns:r+1})}))]),wm.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>WM(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(PM(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r=QM(Ne("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,hO.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),a={...r,markers:MO(s),movement:eD(n,s),showMenuRole:!1};return{type:"widget",data:{value:Ne("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ey.widget(Qv.sketch(a))]}},imageselect:(e,t)=>{const o="imageselector",n=e.initData.columns,s=QM(Ne("menu-value"),e.initData.items,(t=>{e.onAction({value:t})}),n,o,hO.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),r={...s,markers:MO(o),movement:eD(n,o),showMenuRole:!1};return{type:"widget",data:{value:Ne("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem","tox-collection--toolbar"]},autofocus:!0,components:[ey.widget(Qv.sketch(r))]}}},iD=e=>({type:"separator",dom:{tag:"div",classes:[bO,"tox-collection__group-heading"]},components:e.text.map(Og).toArray()}),lD=(e,t,o)=>{LO(e).each((e=>{var n;((e,t)=>{Xo(t.element,"id").each((t=>Go(e.element,"aria-activedescendant",t)))})(e,o),(rs((n=t).element,PO)?A.some(n.element):Xn(n.element,"."+PO)).each((t=>{Xo(t,"id").each((t=>Go(e.element,"aria-controls",t)))}))})),Go(o.element,"aria-selected","true")},cD=(e,t,o)=>{Go(o.element,"aria-selected","false")},dD=e=>Pg.getExistingCoupled(e,"sandbox").bind(zO).map(VO).map((e=>e.fetchPattern)).getOr("");var uD;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(uD||(uD={}));const mD=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,_r("menuitem",JE,i)).fold(XO,(e=>A.some(((e,t,o,n=!0)=>{const s=HA({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),labelContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return EA({context:e.context,data:AA(e),getApi:e=>({isEnabled:()=>!tp.isDisabled(e),setEnabled:t=>tp.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>_r("nestedmenuitem",QE,e))(e).fold(XO,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,BA("chevron-down",a,[CO])):(e=>BA("chevron-right",e,[CO]))(o.icons);var a;const i=HA({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,labelContent:A.none(),caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return EA({context:e.context,data:AA(e),getApi:e=>({isEnabled:()=>!tp.isDisabled(e),setEnabled:t=>tp.set(e,!t),setIconFill:(t,o)=>{Xn(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{Go(e,"fill",o)}))},setTooltip:t=>{const n=o.translate(t);Go(e.element,"aria-label",n)}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>_r("togglemenuitem",ZE,e))(e).fold(XO,(e=>A.some(((e,t,o,n=!0)=>{const s=HA({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),labelContent:A.none(),ariaLabel:e.text,checkMark:A.some(zA(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return Le(EA({context:e.context,data:AA(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Yb.set(e,t)},isActive:()=>Yb.isOn(e),isEnabled:()=>!tp.isDisabled(e),setEnabled:t=>tp.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:yO,toggleOnExecute:!1,selected:e.active},role:e.role.getOrUndefined()})})(a(e),t,r,n))));case"separator":return(e=>_r("separatormenuitem",hE,e))(e).fold(XO,(e=>A.some(iD(e))));case"fancymenuitem":return(e=>_r("fancymenuitem",XE,e))(e).fold(XO,(e=>((e,t)=>fe(aD,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},gD=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||qO(e);return Pe(L(e,(e=>{switch(e.type){case"separator":return(n=e,_r("Autocompleter.Separator",hE,n)).fold(XO,(e=>A.some(iD(e))));case"cardmenuitem":return(e=>_r("cardmenuitem",UE,e))(e).fold(XO,(e=>A.some(((e,t,o,n)=>{const s={dom:LA(e.label,[]),optComponents:[A.some({dom:{tag:"div",classes:[OO,_O]},components:WA(e.items,n)})]};return EA({context:"mode:design",data:AA({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!tp.isDisabled(e),setEnabled:t=>{tp.set(e,!t),V(ms(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(tp)&&tp.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:PA(e.meta,r,A.none()),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>_r("Autocompleter.Item",fE,e))(e).fold(XO,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=HA({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>UA(e,t))):A.none(),ariaLabel:e.text,labelContent:A.none(),iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon),c=e.text.filter((e=>!o&&""!==e));return EA({context:"mode:design",data:AA(e),enabled:e.enabled,getApi:y({}),onAction:t=>s(e.value,e.meta),onSetup:y(b),triggersSubmenu:!1,itemBehaviours:PA(e,a,c)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},pD=(e,t,o,n,s,r)=>{const a=qO(t),i=Pe(L(t,(e=>{const t=e=>mD(e,o,n,(e=>s?!be(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?YO:KO)(e,a,i,1,l)},hD=e=>ax.singleData(e.value,e),fD=e=>vs(ct(e.startContainer),e.startOffset,ct(e.endContainer),e.endOffset),bD=(e,t)=>{const o=Ne("autocompleter"),n=ye(!1),s=ye(!1),r=Xe(),a=Ag(Xx.sketch({dom:{tag:"div",classes:["tox-autocompleter"],attributes:{id:o}},components:[],fireDismissalEventInstead:{},inlineBehaviours:rd([ud("dismissAutocompleter",[Wc(Ya(),(()=>u())),Wc(oi(),((t,o)=>{Xo(o.event.target,"id").each((t=>Go(ct(e.getBody()),"aria-activedescendant",t)))}))])]),lazySink:t.getSink})),i=()=>Xx.isOpen(a),l=s.get,c=()=>{if(i()){Xx.hide(a),e.dom.remove(o,!1);const t=ct(e.getBody());Xo(t,"aria-owns").filter((e=>e===o)).each((()=>{Ko(t,"aria-owns"),Ko(t,"aria-activedescendant")}))}},d=()=>Xx.getContent(a).bind((e=>ee(e.components(),0))),u=()=>e.execCommand("mceAutocompleterClose"),m=s=>{const i=(o=>{const s=se(o,(e=>A.from(e.columns))).getOr(1);return q(o,(o=>{const a=o.items;return gD(a,o.matchText,((t,s)=>{const a={hide:()=>u(),reload:t=>{c(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};e.execCommand("mceAutocompleterRefreshActiveRange"),r.get().each((e=>{n.set(!0),o.onAction(a,e,t,s),n.set(!1)}))}),s,hO.BUBBLE_TO_SANDBOX,t,o.highlightOn)}))})(s);i.length>0?(((t,o)=>{const n=se(t,(e=>A.from(e.columns))).getOr(1);Xx.showMenuAt(a,{anchor:{type:"selection",getSelection:()=>r.get().map(fD),root:ct(e.getBody())}},((e,t,o,n)=>{const s=eD(t,n),r=MO(n);return{data:hD({...e,movement:s,menuBehaviours:cA("auto"!==t?[]:[Jc(((e,t)=>{lA(e,4,r.item).each((({numColumns:t,numRows:o})=>{wm.setGridSize(e,o,t)}))}))])}),menu:{markers:MO(n),fakeFocus:o===uD.ContentFocus}}})(KO("autocompleter-value",!0,o,n,{menuType:"normal"}),n,uD.ContentFocus,"normal")),d().each(Gd.highlightFirst)})(s,i),Go(ct(e.getBody()),"aria-owns",o),e.inline||g()):c()},g=()=>{e.dom.get(o)&&e.dom.remove(o,!1);const t=e.getDoc().documentElement,n=e.selection.getNode(),s=(e=>Zo(e,!0))(a.element);un(s,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px",top:`${n.offsetTop}px`,left:`${n.offsetLeft}px`}),e.dom.add(t,s.dom),Xn(s,'[role="menu"]').each((e=>{vn(e,"position"),vn(e,"max-height")}))};e.on("AutocompleterStart",(({lookupData:e})=>{s.set(!0),n.set(!1),m(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>m(e))),e.on("AutocompleterUpdateActiveRange",(({range:e})=>r.set(e))),e.on("AutocompleterEnd",(()=>{c(),s.set(!1),n.set(!1),r.clear()}));((e,t)=>{const o=(e,t)=>{Rc(e,wa(),{raw:t})},n=()=>e.getMenu().bind(Gd.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(Nc),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Gd.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(()=>{!e.isActive()||e.isProcessingAction()||t.queryCommandState("mceAutoCompleterInRange")||e.cancelIfNecessary()}))})({cancelIfNecessary:u,isMenuOpen:i,isActive:l,isProcessingAction:n.get,getMenu:d},e)};var vD=tinymce.util.Tools.resolve("tinymce.html.Entities");const xD=(e,t,o,n)=>{const s=yD(e,t,o,n);return Ux.sketch(s)},yD=(e,t,o,n)=>({dom:wD(o),components:e.toArray().concat([t]),fieldBehaviours:rd(n)}),wD=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),SD=(e,t)=>Ux.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Og(t.translate(e))]}),CD=Ne("form-component-change"),kD=Ne("form-component-input"),OD=Ne("form-close"),_D=Ne("form-cancel"),TD=Ne("form-action"),ED=Ne("form-submit"),AD=Ne("form-block"),MD=Ne("form-unblock"),DD=Ne("form-tabchange"),BD=Ne("form-resize"),ID=(e,t,o)=>{const n=e.label.map((e=>SD(e,t))),s=t.icons(),r=e=>(t,o)=>{Yn(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,qo(n,"data-collection-item-value"))}))},a=r(((o,n,s,r)=>{n.stop(),t.checkUiComponentContext("mode:design").shouldDisable||t.isDisabled()||Rc(o,TD,{name:e.name,value:r})})),i=[Wc(va(),r(((e,t,o)=>{Fo(o,!0)}))),Wc(Oa(),a),Wc(La(),a),Wc(xa(),r(((e,t,o)=>{Xn(e.element,"."+kO).each((e=>{ss(e,kO)})),os(o,kO)}))),Wc(ya(),r((e=>{Xn(e.element,"."+kO).each((e=>{ss(e,kO),Ro(e)}))}))),ed(r(((t,o,n,s)=>{Rc(t,TD,{name:e.name,value:s})})))],l=(e,t)=>L(ms(e.element,".tox-collection__item"),t),c=Ux.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:rd([tp.config({disabled:()=>t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{l(e,(e=>{os(e,"tox-collection__item--state-disabled"),Go(e,"aria-disabled",!0)}))},onEnabled:e=>{l(e,(e=>{ss(e,"tox-collection__item--state-disabled"),Ko(e,"aria-disabled")}))}}),kA((()=>t.checkUiComponentContext(e.context))),Zm.config({}),iv.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{Xn(e.element,"."+kO+"[data-mce-tooltip]").each((o=>{Xo(o,"data-mce-tooltip").each((o=>{iv.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-keyboard-focus",anchor:e=>({type:"node",node:Xn(e.element,"."+kO).orThunk((()=>xt(".tox-collection__item"))),root:e.element,layouts:{onLtr:y([jl,Gl,Pl,Wl,Ul,$l]),onRtl:y([jl,Gl,Pl,Wl,Ul,$l])},bubble:wl(0,-2,{})})}),Yh.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const r=t.checkUiComponentContext("mode:design").shouldDisable||t.isDisabled()?" tox-collection__item--state-disabled":"",a=L(n,(t=>{const o=tO.translate(t.text),n=1===e.columns?`<div class="tox-collection__item-label">${o}</div>`:"",a=`<div class="tox-collection__item-icon">${(e=>{var t;return null!==(t=s[e])&&void 0!==t?t:e})(t.icon)}</div>`,i={_:" "," - ":" ","-":" "},l=o.replace(/\_| \- |\-/g,(e=>i[e]));return`<div data-mce-tooltip="${l}" class="tox-collection__item${r}" tabindex="-1" data-collection-item-value="${vD.encodeAllRaw(t.value)}" aria-label="${l}">${a}${n}</div>`})),i="auto"!==e.columns&&e.columns>1?z(a,e.columns):[a],l=L(i,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));tn(o.element,l.join(""))})(o,n),"auto"===e.columns&&lA(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{wm.setGridSize(o,e,t)})),Fc(o,BD)}}),Pb.config({}),wm.config((d=e.columns,1===d?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===d?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${bO}`}})),ud("collection-events",i)]),eventOrder:{[Na()]:["disabling","alloy.base.behaviour","collection-events"],[xa()]:["collection-events","tooltipping"]}});var d;return xD(n,c,["tox-form__group--collection"],[])},FD=Ne("color-input-change"),RD=Ne("color-swatch-change"),ND=Ne("color-picker-cancel"),zD=()=>zg.config({find:A.some}),LD=e=>zg.config({find:t=>bo(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),VD=vr([Qr("preprocess",w),Qr("postprocess",w)]),HD=(e,t)=>{const o=Er("RepresentingConfigs.memento processors",VD,t);return Yh.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=Yh.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);Yh.setValue(r,s)}}})},PD=(e,t,o)=>Yh.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),UD=(e,t,o)=>PD(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),WD=e=>Yh.config({store:{mode:"memory",initialValue:e}}),$D=Ne("rgb-hex-update"),GD=Ne("slider-update"),jD=Ne("palette-update"),qD=Ne("valid-input"),XD=Ne("invalid-input"),YD=Ne("validating-input"),KD="colorcustom.rgb.",JD={isEnabled:E,setEnabled:b,immediatelyShow:b,immediatelyHide:b},QD=(e,t,o,n,s,r)=>{const a=(e,t)=>{const o=t.get();e!==o.isEnabled()&&(o.setEnabled(e),e?o.immediatelyShow():o.immediatelyHide())},i=(o,n,s)=>Kh.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Rc(e,YD,{type:o})},onValid:e=>{a(!1,s),Rc(e,qD,{type:o,value:Yh.getValue(e)})},onInvalid:e=>{a(!0,s),Rc(e,XD,{type:o,value:Yh.getValue(e)})}},validator:{validate:t=>{const o=Yh.getValue(t),s=n(o)?Ae.value(!0):Ae.error(e("aria.input.invalid"));return _e(s)},validateOnLoad:!1}}),l=(o,n,a,l,c)=>{const d=ye(JD),u=e(KD+"range"),m=Ux.parts.label({dom:{tag:"label"},components:[Og(a)]}),g=Ux.parts.field({data:c,factory:Zx,inputAttributes:{type:"text","aria-label":l,..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:rd([i(n,o,d),Pb.config({}),iv.config({...s({tooltipText:"",onSetup:e=>{d.set({isEnabled:()=>iv.isEnabled(e),setEnabled:t=>iv.setEnabled(e,t),immediatelyShow:()=>iv.immediateOpenClose(e,!0),immediatelyHide:()=>iv.immediateOpenClose(e,!1)}),iv.setEnabled(e,!1)},onShow:(o,s)=>{iv.setComponents(o,[{dom:{tag:"p",classes:[t("rgb-warning-note")]},components:[Og(e("hex"===n?"colorcustom.rgb.invalidHex":"colorcustom.rgb.invalid"))]}])}})})]),onSetValue:e=>{Kh.isInvalid(e)&&Kh.run(e).get(b)}}),p=Ne("aria-invalid"),h=dv(r("invalid",A.some(p),"warning")),f=[m,g,dv({dom:{tag:"div",classes:[t("invalid-icon")]},components:[h.asSpec()]}).asSpec()],v="hex"!==n?[Ux.parts["aria-descriptor"]({text:u})]:[],x=f.concat(v);return{dom:{tag:"div",attributes:{role:"presentation"},classes:[t("rgb-container")]},components:x}},c=(e,t)=>{const o=t.red,n=t.green,s=t.blue;Yh.setValue(e,{red:o,green:n,blue:s})},d=dv({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),u=(e,t)=>{d.getOpt(e).each((e=>{dn(e.element,"background-color","#"+t.value)}))},m=nh({factory:()=>{const s={red:ye(A.some(255)),green:ye(A.some(255)),blue:ye(A.some(255)),hex:ye(A.some("ffffff"))},r=e=>s[e].get(),a=(e,t)=>{s[e].set(t)},i=e=>{const t=e.red,o=e.green,n=e.blue;a("red",A.some(t)),a("green",A.some(o)),a("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?a(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=GA(t);a("hex",A.some(n.value));const s=lM(n);c(e,s),i(s),Rc(e,$D,{hex:n}),u(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);a(t,A.some(n)),r("red").bind((e=>r("green").bind((t=>r("blue").map((o=>rM(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=JA(t);return Ax.getField(e,"hex").each((t=>{fd.isFocused(t)||Yh.setValue(e,{hex:o.value})})),o})(e,t);Rc(e,$D,{hex:o}),u(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(KD+t+".label"),description:e(KD+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return Le(Ax.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",Ux.sketch(l(aM,"red",h.label,h.description,255))),o.field("green",Ux.sketch(l(aM,"green",f.label,f.description,255))),o.field("blue",Ux.sketch(l(aM,"blue",b.label,b.description,255))),o.field("hex",Ux.sketch(l(XA,"hex",v.label,v.description,"ffffff"))),d.asSpec()],formBehaviours:rd([Kh.config({invalidClass:t("form-invalid")}),ud("rgb-form-events",[Wc(qD,g),Wc(XD,m),Wc(YD,m)])])}))),{apis:{updateHex:(e,t)=>{Yh.setValue(e,{hex:t.value}),((e,t)=>{const o=lM(t);c(e,o),i(o)})(e,t),u(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return m},ZD=(e,t,o,n)=>{const s=nh({name:"ColourPicker",configFields:[Rr("dom"),Qr("onValidHex",b),Qr("onInvalidHex",b)],factory:s=>{const r=QD(e,t,s.onValidHex,s.onInvalidHex,o,n),a=((e,t)=>{const o=Pw.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=Pw.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)},r=nh({factory:r=>{const a=y({x:0,y:0}),i=rd([zg.config({find:A.some}),fd.config({})]);return Pw.sketch({dom:{tag:"div",attributes:{role:"slider","aria-valuetext":e(["Saturation {0}%, Brightness {1}%",0,0])},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:a},rounded:!1,components:[o,n],onChange:(t,o,n)=>{h(n)||Go(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",Math.floor(n.x),Math.floor(100-n.y)])),Rc(t,jD,{value:n})},onInit:(e,t,o,n)=>{s(o.element.dom,uM(mM))},sliderBehaviours:i})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=QA(t,100,100),r=iM(n);s(o,uM(r))})(t,o)},setThumb:(t,o,n)=>{((t,o)=>{const n=ZA(lM(o));Pw.setValue(t,{x:n.saturation,y:100-n.value}),Go(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",n.saturation,n.value]))})(o,n)}},extraApis:{}});return r})(e,t),i={paletteRgba:ye(mM),paletteHue:ye(0)},l=dv(((e,t)=>{const o=Pw.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=Pw.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return Pw.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"slider","aria-valuemin":0,"aria-valuemax":360,"aria-valuenow":120}},rounded:!1,model:{mode:"y",getInitialValue:y(0)},components:[o,n],sliderBehaviours:rd([fd.config({})]),onChange:(e,t,o)=>{Go(e.element,"aria-valuenow",Math.floor(360-3.6*o)),Rc(e,GD,{value:o})}})})(0,t)),c=dv(a.sketch({})),d=dv(r.sketch({})),u=(e,t,o)=>{c.getOpt(e).each((e=>{a.setHue(e,o)}))},m=(e,t)=>{d.getOpt(e).each((e=>{r.updateHex(e,t)}))},g=(e,t,o)=>{l.getOpt(e).each((e=>{Pw.setValue(e,(e=>100-e/360*100)(o))}))},p=(e,t)=>{c.getOpt(e).each((e=>{a.setThumb(e,t)}))},f=(e,t,o,n)=>{((e,t)=>{const o=lM(e);i.paletteRgba.set(o),i.paletteHue.set(t)})(t,o),V(n,(n=>{n(e,t,o)}))};return{uid:s.uid,dom:s.dom,components:[c.asSpec(),l.asSpec(),d.asSpec()],behaviours:rd([ud("colour-picker-events",[Wc($D,(()=>{const e=[u,g,p];return(t,o)=>{const n=o.event.hex,s=(e=>ZA(lM(e)))(n);f(t,n,s.hue,e)}})()),Wc(jD,(()=>{const e=[m];return(t,o)=>{const n=o.event.value,s=i.paletteHue.get(),r=QA(s,n.x,100-n.y),a=gM(r);f(t,a,s,e)}})()),Wc(GD,(()=>{const e=[u,m];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=i.paletteRgba.get(),r=ZA(s),a=QA(n,r.saturation,r.value),l=gM(a);f(t,l,n,e)}})())]),zg.config({find:e=>d.getOpt(e)}),wm.config({mode:"acyclic"})])}}});return s},eB={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red channel","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green channel","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue channel","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.rgb.invalid":"Numbers only, 0 to 255","colorcustom.rgb.invalidHex":"Hexadecimal only, 000000 to FFFFFF","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var tB=tinymce.util.Tools.resolve("tinymce.Resource");const oB=e=>be(e,"init");var nB=tinymce.util.Tools.resolve("tinymce.util.Tools");const sB=Ne("browse.files.event"),rB=(e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{V(e,(e=>{e(t,o)}))},r=dv({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:rd([ud("input-file-events",[Xc(Oa()),Xc(La())])])}),a=e.label.map((e=>SD(e,t))),i=Ux.parts.field({factory:_v,dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[Og(t.translate("Browse for an image")),r.asSpec()],action:e=>{r.get(e).element.dom.click()},buttonBehaviours:rd([zD(),WD(o.getOr([])),Pb.config({}),uA((()=>t.checkUiComponentContext(e.context).shouldDisable)),kA((()=>t.checkUiComponentContext(e.context)))])}),l={dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:rd([tp.config({disabled:()=>t.checkUiComponentContext(e.context).shouldDisable}),kA((()=>t.checkUiComponentContext(e.context))),Yb.config({toggleClass:"dragenter",toggleOnExecute:!1}),ud("dropzone-events",[Wc("dragenter",s([n,Yb.toggle])),Wc("dragleave",s([n,Yb.toggle])),Wc("dragover",n),Wc("drop",s([n,(e,t)=>{var o;if(!tp.isDisabled(e)){const n=t.event.raw;Rc(e,sB,{files:null===(o=n.dataTransfer)||void 0===o?void 0:o.files})}}])),Wc(ka(),((e,t)=>{const o=t.event.raw.target;Rc(e,sB,{files:o.files})}))])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[Og(t.translate("Drop an image here"))]},i]}]};return xD(a,l,["tox-form__group--stretched"],[ud("handle-files",[Wc(sB,((o,n)=>{Ux.getField(o).each((o=>{var s,r;s=o,(r=n.event.files)&&(Yh.setValue(s,((e,t)=>{const o=nB.explode(t.getOption("images_file_types"));return P(ne(e),(e=>R(o,(t=>Ze(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(r,t)),Rc(s,CD,{name:e.name}))}))}))])])},aB=Ne("alloy-fake-before-tabstop"),iB=Ne("alloy-fake-after-tabstop"),lB=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:rd([fd.config({ignore:!0}),Pb.config({})])}),cB=(e,t)=>({dom:{tag:"div",classes:["tox-navobj",...e.getOr([])]},components:[lB([aB]),t,lB([iB])],behaviours:rd([LD(1)])}),dB=(e,t)=>{Rc(e,wa(),{raw:{which:9,shiftKey:t}})},uB=(e,t)=>{const o=t.element;rs(o,aB)?dB(e,!0):rs(o,iB)&&dB(e,!1)},mB=e=>ps(e,["."+aB,"."+iB].join(","),T),gB=Ne("update-dialog"),pB=Ne("update-title"),hB=Ne("update-body"),fB=Ne("update-footer"),bB=Ne("body-send-message"),vB=Ne("dialog-focus-shifted"),xB=Jt().browser,yB=xB.isSafari(),wB=xB.isFirefox(),SB=yB||wB,CB=xB.isChromium(),kB=({scrollTop:e,scrollHeight:t,clientHeight:o})=>Math.ceil(e)+o>=t,OB=(e,t)=>e.scrollTo(0,"bottom"===t?99999999:t),_B=(e,t,o)=>{const n=e.dom;A.from(n.contentDocument).fold(o,(e=>{let o=0;const s=((e,t)=>{const o=e.body;return A.from(!/^<!DOCTYPE (html|HTML)/.test(t)&&(!CB&&!yB||g(o)&&(0!==o.scrollTop||Math.abs(o.scrollHeight-o.clientHeight)>1))?o:e.documentElement)})(e,t).map((e=>(o=e.scrollTop,e))).forall(kB),r=()=>{const e=n.contentWindow;g(e)&&(s?OB(e,"bottom"):!s&&SB&&0!==o&&OB(e,o))};yB&&n.addEventListener("load",r,{once:!0}),e.open(),e.write(t),e.close(),yB||r()}))},TB=$e(SB,yB?500:200).map((e=>((e,t)=>{let o=null,n=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null,n=null)},throttle:(...s)=>{n=s,c(o)&&(o=setTimeout((()=>{const t=n;o=null,n=null,e.apply(null,t)}),t))}}})(_B,e))),EB=Ne("toolbar.button.execute"),AB=Ne("common-button-display-events"),MB={[Na()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events","tooltipping"],[qa()]:["toolbar-button-events",AB],[Xa()]:["toolbar-button-events","dropdown-events","tooltipping"],[pa()]:["focusing","alloy.base.behaviour",AB]},DB=e=>dn(e.element,"width",gn(e.element,"width")),BB=(e,t,o)=>dO(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),IB=(e,t)=>BB(e,t,[]),FB=(e,t)=>BB(e,t,[Zm.config({})]),RB=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[Og(o.translate(e))],behaviours:rd([Zm.config({})])}),NB=Ne("update-menu-text"),zB=Ne("update-menu-icon"),LB=Ne("update-tooltip-text"),VB=(e,t,o,n)=>{const s=ye(b),r=ye(e.tooltip),a=e.text.map((e=>dv(RB(e,t,o.providers)))),i=e.icon.map((e=>dv(FB(e,o.providers.icons)))),l=(e,t)=>{const o=Yh.getValue(e);return fd.focus(o),Rc(o,"keydown",{raw:t.event.raw}),kx.close(o),A.some(!0)},c=e.role.fold((()=>({})),(e=>({role:e}))),d=A.from(e.listRole).map((e=>({listRole:e}))).getOr({}),u=e.ariaLabel.fold((()=>({})),(e=>({"aria-label":o.providers.translate(e)}))),m=dO("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),p=Ne("common-button-display-events"),h="dropdown-events",f=dv(kx.sketch({...e.uid?{uid:e.uid}:{},...c,...d,dom:{tag:"button",classes:[t,`${t}--select`].concat(L(e.classes,(e=>`${t}--${e}`))),attributes:{...u,...g(n)?{"data-mce-name":n}:{}}},components:TA([i.map((e=>e.asSpec())),a.map((e=>e.asSpec())),A.some(m)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{LO(e).each((e=>fd.focus(e)))})(n)},dropdownBehaviours:rd([...e.dropdownBehaviours,uA((()=>e.disabled||o.providers.checkUiComponentContext(e.context).shouldDisable)),kA((()=>o.providers.checkUiComponentContext(e.context))),lv.config({}),Zm.config({}),...e.tooltip.map((t=>iv.config(o.providers.tooltips.getConfig({tooltipText:o.providers.translate(t),onShow:t=>{if(Ue(r.get(),e.tooltip,((e,t)=>t!==e)).getOr(!1)){const e=o.providers.translate(r.get().getOr(""));iv.setComponents(t,o.providers.tooltips.getComponents({tooltipText:e}))}}})))).toArray(),ud(h,[hA(e,s),fA(e,s)]),ud(p,[Jc(((t,o)=>{"listbox"!==e.listRole&&DB(t)}))]),ud("update-dropdown-width-variable",[Wc(ja(),((e,t)=>kx.close(e)))]),ud("menubutton-update-display-text",[Wc(NB,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{Zm.set(e,[Og(o.providers.translate(t.event.text))])}))})),Wc(zB,((e,t)=>{i.bind((t=>t.getOpt(e))).each((e=>{Zm.set(e,[FB(t.event.icon,o.providers.icons)])}))})),Wc(LB,((e,t)=>{const n=o.providers.translate(t.event.text);Go(e.element,"aria-label",n),r.set(A.some(t.event.text))}))])]),eventOrder:Le(MB,{[pa()]:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[qa()]:["toolbar-button-events",iv.name(),h,p]}),sandboxBehaviours:rd([wm.config({mode:"special",onLeft:l,onRight:l}),ud("dropdown-sandbox-events",[Wc(FO,((e,t)=>{(e=>{const t=Yh.getValue(e),o=zO(e).map(VO);kx.refetch(t).get((()=>{const e=Pg.getCoupled(t,"sandbox");o.each((t=>zO(e).each((e=>((e,t)=>{Yh.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),Wc(RO,((e,t)=>{((e,t)=>{(e=>bb.getState(e).bind(Gd.getHighlighted).bind(Gd.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...IO(0,e.columns,e.presets),fakeFocus:e.searchable,..."listbox"===e.listRole?{}:{onHighlightItem:lD,onCollapseMenu:(e,t,o)=>{Gd.getHighlighted(o).each((t=>{lD(e,o,t)}))},onDehighlightItem:cD}}},getAnchorOverrides:()=>({maxHeightFunction:(e,t)=>{Cc()(e,t-10)}}),fetch:t=>Oe(C(e.fetch,t))}));return f.asSpec()},HB=e=>"separator"===e.type,PB={type:"separator"},UB=(e,t)=>{const o=((e,t)=>{const o=W(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!HB(e[e.length-1])?e.concat([PB]):e:be(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&HB(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return U(o,((e,o)=>{if((e=>be(e,"getSubmenuItems"))(o)){const n=(e=>{const t=fe(e,"value").getOrThunk((()=>Ne("generated-menu-item")));return Le({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=UB(o,t);return{item:e,menus:Le(n.menus,{[e.value]:n.items}),expansions:Le(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:Le(e.menus,s.menus),items:[s.item,...e.items],expansions:Le(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},WB=(e,t,o,n)=>{const s=Ne("primary-menu"),r=UB(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=pD(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=le(r.menus,((e,n)=>pD(n,e,t,o,!1,l))),d=Le(c,ia(s,i));return A.from(ax.tieredData(s,d,r.expansions))},$B=e=>!be(e,"items"),GB="data-value",jB=(e,t,o,n,s)=>L(o,(o=>$B(o)?{type:"togglemenuitem",...s?{}:{role:"option"},text:o.text,value:o.value,active:o.value===n,onAction:()=>{Yh.setValue(e,o.value),Rc(e,CD,{name:t}),fd.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>jB(e,t,o.items,n,s)})),qB=(e,t)=>se(e,(e=>$B(e)?$e(e.value===t,e):qB(e.items,t))),XB=e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit},YB=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return Ae.value({value:e,unit:o})}return Ae.error(e)},KB=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>be(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},JB=e=>A.none(),QB=(e,t)=>{const o=YB(e).toOptional(),n=YB(t).toOptional();return Ue(o,n,((e,t)=>KB(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>KB(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(JB))).getOr(JB)},ZB=(e,t)=>{const o=e.label.map((e=>SD(e,t))),n=[tp.config({disabled:()=>e.disabled||t.checkUiComponentContext(e.context).shouldDisable}),kA((()=>t.checkUiComponentContext(e.context))),wm.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(Fc(e,ED),A.some(!0))}),ud("textfield-change",[Wc(Ca(),((t,o)=>{Rc(t,CD,{name:e.name})})),Wc(Fa(),((t,o)=>{Rc(t,CD,{name:e.name})}))]),Pb.config({})],s=e.validation.map((e=>Kh.config({getRoot:e=>po(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=Yh.getValue(t),n=e.validator(o);return _e(!0===n?Ae.value(o):Ae.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(y({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(y({}),(e=>({inputmode:e}))),"data-mce-name":e.name},a=Ux.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:rd(j([n,s])),selectOnFocus:!1,factory:Zx}),i=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[a]}:a,l=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),c=[tp.config({disabled:()=>e.disabled||t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{Ux.getField(e).each(tp.disable)},onEnabled:e=>{Ux.getField(e).each(tp.enable)}}),kA((()=>t.checkUiComponentContext(e.context)))];return xD(o,i,l,c)},eI=e=>({isEnabled:()=>!tp.isDisabled(e),setEnabled:t=>tp.set(e,!t),setActive:t=>{const o=e.element;t?(os(o,"tox-tbtn--enabled"),Go(o,"aria-pressed",!0)):(ss(o,"tox-tbtn--enabled"),Ko(o,"aria-pressed"))},isActive:()=>rs(e.element,"tox-tbtn--enabled"),setTooltip:t=>{Rc(e,LB,{text:t})},setText:t=>{Rc(e,NB,{text:t})},setIcon:t=>Rc(e,zB,{icon:t})}),tI=(e,t,o,n,s=!0,r)=>{const a="bordered"===e.buttonType?["bordered"]:[];return VB({text:e.text,icon:e.icon,tooltip:e.tooltip,ariaLabel:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?dD(t):""};e.fetch((t=>{n(WB(t,hO.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,eI(t))},onSetup:e.onSetup,getApi:e=>eI(e),columns:1,presets:"normal",classes:a,dropdownBehaviours:[...s?[Pb.config({})]:[]],context:e.context},t,o.shared,r)},oI=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{Fo(t.element),Rc(t,TD,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(L(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,context:e.context,onAction:n(e),onSetup:s(e)}})))}},nI=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{"aria-label":e}},components:[Og(e)]}),sI=(e,t,o)=>{e.customStateIcon.each((n=>t.push(iI(n,o.shared.providers.icons,e.customStateIconTooltip.fold((()=>[]),(e=>[iv.config(o.shared.providers.tooltips.getConfig({tooltipText:e}))])),["tox-icon-custom-state"]))))},rI=Ne("leaf-label-event-id"),aI=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>tI(e,"tox-mbtn",r,A.none(),o))),i=[nI(e.title)];return sI(e,i,r),a.each((e=>i.push(e))),_v.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[wa()]:[rI,"keying"]},buttonBehaviours:rd([...o?[Pb.config({})]:[],Yb.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),ob.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?Yb.on:Yb.off)(t)}}}}),ud(rI,[Jc(((t,o)=>{s.each((o=>{(o===e.id?Yb.on:Yb.off)(t)}))})),Wc(wa(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(Gn(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{qn(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(fd.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},iI=(e,t,o,n,s)=>dO(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"].concat(n||[]),behaviours:o,attributes:s},t),lI=Ne("directory-label-event-id"),cI=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>tI(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a=n.shared.providers.icons,iI("chevron-right",a,[]))]},nI(e.title)];var a;sI(e,r,n),s.each((e=>{r.push(e)}));const i=t=>{Gn(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!Yb.isOn(o);Yb.toggle(o),Rc(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return _v.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:i,eventOrder:{[wa()]:[lI,"keying"]},buttonBehaviours:rd([...t?[Pb.config({})]:[],ud(lI,[Wc(wa(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&Gn(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!Yb.isOn(o)&&n||Yb.isOn(o)&&s?(i(e),t.stop()):s&&!Yb.isOn(o)&&(Gn(o.element,".tox-tree--directory").each((e=>{qn(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(fd.focus)}))})),t.stop())}))}))}))])])})},dI=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?aI({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):mI({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:rd([Ib.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),Zm.config({})])}),uI=Ne("directory-event-id"),mI=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=ye(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[cI({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),dI({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:rd([ud(uI,[Jc(((e,t)=>{Yb.set(e,c)})),Wc("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),Yb.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?aI({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):mI({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?Ib.grow(r):Ib.shrink(r),Zm.set(r,c)}})])}},gI=Ne("tree-event-id"),pI=(e,t,o=[],n,s,r,a)=>{const i=t.fold((()=>({})),(e=>({action:e}))),l={buttonBehaviours:rd([dA((()=>!e.enabled||a.checkUiComponentContext(e.context).shouldDisable)),kA((()=>a.checkUiComponentContext(e.context))),Pb.config({}),...r.map((e=>iv.config(a.tooltips.getConfig({tooltipText:a.translate(e)})))).toArray(),ud("button press",[Uc("click")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...i},c=Le(l,{dom:n});return Le(c,{components:s})},hI=(e,t,o,n=[],s)=>{const r={tag:"button",classes:["tox-tbtn"],attributes:{...e.tooltip.map((e=>({"aria-label":o.translate(e)}))).getOr({}),"data-mce-name":s}},a=e.icon.map((e=>IB(e,o.icons))),i=TA([a]);return pI(e,t,n,r,i,e.tooltip,o)},fI=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},bI=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>IB(e,o.icons))),i=[a.getOrThunk((()=>Og(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c={tag:"button",classes:[...fI(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s],attributes:{"aria-label":r,"data-mce-name":e.text}},d=e.icon.map(y(r));return pI(e,t,n,c,i,d,o)},vI=(e,t,o,n=[],s=[])=>{const r=bI(e,A.some(t),o,n,s);return _v.sketch(r)},xI=(e,t)=>o=>{"custom"===t?Rc(o,TD,{name:e,value:{}}):"submit"===t?Fc(o,ED):"cancel"===t?Fc(o,_D):console.error("Unknown button type: ",t)},yI=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,buttonType:"default",type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:oI(n.items,t,o)},r=dv(tI(s,"tox-tbtn",o,A.none(),!0,e.text.or(e.tooltip).getOrUndefined()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=xI(e.name,t),s={...e,context:"cancel"===t?"any":e.context,borderless:!1};return vI(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t,o)=>{var n,s;const r=e.icon.map((e=>FB(e,t.icons))).map(dv),a=e.buttonType.getOr(e.primary?"primary":"secondary"),i={...e,name:null!==(n=e.name)&&void 0!==n?n:"",primary:"primary"===a,tooltip:e.tooltip,enabled:null!==(s=e.enabled)&&void 0!==s&&s,borderless:!1},l=i.tooltip.or(e.text).map((e=>({"aria-label":t.translate(e)}))).getOr({}),c=fI(null!=a?a:"secondary"),d=e.icon.isSome()&&e.text.isSome(),u={tag:"button",classes:[...c.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...d?["tox-button--icon-and-text"]:[]],attributes:{...l,...g(o)?{"data-mce-name":o}:{}}},m=t.translate(e.text.getOr("")),p=Og(m),h=[...TA([r.map((e=>e.asSpec()))]),...e.text.isSome()?[p]:[]],f=pI(i,A.some((o=>{Rc(o,TD,{name:e.name,value:{setIcon:e=>{r.map((n=>n.getOpt(o).each((o=>{Zm.set(o,[FB(e,t.icons)])}))))}}})})),[],u,h,e.tooltip,t);return _v.sketch(f)})(e,o.shared.providers,e.text.or(e.tooltip).getOrUndefined());throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},wI={type:"separator"},SI=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),CI=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),kI=(e,t)=>(e=>L(e,SI))(((e,t)=>P(t,(t=>t.type===e)))(e,t)),OI=e=>kI("header",e.targets),_I=e=>kI("anchor",e.targets),TI=e=>A.from(e.anchorTop).map((e=>CI("<top>",e))).toArray(),EI=e=>A.from(e.anchorBottom).map((e=>CI("<bottom>",e))).toArray(),AI=(e,t)=>{const o=e.toLowerCase();return P(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return Je(n.toLowerCase(),o)||Je(s.toLowerCase(),o)}))},MI=Ne("aria-invalid"),DI=e=>(t,o,n,s,r)=>fe(o,"name").fold((()=>e(o,s,A.none(),r)),(a=>t.field(a,e(o,s,fe(n,a),r)))),BI={bar:DI(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:L(e.items,t.interpreter)}))(e,t.shared))),collection:DI(((e,t,o)=>ID(e,t.shared.providers,o))),alertbanner:DI(((e,t)=>((e,t)=>{const o=iO(e.icon,t.icons);return rh.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:e.url?void 0:o},components:e.url?[_v.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:o,attributes:{title:t.translate(e.iconTooltip)}},action:t=>Rc(t,TD,{name:"alert-banner",value:e.url}),buttonBehaviours:rd([lO()])})]:void 0},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]})})(e,t.shared.providers))),input:DI(((e,t,o)=>((e,t,o)=>ZB({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o,context:e.context},t))(e,t.shared.providers,o))),textarea:DI(((e,t,o)=>((e,t,o)=>ZB({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o,context:e.context},t))(e,t.shared.providers,o))),label:DI(((e,t,o,n)=>((e,t,o)=>{const n="tox-label",s="center"===e.align?[`${n}--center`]:[],r="end"===e.align?[`${n}--end`]:[],a=dv({dom:{tag:"label",classes:[n,...s,...r]},components:[Og(t.providers.translate(e.label))]}),i=L(e.items,t.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[a.asSpec(),...i],behaviours:rd([zD(),Zm.config({}),(l=A.none(),UD(l,en,tn)),wm.config({mode:"acyclic"}),ud("label",[Jc((t=>{e.for.each((e=>{o(e).each((e=>{a.getOpt(t).each((t=>{var o;const n=null!==(o=qo(e.element,"id"))&&void 0!==o?o:Ne("form-field");Go(e.element,"id",n),Go(t.element,"for",n)}))}))}))}))])])};var l})(e,t.shared,n))),iframe:(DF=(e,t,o)=>((e,t,o)=>{const n="tox-dialog__iframe",s=e.transparent?[]:[`${n}--opaque`],r=e.border?["tox-navobj-bordered"]:[],a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...e.sandboxed?{sandbox:"allow-scripts allow-same-origin"}:{}},i=((e,t)=>{const o=ye(e.getOr(""));return{getValue:e=>o.get(),setValue:(e,n)=>{if(o.get()!==n){const o=e.element,s=()=>Go(o,"srcdoc",n);t?TB.fold(y(_B),(e=>e.throttle))(o,n,s):s()}o.set(n)}}})(o,e.streamContent),l=e.label.map((e=>SD(e,t))),c=Ux.parts.field({factory:{sketch:e=>cB(A.from(r),{uid:e.uid,dom:{tag:"iframe",attributes:a,classes:[n,...s]},behaviours:rd([Pb.config({}),fd.config({}),PD(o,i.getValue,i.setValue),ob.config({channels:{[vB]:{onReceive:(e,t)=>{t.newFocus.each((t=>{po(e.element).each((o=>{(yt(e.element,t)?os:ss)(o,"tox-navobj-bordered-focus")}))}))}}}})])})}});return xD(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n,s)=>{const r=Le(t,{source:"dynamic"});return DI(DF)(e,r,o,n,s)}),button:DI(((e,t)=>((e,t)=>{const o=xI(e.name,"custom");return n=A.none(),s=Ux.parts.field({factory:_v,...bI(e,A.some(o),t,[WD(""),zD()])}),xD(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:DI(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=Ux.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:rd([zD(),tp.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{po(e.element).each((e=>os(e,"tox-checkbox--disabled")))},onEnabled:e=>{po(e.element).each((e=>ss(e,"tox-checkbox--disabled")))}}),Pb.config({}),fd.config({}),UD(o,Jn,Kn),wm.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),ud("checkbox-events",[Wc(ka(),((t,o)=>{Rc(t,CD,{name:e.name})}))])])}),r=Ux.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[Og(t.translate(e.label))],behaviours:rd([lv.config({})])}),a=e=>dO("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=dv({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return Ux.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:rd([tp.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable}),kA((()=>t.checkUiComponentContext(e.context)))])})})(e,t.shared.providers,o))),colorinput:DI(((e,t,o)=>((e,t,o,n)=>{const s=Ux.parts.field({factory:Zx,inputClasses:["tox-textfield"],data:n,onSetValue:e=>Kh.run(e).get(b),inputBehaviours:rd([tp.config({disabled:()=>t.providers.isDisabled()||t.providers.checkUiComponentContext(e.context).shouldDisable}),kA((()=>t.providers.checkUiComponentContext(e.context))),Pb.config({}),Kh.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>po(e.element),notify:{onValid:e=>{const t=Yh.getValue(e);Rc(e,FD,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=Yh.getValue(e);if(0===t.length)return _e(Ae.value(!0));{const e=it("span");dn(e,"background-color",t);const o=hn(e,"background-color").fold((()=>Ae.error("blah")),(e=>Ae.value(t)));return _e(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>SD(e,t.providers))),a=(e,t)=>{Rc(e,RD,{value:t})},i=dv(((e,t)=>kx.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:rd([uA((()=>t.providers.isDisabled()||t.providers.checkUiComponentContext(e.context).shouldDisable)),kA((()=>t.providers.checkUiComponentContext(e.context))),lv.config({}),Pb.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>Oe((t=>e.fetch(t))).map((n=>A.from(hD(Le(QM(Ne("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,hO.CLOSE_ON_EXECUTE,T,t.providers),{movement:eD(e.columns,e.presets)}))))),parts:{menu:IO(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[Ul,Pl,jl],onLtr:()=>[Pl,Ul,jl]},components:[],fetch:$M(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>Fc(t,ND)),(o=>{a(t,o),TM(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))},context:e.context},t));return Ux.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:rd([ud("form-field-events",[Wc(FD,((t,o)=>{i.getOpt(t).each((e=>{dn(e.element,"background-color",o.event.color)})),Rc(t,CD,{name:e.name})})),Wc(RD,((e,t)=>{Ux.getField(e).each((o=>{Yh.setValue(o,t.event.value),zg.getCurrent(e).each(fd.focus)}))})),Wc(ND,((e,t)=>{Ux.getField(e).each((t=>{zg.getCurrent(e).each(fd.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:DI(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=ZD((e=>t=>r(t)?e.translate(eB[t]):e.translate(t))(t),n,t.tooltips.getConfig,((e,o,n=e,s=e)=>dO(n,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:t.translate(s),"aria-live":"polite",...o.fold((()=>({})),(e=>({id:e})))}},t.icons))),a=dv(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Rc(e,TD,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Rc(e,TD,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[a.asSpec()],behaviours:rd([PD(o,(e=>{const t=a.get(e);return zg.getCurrent(t).bind((e=>Yh.getValue(e).hex)).map((e=>"#"+Ke(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>ee(e,1))),n=a.get(e);zg.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{Yh.setValue(e,{hex:o.getOr("")}),Ax.getField(e,"hex").each((e=>{Fc(e,Ca())}))}))})),zD()])}})(0,t.shared.providers,o))),dropzone:DI(((e,t,o)=>rB(e,t.shared.providers,o))),grid:DI(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:L(e.items,t.interpreter)}))(e,t.shared))),listbox:DI(((e,t,o)=>((e,t,o)=>{const n=R(e.items,(e=>!$B(e))),s=t.shared.providers,r=o.bind((t=>qB(e.items,t))).orThunk((()=>te(e.items).filter($B))),a=e.label.map((e=>SD(e,s))),i=Ux.parts.field({dom:{},factory:{sketch:o=>VB({context:e.context,uid:o.uid,text:r.map((e=>e.text)),icon:A.none(),tooltip:A.none(),role:$e(!n,"combobox"),...n?{}:{listRole:"listbox"},ariaLabel:e.label,fetch:(o,s)=>{const r=jB(o,e.name,e.items,Yh.getValue(o),n);s(WB(r,hO.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:y(b),getApi:y({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Pb.config({}),PD(r.map((e=>e.value)),(e=>qo(e.element,GB)),((t,o)=>{qB(e.items,o).each((e=>{Go(t.element,GB,e.value),Rc(t,NB,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),l={dom:{tag:"div",classes:["tox-listboxfield"]},components:[i]};return Ux.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:j([a.toArray(),[l]]),fieldBehaviours:rd([tp.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{Ux.getField(e).each(tp.disable)},onEnabled:e=>{Ux.getField(e).each(tp.enable)}})])})})(e,t,o))),selectbox:DI(((e,t,o)=>((e,t,o)=>{const n=L(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>SD(e,t))),r=Ux.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:qx,selectBehaviours:rd([tp.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable}),Pb.config({}),ud("selectbox-change",[Wc(ka(),((t,o)=>{Rc(t,CD,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(dO("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:j([[r],a.toArray()])};return Ux.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:j([s.toArray(),[i]]),fieldBehaviours:rd([tp.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{Ux.getField(e).each(tp.disable)},onEnabled:e=>{Ux.getField(e).each(tp.enable)}}),kA((()=>t.checkUiComponentContext(e.context)))])})})(e,t.shared.providers,o))),sizeinput:DI(((e,t)=>((e,t)=>{let o=JB;const n=Ne("ratio-event"),s=e=>dO(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable,a=kA((()=>t.checkUiComponentContext(e.context))),i=e.label.getOr("Constrain proportions"),l=t.translate(i),c=jx.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":l,"data-mce-name":i}},components:[s("lock"),s("unlock")],buttonBehaviours:rd([tp.config({disabled:r}),a,Pb.config({}),iv.config(t.tooltips.getConfig({tooltipText:l}))])}),d=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),u=t=>Ux.parts.field({factory:Zx,inputClasses:["tox-textfield"],inputBehaviours:rd([tp.config({disabled:r}),a,Pb.config({}),ud("size-input-events",[Wc(xa(),((e,o)=>{Rc(e,n,{isField1:t})})),Wc(ka(),((t,o)=>{Rc(t,CD,{name:e.name})}))])]),selectOnFocus:!1}),m=e=>({dom:{tag:"label",classes:["tox-label"]},components:[Og(t.translate(e))]}),g=jx.parts.field1(d([Ux.parts.label(m("Width")),u(!0)])),p=jx.parts.field2(d([Ux.parts.label(m("Height")),u(!1)]));return jx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[g,p,d([m("\xa0"),c])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{YB(Yh.getValue(e)).each((e=>{o(e).each((e=>{Yh.setValue(t,XB(e))}))}))},coupledFieldBehaviours:rd([tp.config({disabled:r,onDisabled:e=>{jx.getField1(e).bind(Ux.getField).each(tp.disable),jx.getField2(e).bind(Ux.getField).each(tp.disable),jx.getLock(e).each(tp.disable)},onEnabled:e=>{jx.getField1(e).bind(Ux.getField).each(tp.enable),jx.getField2(e).bind(Ux.getField).each(tp.enable),jx.getLock(e).each(tp.enable)}}),kA((()=>t.checkUiComponentContext("mode:design"))),ud("size-input-events2",[Wc(n,((e,t)=>{const n=t.event.isField1,s=n?jx.getField1(e):jx.getField2(e),r=n?jx.getField2(e):jx.getField1(e),a=s.map(Yh.getValue).getOr(""),i=r.map(Yh.getValue).getOr("");o=QB(a,i)}))])])})})(e,t.shared.providers))),slider:DI(((e,t,o)=>((e,t,o)=>{const n=Pw.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Og(t.translate(e.label))]}),s=Pw.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=Pw.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return Pw.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:y(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:rd([zD(),fd.config({})]),onChoose:(t,o,n)=>{Rc(t,CD,{name:e.name,value:n})},onChange:(t,o,n)=>{Rc(t,CD,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:DI(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=Yh.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":MI,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=Yh.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=AI(a,(e=>L(e,(e=>CI(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,AI(a,OI(t)),AI(a,j([TI(t),_I(t),EI(t)]))],W(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(wI,t)),[])):n;var s}))})(e.filetype,n,o),r=WB(s,hO.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return _e(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(Kh)&&Kh.run(e).get(b)},typeaheadBehaviours:rd([...o.getValidationHandler().map((t=>Kh.config({getRoot:e=>po(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{Go(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=Yh.getValue(o);return De((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=Ae.error(e.message);o(t)}else{const t=Ae.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),tp.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable}),Pb.config({}),ud("urlinput-events",[Wc(Ca(),(t=>{const o=cs(t.element),n=o.trim();n!==o&&ds(t.element,n),"file"===e.filetype&&Rc(t,CD,{name:e.name})})),Wc(ka(),(t=>{Rc(t,CD,{name:e.name}),r(t)})),Wc(Fa(),(t=>{Rc(t,CD,{name:e.name}),r(t)}))])]),eventOrder:{[Ca()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:IO(0,0,"normal")},onExecute:(e,t,o)=>{Rc(t,ED,{})},onItemExecute:(t,o,n,s)=>{r(t),Rc(t,CD,{name:e.name})}},i=Ux.parts.field({...a,factory:RS}),l=e.label.map((e=>SD(e,s))),c=dv(((e,t,o=e,n=e)=>dO(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(MI),"warning")),d=dv({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=Ne("browser.url.event"),g=dv({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:rd([tp.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable})])}),p=dv(vI({context:e.context,name:e.name,icon:A.some("browse"),text:e.picker_text.or(e.label).getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>Fc(e,m)),s,[],["tox-browse-url"]));return Ux.sketch({dom:wD([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:j([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:rd([tp.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{Ux.getField(e).each(tp.disable),p.getOpt(e).each(tp.disable)},onEnabled:e=>{Ux.getField(e).each(tp.enable),p.getOpt(e).each(tp.enable)}}),kA((()=>s.checkUiComponentContext(e.context))),ud("url-input-events",[Wc(m,(t=>{zg.getCurrent(t).each((o=>{const n=Yh.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{Yh.setValue(o,n),Rc(t,CD,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:DI((e=>{const t=Xe(),o=dv({dom:{tag:e.tag}}),n=Xe(),s=!oB(e)&&e.onFocus.isSome()?[fd.config({onFocus:t=>{e.onFocus.each((e=>{e(t.element.dom)}))}}),Pb.config({})]:[];return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:rd([ud("custom-editor-events",[Jc((s=>{o.getOpt(s).each((o=>{(oB(e)?e.init(o.element.dom):tB.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),PD(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),zD()].concat(s)),components:[o.asSpec()]}})),htmlpanel:DI(((e,t)=>((e,t)=>{const o=["tox-form__group",...e.stretched?["tox-form__group--stretched"]:[]],n=ud("htmlpanel",[Jc((t=>{e.onInit(t.element.dom)}))]);return"presentation"===e.presets?rh.sketch({dom:{tag:"div",classes:o,innerHtml:e.html},containerBehaviours:rd([iv.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{Xn(e.element,"[data-mce-tooltip]:hover").orThunk((()=>Lo(e.element))).each((o=>{Xo(o,"data-mce-tooltip").each((o=>{iv.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-normal",anchor:e=>({type:"node",node:Xn(e.element,"[data-mce-tooltip]:hover").orThunk((()=>Lo(e.element).filter((e=>Xo(e,"data-mce-tooltip").isSome())))),root:e.element,layouts:{onLtr:y([jl,Gl,Pl,Wl,Ul,$l]),onRtl:y([jl,Gl,Pl,Wl,Ul,$l])},bubble:wl(0,-2,{})})}),n])}):rh.sketch({dom:{tag:"div",classes:o,innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:rd([Pb.config({}),fd.config({}),n])})})(e,t.shared.providers))),imagepreview:DI(((e,t,o)=>((e,t)=>{const o=ye(t.getOr({url:""})),n=dv({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=dv({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:rd([zD(),PD(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=Tn(e),s=Sn(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Tn(e.element),Sn(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{un(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;t.url!==qo(n,"src")&&(Go(n,"src",t.url),ss(e.element,"tox-imagepreview__loaded")),a(),on(n).then((t=>{e.getSystem().isConnected()&&(os(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:DI(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:L(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:L(n,(e=>({dom:{tag:"tr"},components:L(e,o)})))})],behaviours:rd([Pb.config({}),fd.config({})])};var n,s})(e,t.shared.providers))),tree:DI(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=ye(s),a=ye(e.defaultSelectedId),i=Ne("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?aI({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):mI({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:rd([wm.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),ud(gI,[Wc("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),ob.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),Zm.set(e,l(A.some(t.value),r.get()))}}}}),Zm.config({})])}})(e,t))),panel:DI(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:L(e.items,t.shared.interpreter)}))(e,t)))},II={field:(e,t)=>t,record:y([])},FI=(e,t,o,n,s)=>{const r=Le(n,{shared:{interpreter:t=>RI(e,t,o,r,s)}});return RI(e,t,o,r,s)},RI=(e,t,o,n,s)=>fe(BI,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(r=>r(e,t,o,n,s))),NI=(e,t,o,n)=>RI(II,e,t,o,n),zI={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},LI=(e,t,o)=>{const n={maxHeightFunction:kc()};return()=>o()?{type:"node",root:Co(So(e())),node:A.from(e()),bubble:wl(12,12,zI),layouts:{onRtl:()=>[gc],onLtr:()=>[mc]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:wl(-12,12,zI),layouts:{onRtl:()=>[Pl,Ul,jl],onLtr:()=>[Ul,Pl,jl]},overrides:n}},VI=(e,t,o,n)=>{const s={maxHeightFunction:kc()};return()=>n()?{type:"node",root:Co(So(t())),node:A.from(t()),bubble:wl(12,12,zI),layouts:{onRtl:()=>[pc],onLtr:()=>[pc]},overrides:s}:e?{type:"node",root:Co(So(t())),node:A.from(t()),bubble:wl(0,-Cn(t()),zI),layouts:{onRtl:()=>[Gl],onLtr:()=>[Gl]},overrides:s}:{type:"hotspot",hotspot:o(),bubble:wl(0,0,zI),layouts:{onRtl:()=>[Gl],onLtr:()=>[Gl]},overrides:s}},HI=(e,t,o)=>()=>o()?{type:"node",root:Co(So(e())),node:A.from(e()),layouts:{onRtl:()=>[pc],onLtr:()=>[pc]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[jl],onLtr:()=>[jl]}},PI=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:ct(e),lastCell:ct(t)};return A.some(n)}return A.some(Ss.range(ct(t.startContainer),t.startOffset,ct(t.endContainer),t.endOffset))}}),UI=e=>t=>({type:"node",root:e(),node:t}),WI=(e,t,o,n)=>{const s=PC(e),r=()=>ct(e.getBody()),a=()=>ct(e.getContentAreaContainer()),i=()=>s||!n();return{inlineDialog:LI(a,t,i),inlineBottomDialog:VI(e.inline,a,o,i),banner:HI(a,t,i),cursor:PI(e,r),node:UI(r)}},$I=e=>(t,o)=>{JM(e)(t,o)},GI=e=>()=>zM(e),jI=e=>t=>IM(e,t),qI=e=>t=>NM(e,t),XI=e=>()=>wC(e),YI=e=>ve(e,"items"),KI=e=>ve(e,"format"),JI=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],QI=e=>W(e,((e,t)=>{if(be(t,"items")){const o=QI(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(be(t,"inline")||(e=>be(e,"block"))(t)||(e=>be(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),ZI=e=>eC(e).map((t=>{const o=((e,t)=>{const o=QI(t),n=t=>{V(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return tC(e)?JI.concat(o):o})).getOr(JI),eF=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),tF=(e,t,o,n)=>{const s=t=>L(t,(t=>YI(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:y(t)}})(t):KI(t)?(e=>eF(e,o,n))(t):(e=>{const t=re(e);return 1===t.length&&F(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:Ne(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},oF=e=>{let t=0;const o=e=>[{dom:{tag:"div",classes:["tox-tooltip__body"]},components:[Og(e.tooltipText)]}];return{getConfig:n=>({delayForShow:()=>t>0?60:300,delayForHide:y(300),exclusive:!0,lazySink:e,tooltipDom:{tag:"div",classes:["tox-tooltip","tox-tooltip--up"]},tooltipComponents:o(n),onShow:(e,o)=>{t++,n.onShow&&n.onShow(e,o)},onHide:(e,o)=>{t--,n.onHide&&n.onHide(e,o)},onSetup:n.onSetup}),getComponents:o}},nF=nB.trim,sF=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},rF=sF("true"),aF=sF("false"),iF=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),lF=e=>e.innerText||e.textContent,cF=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&uF(e),dF=e=>e&&/^(H[1-6])$/.test(e.nodeName),uF=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return rF(t)}return!1})(e)&&!aF(e),mF=e=>dF(e)&&uF(e),gF=e=>{var t;const o=(e=>e.id?e.id:Ne("h"))(e);return iF("header",null!==(t=lF(e))&&void 0!==t?t:"","#"+o,(e=>dF(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},pF=e=>{const t=e.id||e.name,o=lF(e);return iF("anchor",o||"#"+t,"#"+t,0,b)},hF=e=>nF(e.title).length>0,fF=e=>{const t=(e=>{const t=L(ms(ct(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return P((e=>L(P(e,mF),gF))(t).concat((e=>L(P(e,cF),pF))(t)),hF)},bF="tinymce-url-history",vF=e=>r(e)&&/^https?/.test(e),xF=e=>a(e)&&pe(e,(e=>{return!(l(t=e)&&t.length<=5&&X(t,vF));var t})).isNone(),yF=()=>{const e=kM.getItem(bF);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+bF+" was not valid JSON",e),{};throw e}return xF(t)?t:(console.log("Local storage "+bF+" was not valid format",t),{})},wF=e=>{const t=yF();return fe(t,e).getOr([])},SF=(e,t)=>{if(!vF(e))return;const o=yF(),n=fe(o,t).getOr([]),s=P(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!xF(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));kM.setItem(bF,JSON.stringify(e))})(o)},CF=e=>!!e,kF=e=>le(nB.makeMap(e,/[, ]/),CF),OF=e=>A.from(pC(e)),_F=e=>A.from(e).filter(r).getOrUndefined(),TF=e=>({getHistory:wF,addToHistory:SF,getLinkInformation:()=>(e=>vC(e)?A.some({targets:fF(e.getBody()),anchorTop:_F(xC(e)),anchorBottom:_F(yC(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(hC(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(bC(e)).filter(CF).map(kF);return OF(e).fold(T,(e=>t.fold(E,(e=>re(e).length>0&&e))))})(e);return d(o)?o?OF(e):A.none():o[t]?OF(e):A.none()})(e,t).map((o=>n=>Oe((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),EF=b,AF=T,MF=y([]);var DF,BF=Object.freeze({__proto__:null,setup:EF,isDocked:AF,getBehaviours:MF});const IF=y(Ne("toolbar-height-change")),FF={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},RF="tox-tinymce--toolbar-sticky-on",NF="tox-tinymce--toolbar-sticky-off",zF=(e,t)=>F(Ep.getModes(e),t),LF=e=>{const t=e.element;po(t).each((o=>{const n="padding-"+Ep.getModes(e)[0];if(Ep.isDocked(e)){const e=Tn(o);dn(t,"width",e+"px"),dn(o,n,(e=>Cn(e)+(parseInt(gn(e,"margin-top"),10)||0)+(parseInt(gn(e,"margin-bottom"),10)||0))(t)+"px")}else vn(t,"width"),vn(o,n)}))},VF=(e,t)=>{t?(ss(e,FF.fadeOutClass),as(e,[FF.transitionClass,FF.fadeInClass])):(ss(e,FF.fadeInClass),as(e,[FF.fadeOutClass,FF.transitionClass]))},HF=(e,t)=>{const o=ct(e.getContainer());t?(os(o,RF),ss(o,NF)):(os(o,NF),ss(o,RF))},PF=(e,t)=>{const o=Xe(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||LF(t),HF(e,Ep.isDocked(t)),t.getSystem().broadcastOn([pv()],{}),n().each((e=>e.getSystem().broadcastOn([pv()],{})))},a=e.inline?[]:[ob.config({channels:{[IF()]:{onReceive:LF}}})];return[fd.config({}),Ep.config({contextual:{lazyContext:t=>{const o=Cn(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Rs(ct(n));return QC(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(zF(t,"top")?0:o);return Fs(s.x,n,s.width,e)}),(e=>{const n=zs(s,ZC(e)),r=zF(t,"top")?n.y:n.y+o;return Fs(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>VF(e,!0)))},onShown:e=>{s((e=>is(e,[FF.transitionClass,FF.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=lo(t);zo(o).filter((e=>!yt(t,e))).filter((t=>yt(t,ct(o.dom.body))||wt(e,t))).each((()=>Fo(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>Lo(e).orThunk((()=>t().toOptional().bind((e=>Lo(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>VF(e,!1)))},onHidden:()=>{s((e=>is(e,[FF.transitionClass])))},...FF},lazyViewport:t=>QC(e,t.element).fold((()=>{const o=Ls(),n=uC(e),s=o.y+(zF(t,"top")&&!JC(e)?n:0),r=o.height-(zF(t,"bottom")?n:0);return{bounds:Fs(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:ZC(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:In(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var UF=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(Ep.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(LF)})),e.on("SkinLoaded",(()=>{o().each((e=>{Ep.isDocked(e)?Ep.reset(e):Ep.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(Ep.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{Ep.refresh(t);const o=t.element;kn(o)&&((e,t)=>{const o=lo(t),n=mo(t).dom.innerHeight,s=Rn(o),r=ct(e.elm),a=Ns(r),i=Sn(r),l=a.y,c=l+i,d=In(t),u=Sn(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Nn(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Nn(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{HF(e,!1)}))},isDocked:e=>e().map(Ep.isDocked).getOr(!1),getBehaviours:PF}),WF=nh({factory:(e,t)=>{const o={focus:wm.focusIn,setMenus:(e,o)=>{const n=L(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())},context:"any"},n=HT(o).mapError((e=>Ar(e))).getOrDie();return tI(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));Zm.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:rd([Zm.config({}),ud("menubar-events",[Jc((t=>{e.onSetup(t)})),Wc(va(),((e,t)=>{Xn(e.element,".tox-mbtn--active").each((o=>{Yn(t.event.target,".tox-mbtn").each((t=>{yt(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{kx.expand(e),kx.close(o),fd.focus(e)}))}))}))}))})),Wc(Ja(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{kx.isOpen(o)&&(kx.expand(e),kx.close(o))}))}))}))]),wm.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),Pb.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[Rr("dom"),Rr("uid"),Rr("onEscape"),Rr("backstage"),Qr("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const $F=e=>({element:()=>e.element.dom}),GF=(e,t)=>{const o=L(re(t),(e=>{const o=t[e],n=Tr((e=>_r("sidebar",eA,e))(o));return{name:e,getApi:$F,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return L(o,(t=>{const n=ye(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:cA([hA(t,n),fA(t,n),Wc(Qa(),((e,t)=>{const n=t.event,s=$(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},jF=e=>qw.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:GF(t,e),slotBehaviours:cA([Jc((e=>qw.hideAllSlots(e)))])}))),qF=(e,t)=>{Go(e,"role",t)},XF=e=>zg.getCurrent(e).bind((e=>Ib.isGrowing(e)||Ib.hasGrown(e)?zg.getCurrent(e).bind((e=>$(qw.getSlotNames(e),(t=>qw.isShowing(e,t))))):A.none())),YF=Ne("FixSizeEvent"),KF=Ne("AutoSizeEvent"),JF=e=>zg.getCurrent(e).each((e=>Fo(e.element,!0))),QF=(e,t,o)=>{const n=ye(!1),s=Xe(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?te(s.composedPath()):A.from(s.target)).map(ct).filter(no).exists((e=>rs(e,"mce-pastebin"))))&&(o.preventDefault(),JF(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(ct).each((e=>{t?(Xo(e,o).each((t=>Go(e,n,t))),Go(e,o,-1)):(Ko(e,o),Xo(e,n).each((t=>{Go(e,o,t),Ko(e,n)})))}))})(e,o),o)Fg.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:cv('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),vn(s,"display"),Ko(s,"aria-hidden"),e.hasFocus()&&JF(t);else{const o=zg.getCurrent(t).exists((e=>No(e.element)));Fg.unblock(t),dn(s,"display","none"),Go(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=VS.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},ZF=e=>{const t=e.label.isNone()?e.title.fold((()=>({})),(e=>({attributes:{"aria-label":e}}))):e.label.fold((()=>({})),(e=>({attributes:{"aria-label":e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"].concat(e.label.isSome()?["tox-toolbar__group_with_label"]:[]),...t},components:[...e.label.map((e=>({dom:{tag:"span",classes:["tox-label","tox-label--context-toolbar"]},components:[Og(e)]}))).toArray(),cS.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-item:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:rd([Pb.config({}),fd.config({ignore:!0})])}},eR=e=>cS.sketch(ZF(e)),tR=(e,t)=>{const o=Jc((t=>{const o=L(e.initGroups,eR);Bx.setGroups(t,o)}));return rd([gA((()=>e.providers.checkUiComponentContext("any").shouldDisable)),kA((()=>e.providers.checkUiComponentContext("any"))),wm.config({mode:t,onEscape:e.onEscape,visibilitySelector:".tox-toolbar__overflow",selector:".tox-toolbar__group"}),ud("toolbar-events",[o])])},oR=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":ZF({title:A.none(),label:A.none(),items:[]}),"overflow-button":hI({context:"any",name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("Reveal or hide additional toolbar items"),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers,[],"overflow-button")},splitToolbarBehaviours:tR(e,t)}},nR=e=>{const t=oR(e),o=mS.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return mS.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Ns(t),n=uo(t),s=Ns(n),r=Math.max(n.dom.scrollHeight,s.height);return Fs(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},sR=e=>{const t=bS.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=bS.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=oR(e);return bS.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([IF()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([IF()],{type:"closed"}),e.onToggled(t,!1)}})},rR=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return Bx.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===NS.scrolling?["tox-toolbar--scrolling"]:[])},components:[Bx.parts.groups({})],toolbarBehaviours:tR(e,t)})},aR=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>FB(e,t.icons))).map(dv),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=fI(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(Og),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=TA([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=pI(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{Zm.set(o,[FB(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(os(t,"tox-button--enabled"),Go(t,"aria-pressed",!0)):(ss(t,"tox-button--enabled"),Ko(t,"aria-pressed"))},isActive:()=>rs(o.element,"tox-button--enabled"),focus:()=>Fo(o.element)}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,e.tooltip,t);return _v.sketch(h)})(e,t),iR=Jt().deviceType,lR=iR.isPhone(),cR=iR.isTablet();var dR=sh({name:"silver.View",configFields:[Rr("viewConfig")],partFields:[Zi({factory:{sketch:e=>{let t=!1;const o=L(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:L(e.buttons,(e=>aR(e,t)))}))(o,e.providers)):aR(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...lR||cR?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:rd([fd.config({}),wm.config({mode:"flow",selector:"button, .tox-button",focusInside:Ei.OnEnterOrSpaceMode})]),components:t?o:[rh.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),rh.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[Rr("buttons"),Rr("providers")],name:"header"}),Zi({factory:{sketch:e=>({uid:e.uid,behaviours:rd([fd.config({}),Pb.config({})]),dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>uv.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,behaviours:rd([fd.config({}),wm.config({mode:"cyclic",focusInside:Ei.OnEnterOrSpaceMode})]),apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const uR=(e,t,o)=>ge(t,((t,n)=>{const s=Tr(_r("view",iA,t));return e.slot(n,dR.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[dR.parts.header({buttons:s.buttons,providers:o})]:[],dR.parts.pane({})]}))})),mR=(e,t)=>qw.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:uR(o,e,t),slotBehaviours:cA([Jc((e=>qw.hideAllSlots(e)))])}))),gR=e=>$(qw.getSlotNames(e),(t=>qw.isShowing(e,t))),pR=(e,t,o)=>{qw.getSlot(e,t).each((e=>{dR.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:y(n)}))}))}))};var hR=nh({factory:(e,t)=>{const o={setViews:(e,o)=>{Zm.set(e,[mR(o,t.backstage.shared.providers)])},whichView:e=>zg.getCurrent(e).bind(gR),toggleView:(e,t,o,n)=>zg.getCurrent(e).exists((s=>{const r=gR(s),a=r.exists((e=>n===e)),i=qw.getSlot(s,n).isSome();return i&&(qw.hideAllSlots(s),a?((e=>{const t=e.element;dn(t,"display","none"),Go(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;vn(t,"display"),Ko(t,"aria-hidden")})(e),qw.showSlot(s,n),((e,t)=>{pR(e,t,dR.getOnShow)})(s,n)),r.each((e=>((e,t)=>pR(e,t,dR.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:rd([Zm.config({}),zg.config({find:e=>{const t=Zm.contents(e);return te(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[Rr("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const fR=mv.optional({factory:WF,name:"menubar",schema:[Rr("backstage")]}),bR=mv.optional({factory:{sketch:e=>Mv.sketch({uid:e.uid,dom:e.dom,listBehaviours:rd([wm.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>rR({type:e.type,uid:Ne("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{Bx.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[Rr("dom"),Rr("onEscape")]}),vR=mv.optional({factory:{sketch:e=>{const t=(e=>e.type===NS.sliding?sR:e.type===NS.floating?nR:rR)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[Rr("dom"),Rr("onEscape"),Rr("getSink")]}),xR=mv.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?PF:MF;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:rd(o(t,e.sharedBackstage))}}},name:"header",schema:[Rr("dom")]}),yR=mv.optional({factory:{sketch:e=>{const t=e.promotionLink?[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-upgrade-to-cloud/?utm_campaign=self_hosted_upgrade_promo&utm_source=tiny&utm_medium=referral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u{1f49d}Get all features"}}]:[];return{uid:e.uid,dom:e.dom,components:t}}},name:"promotion",schema:[Rr("dom"),Rr("promotionLink")]}),wR=mv.optional({name:"socket",schema:[Rr("dom")]}),SR=mv.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:rd([Pb.config({}),fd.config({}),Ib.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{zg.getCurrent(e).each(qw.hideAllSlots),Fc(e,KF)},onGrown:e=>{Fc(e,KF)},onStartGrow:e=>{Rc(e,YF,{width:hn(e.element,"width").getOr("")})},onStartShrink:e=>{Rc(e,YF,{width:Tn(e.element)+"px"})}}),Zm.config({}),zg.config({find:e=>{const t=Zm.contents(e);return te(t)}})])}],behaviours:rd([LD(0),ud("sidebar-sliding-events",[Wc(YF,((e,t)=>{dn(e.element,"width",t.event.width)})),Wc(KF,((e,t)=>{vn(e.element,"width")}))])])})},name:"sidebar",schema:[Rr("dom")]}),CR=mv.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:rd([Zm.config({}),Fg.config({focus:!1}),zg.config({find:e=>te(e.components())})]),components:[]})},name:"throbber",schema:[Rr("dom")]}),kR=mv.optional({factory:hR,name:"viewWrapper",schema:[Rr("backstage")]}),OR=mv.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var _R=sh({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s=e=>{jn(e,".tox-statusbar").each((e=>{"none"===gn(e,"display")&&"true"===qo(e,"aria-hidden")?(vn(e,"display"),Ko(e,"aria-hidden")):(dn(e,"display","none"),Go(e,"aria-hidden","true"))}))},a={getSocket:t=>uv.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{uv.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{zg.getCurrent(e).each((n=>{Zm.set(n,[jF(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&be(t,s)&&zg.getCurrent(n).each((t=>{qw.showSlot(t,s),Ib.immediateGrow(n),vn(n.element,"width"),qF(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{uv.getPart(t,e,"sidebar").each((e=>((e,t)=>{zg.getCurrent(e).each((o=>{zg.getCurrent(o).each((n=>{Ib.hasGrown(o)?qw.isShowing(n,t)?(Ib.shrink(o),qF(e.element,"presentation")):(qw.hideAllSlots(n),qw.showSlot(n,t),qF(e.element,"region")):(qw.hideAllSlots(n),qw.showSlot(n,t),Ib.grow(o),qF(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>uv.getPart(t,e,"sidebar").bind(XF).getOrNull(),getHeader:t=>uv.getPart(t,e,"header"),getToolbar:t=>uv.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{uv.getPart(t,e,"toolbar").each((e=>{const t=L(o,eR);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{uv.getPart(t,e,"multiple-toolbar").each((e=>{const t=L(o,(e=>L(e,eR)));Mv.setItems(e,t)}))},refreshToolbar:t=>{uv.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{uv.getPart(t,e,"toolbar").each((e=>{We(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{uv.getPart(t,e,"toolbar").each((e=>{We(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>uv.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>uv.getPart(t,e,"throbber"),focusToolbar:t=>{uv.getPart(t,e,"toolbar").orThunk((()=>uv.getPart(t,e,"multiple-toolbar"))).each((e=>{wm.focusIn(e)}))},setMenubar:(t,o)=>{uv.getPart(t,e,"menubar").each((e=>{WF.setMenus(e,o)}))},focusMenubar:t=>{uv.getPart(t,e,"menubar").each((e=>{WF.focus(e)}))},setViews:(t,o)=>{uv.getPart(t,e,"viewWrapper").each((e=>{hR.setViews(e,o)}))},toggleView:(t,o)=>uv.getPart(t,e,"viewWrapper").exists((e=>hR.toggleView(e,(()=>a.showMainView(t)),(()=>a.hideMainView(t)),o))),whichView:t=>uv.getPart(t,e,"viewWrapper").bind(hR.whichView).getOrNull(),hideMainView:t=>{n=a.isToolbarDrawerToggled(t),n&&a.toggleToolbarDrawer(t),uv.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),dn(t,"display","none"),Go(t,"aria-hidden","true")}))},showMainView:t=>{n&&a.toggleToolbarDrawer(t),uv.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),vn(t,"display"),Ko(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:a,behaviours:e.behaviours}},configFields:[Rr("dom"),Rr("behaviours")],partFields:[xR,fR,vR,bR,wR,SR,yR,CR,kR,OR],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const TR={file:{title:"File",items:"newdocument restoredraft | preview | importword exportpdf exportword | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code revisionhistory | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed inserttemplate codesample inserttable accordion math | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"aidialog aishortcuts | spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},ER=e=>e.split(" "),AR=(e,t)=>{const o={...TR,...t.menus},n=re(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?ER("file edit view insert format tools table help"):ER(!1===t.menubar?"":t.menubar),a=P(s,(e=>{const o=be(TR,e);return n?o||fe(t.menus,e).exists((e=>be(e,"items"))):o})),i=L(a,(n=>{const s=o[n];return((e,t,o)=>{const n=sC(o).split(/[ ,]/);return{text:e.title,getItems:()=>q(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||R(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:ER(s.items)},t,e)}));return P(i,(e=>e.getItems().length>0&&R(e.getItems(),(e=>r(e)||"separator"!==e.type))))},MR=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),DR=(e,t,o,n)=>(e.on("remove",(()=>n.unloadRawCss(t))),n.loadRawCss(t,o)),BR=e=>A.from(tinymce.Resource.get(e)).filter(r),IR=(e,t,o="")=>{const n=(e=>{const t=DC(e);return t?A.from(t):A.none()})(e).map((e=>((e,t)=>"ui/"+e+"/"+t)(e,`${t}.css`))),s=n.bind(BR);return Ue(n,s,((e,t)=>({_kind:"load-raw",key:e,css:t}))).getOrThunk((()=>{const n=e.editorManager.suffix;return{_kind:"load-stylesheet",url:o+`/${t}${n}.css`}}))},FR=(e,t)=>{const o=e.ui.styleSheetLoader,n=IR(e,"skin",t);switch(n._kind){case"load-raw":const{key:t,css:s}=n;return DR(e,t,s,o),Promise.resolve();case"load-stylesheet":const{url:r}=n;return MR(e,r,o);default:return Promise.resolve()}},RR=(e,t)=>{var o;if(o=ct(e.getElement()),!ko(o).isSome())return Promise.resolve();{const o=HS.DOM.styleSheetLoader,n=IR(e,"skin.shadowdom",t);switch(n._kind){case"load-raw":const{key:t,css:s}=n;return DR(e,t,s,o),Promise.resolve();case"load-stylesheet":const{url:r}=n;return MR(e,r,o);default:return Promise.resolve()}}},NR=(e,t)=>(async(e,t)=>{const o=FC(t);if(await((e,t,o)=>{const n=IR(e,t?"content.inline":"content",o);switch(n._kind){case"load-raw":const{key:s,css:r}=n;return t?DR(e,s,r,e.ui.styleSheetLoader):e.on("PostRender",(()=>{DR(e,s,r,e.dom.styleSheetLoader)})),Promise.resolve();case"load-stylesheet":const{url:a}=n;return o&&e.contentCSS.push(a),Promise.resolve();default:return Promise.resolve()}})(t,e,o),!BC(t)&&r(o))return Promise.all([FR(t,o),RR(t,o)]).then()})(e,t).then((e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}})(t),(e=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)),zR=C(NR,!1),LR=C(NR,!0),VR=e=>({isEnabled:()=>!tp.isDisabled(e),setEnabled:t=>tp.set(e,!t),setText:t=>Rc(e,NB,{text:t}),setIcon:t=>Rc(e,zB,{icon:t})}),HR=e=>({setActive:t=>{Yb.set(e,t)},isActive:()=>Yb.isOn(e),isEnabled:()=>!tp.isDisabled(e),setEnabled:t=>tp.set(e,!t),setText:t=>Rc(e,NB,{text:t}),setIcon:t=>Rc(e,zB,{icon:t})}),PR=(e,t)=>e.map((e=>({"aria-label":t.translate(e)}))).getOr({}),UR=Ne("focus-button"),WR=(e,t,o,n,s,r,a)=>{const i=t.map((e=>dv(RB(e,"tox-tbtn",s)))),l=e.map((e=>dv(FB(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:{...PR(o,s),...g(a)?{"data-mce-name":a}:{}}},components:TA([l.map((e=>e.asSpec())),i.map((e=>e.asSpec()))]),eventOrder:{[pa()]:["focusing","alloy.base.behaviour",AB],[qa()]:[AB,"toolbar-group-button-events"],[Xa()]:[AB,"toolbar-group-button-events","tooltipping"]},buttonBehaviours:rd([gA((()=>s.checkUiComponentContext(r).shouldDisable)),kA((()=>s.checkUiComponentContext(r))),ud(AB,[Jc(((e,t)=>DB(e))),Wc(NB,((e,t)=>{i.bind((t=>t.getOpt(e))).each((e=>{Zm.set(e,[Og(s.translate(t.event.text))])}))})),Wc(zB,((e,t)=>{l.bind((t=>t.getOpt(e))).each((e=>{Zm.set(e,[FB(t.event.icon,s.icons)])}))})),Wc(pa(),((e,t)=>{t.event.prevent(),Fc(e,UR)}))])].concat(n.getOr([])))}},$R=(e,t,o,n)=>{var s;const r=ye(b),a=WR(e.icon,e.text,e.tooltip,A.none(),o,e.context,n);return _v.sketch({dom:a.dom,components:a.components,eventOrder:MB,buttonBehaviours:{...rd([ud("toolbar-button-events",[(i={onAction:e.onAction,getApi:t.getApi},ed(((e,t)=>{pA(i,e)((t=>{Rc(e,EB,{buttonApi:t}),i.onAction(t)}))}))),hA(t,r),fA(t,r)]),...e.tooltip.map((t=>iv.config(o.tooltips.getConfig({tooltipText:o.translate(t)+e.shortcut.map((e=>` (${DA(e)})`)).getOr("")})))).toArray(),gA((()=>!e.enabled||o.checkUiComponentContext(e.context).shouldDisable)),kA((()=>o.checkUiComponentContext(e.context)))].concat(t.toolbarButtonBehaviours)),[AB]:null===(s=a.buttonBehaviours)||void 0===s?void 0:s[AB]}});var i},GR=(e,t,o,n)=>$R(e,{toolbarButtonBehaviours:o.length>0?[ud("toolbarButtonWith",o)]:[],getApi:VR,onSetup:e.onSetup},t,n),jR=(e,t,o,n)=>$R(e,{toolbarButtonBehaviours:[Zm.config({}),Yb.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[ud("toolbarToggleButtonWith",o)]:[]),getApi:HR,onSetup:e.onSetup},t,n),qR=(e,t,o)=>n=>Oe((e=>t.fetch(e))).map((s=>A.from(hD(Le(QM(Ne("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,hO.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:eD(t.columns,t.presets),menuBehaviours:cA("auto"!==t.columns?[]:[Jc(((e,o)=>{lA(e,4,TO(t.presets)).each((({numRows:t,numColumns:o})=>{wm.setGridSize(e,t,o)}))}))])}))))),XR=e=>{Xx.getContent(e).each((e=>{Xn(e.element,".tox-toolbar-slider__input,.tox-toolbar-textfield").fold((()=>wm.focusIn(e)),Fo)}))},YR=Ne("forward-slide"),KR=Ne("backward-slide"),JR=Ne("change-slide-event"),QR="tox-pop--resizing",ZR=(e,t,o)=>ot(o)?e.translate(t):e.translate([t,e.translate(o)]),eN=(e,t)=>{const o=(o,s,r,a)=>{const i=e.shared.providers.translate(o.title);if("separator"===o.type)return A.some({type:"separator",text:i});if("submenu"===o.type){const e=q(o.getStyleItems(),(e=>n(e,s,a)));return 0===s&&e.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:e.length>0,getSubmenuItems:()=>q(o.getStyleItems(),(e=>n(e,s,a)))})}return A.some({type:"togglemenuitem",text:i,icon:o.icon,active:o.isSelected(a),enabled:!r,onAction:t.onAction(o),...o.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},n=(e,n,s)=>{const r="formatter"===e.type&&t.isInvalid(e);return 0===n?r?[]:o(e,n,!1,s).toArray():o(e,n,r,s).toArray()},s=e=>{const o=t.getCurrentValue(),s=t.shouldHide?0:1;return q(e,(e=>n(e,s,o)))};return{validateItems:s,getFetch:(e,t)=>(o,n)=>{const r=t(),a=s(r);n(WB(a,hO.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},tN=(e,t)=>{const o=t.dataset,n="basic"===o.type?()=>L(o.data,(e=>eF(e,t.isSelectedFor,t.getPreviewFor))):o.getData;return{items:eN(e,t),getStyleItems:n}},oN=(e,t,o,n,s,r)=>{const{items:a,getStyleItems:i}=tN(t,o),l=ye(o.tooltip);return VB({context:"mode:design",text:o.icon.isSome()?A.none():o.text,icon:o.icon,ariaLabel:A.some(o.tooltip),tooltip:A.none(),role:A.none(),fetch:a.getFetch(t,i),onSetup:t=>{const r=o=>t.setTooltip(ZR(e,n(o.value),o.value));return e.on(s,r),vM(wM(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),tp.set(t.getComponent(),!e.selection.isEditable()||0===i().length)}))(t),(()=>e.off(s,r)))},getApi:e=>({getComponent:y(e),setTooltip:o=>{const n=t.shared.providers.translate(o);Go(e.element,"aria-label",n),l.set(o)}}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[iv.config({...t.shared.providers.tooltips.getConfig({tooltipText:t.shared.providers.translate(o.tooltip),onShow:e=>{if(o.tooltip!==l.get()){const o=t.shared.providers.translate(l.get());iv.setComponents(e,t.shared.providers.tooltips.getComponents({tooltipText:o}))}}})})]},"tox-tbtn",t.shared,r)};var nN;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(nN||(nN={}));const sN=(e,t,o)=>{const n=(s=((e,t)=>t===nN.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),L(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},rN=y("Alignment {0}"),aN="left",iN=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],lN=e=>{const t={type:"basic",data:iN};return{tooltip:ZR(e,rN(),aN),text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>$(iN,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=$(iN,(t=>e.formatter.match(t.format))).fold(y(aN),(e=>e.title.toLowerCase()));Rc(t,zB,{icon:`align-${o}`}),((e,t)=>{e.dispatch("AlignTextUpdate",t)})(e,{value:o})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},cN=(e,t)=>{const o=t(),n=L(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>$(o,(t=>t.format===e))))},dN=y("Block {0}"),uN="Paragraph",mN=e=>{const t=sN(e,"block_formats",nN.SemiColon);return{tooltip:ZR(e,dN(),uN),text:A.some(uN),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:SM(e),updateText:o=>{const n=cN(e,(()=>t.data)).fold(y(uN),(e=>e.title));Rc(o,NB,{text:n}),((e,t)=>{e.dispatch("BlocksTextUpdate",t)})(e,{value:n})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},gN=y("Font {0}"),pN="System Font",hN=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],fN=e=>{const t=e.split(/\s*,\s*/);return L(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},bN=(e,t)=>t.length>0&&X(t,(t=>e.indexOf(t.toLowerCase())>-1)),vN=e=>{const t=()=>{const t=e=>e?fN(e)[0]:"",n=e.queryCommandValue("FontName"),s=o.data,r=n?n.toLowerCase():"",a=MC(e),i=$(s,(e=>{const o=e.format;return o.toLowerCase()===r||t(o).toLowerCase()===t(r).toLowerCase()})).orThunk((()=>$e(((e,t)=>{if(0===e.indexOf("-apple-system")||t.length>0){const o=fN(e.toLowerCase());return bN(o,hN)||bN(o,t)}return!1})(r,a),{title:pN,format:r})));return{matchOpt:i,font:n}},o=sN(e,"font_family_formats",nN.SemiColon);return{tooltip:ZR(e,gN(),pN),text:A.some(pN),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=t();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:o=>{const{matchOpt:n,font:s}=t(),r=n.fold(y(s),(e=>e.title));Rc(o,NB,{text:r}),((e,t)=>{e.dispatch("FontFamilyTextUpdate",t)})(e,{value:r})},dataset:o,shouldHide:!1,isInvalid:T}};var xN=tinymce.util.Tools.resolve("tinymce.util.VK");const yN=y("Font size {0}"),wN="12pt",SN={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},CN={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},kN=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":fe(CN,e).getOr(e),ON=e=>fe(SN,e).getOr(""),_N=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=kN(s,e),r=ON(n);t=$(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=y(A.none),n=sN(e,"font_size_formats",nN.Space);return{tooltip:ZR(e,yN(),wN),text:A.some(wN),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:o=>{const{matchOpt:n,size:s}=t(),r=n.fold(y(s),(e=>e.title));Rc(o,NB,{text:r}),((e,t)=>{e.dispatch("FontSizeTextUpdate",t)})(e,{value:r})},dataset:n,shouldHide:!1,isInvalid:T}},TN=e=>ot(e)?"Formats":"Format {0}",EN=(e,t)=>{const o="Formats";return{tooltip:ZR(e,TN(""),""),text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:SM(e),updateText:t=>{const n=e=>YI(e)?q(e.items,n):KI(e)?[{title:e.title,format:e.format}]:[],s=q(ZI(e),n),r=cN(e,y(s)).fold(y({title:o,tooltipLabel:""}),(e=>({title:e.title,tooltipLabel:e.title})));Rc(t,NB,{text:r.title}),((e,t)=>{e.dispatch("StylesTextUpdate",t)})(e,{value:r.tooltipLabel})},shouldHide:oC(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},AN=[{name:"history",items:["undo","redo"]},{name:"ai",items:["aidialog","aishortcuts"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],MN=(e,t)=>(o,n,s,r)=>{const a=e(o).mapError((e=>Ar(e))).getOrDie();return t(a,n,s,r)},DN={button:MN(xE,((e,t,o,n)=>((e,t,o)=>GR(e,t,[],o))(e,t.shared.providers,n))),togglebutton:MN(SE,((e,t,o,n)=>((e,t,o)=>jR(e,t,[],o))(e,t.shared.providers,n))),menubutton:MN(HT,((e,t,o,n)=>tI(e,"tox-tbtn",t,A.none(),!1,n))),splitbutton:MN((e=>_r("SplitButton",oA,e)),((e,t,o,n)=>((e,t,o)=>{const n=ye(e.tooltip.getOr("")),s=e=>({isEnabled:()=>!tp.isDisabled(e),setEnabled:t=>tp.set(e,!t),setIconFill:(t,o)=>{Xn(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{Go(e,"fill",o)}))},setActive:t=>{Go(e.element,"aria-pressed",t),Xn(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>Yb.set(e,t)))}))},isActive:()=>Xn(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(Yb.isOn))),setText:t=>Xn(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Rc(e,NB,{text:t}))))),setIcon:t=>Xn(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Rc(e,zB,{icon:t}))))),setTooltip:o=>{const s=t.providers.translate(o);Go(e.element,"aria-label",s),n.set(o)}}),r=ye(b),a={getApi:s,onSetup:e.onSetup};return Qw.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...PR(e.tooltip,t.providers),...g(o)?{"data-mce-name":o}:{}}},onExecute:t=>{const o=s(t);o.isEnabled()&&e.onAction(o)},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:rd([ud("split-dropdown-events",[Jc(((e,t)=>DB(e))),Wc(UR,fd.focus),hA(a,r),fA(a,r)]),mA((()=>t.providers.isDisabled()||t.providers.checkUiComponentContext(e.context).shouldDisable)),kA((()=>t.providers.checkUiComponentContext(e.context))),lv.config({}),...e.tooltip.map((e=>iv.config({...t.providers.tooltips.getConfig({tooltipText:t.providers.translate(e),onShow:o=>{if(n.get()!==e){const e=t.providers.translate(n.get());iv.setComponents(o,t.providers.tooltips.getComponents({tooltipText:e}))}}})}))).toArray()]),eventOrder:{[qa()]:["alloy.base.behaviour","split-dropdown-events","tooltipping"],[Xa()]:["split-dropdown-events","tooltipping"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:qR(s,e,t.providers),parts:{menu:IO(0,e.columns,e.presets)},components:[Qw.parts.button(WR(e.icon,e.text,A.none(),A.some([Yb.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1}),gA(T),kA(y({contextType:"any",shouldDisable:!1}))]),t.providers,e.context)),Qw.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:iO("chevron-down",t.providers.icons)},buttonBehaviours:rd([mA(T),kA(y({contextType:"any",shouldDisable:!1}))])}),Qw.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared,n))),grouptoolbarbutton:MN((e=>_r("GroupToolbarButton",tA,e)),((e,t,o,n)=>{const s=o.ui.registry.getAll().buttons,r={[Tc]:t.shared.header.isPositionedAtTop()?_c.TopToBottom:_c.BottomToTop};if(rC(o)===NS.floating)return((e,t,o,n,s)=>{const r=t.shared,a=ye(b),i={toolbarButtonBehaviours:[],getApi:VR,onSetup:e.onSetup},l=[ud("toolbar-group-button-events",[hA(i,a),fA(i,a)]),...e.tooltip.map((e=>iv.config(t.shared.providers.tooltips.getConfig({tooltipText:t.shared.providers.translate(e)})))).toArray()];return Vx.sketch({lazySink:r.getSink,fetch:()=>Oe((t=>{t(L(o(e.items),eR))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:WR(e.icon,e.text,e.tooltip,A.some(l),r.providers,e.context,s),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>IN(o,{buttons:s,toolbar:e,allowToolbarGroups:!1},t,A.none())),r,n);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},BN={styles:(e,t)=>{const o={type:"advanced",...t.styles};return oN(e,t,EN(e,o),TN,"StylesTextUpdate","styles")},fontsize:(e,t)=>oN(e,t,_N(e),yN,"FontSizeTextUpdate","fontsize"),fontsizeinput:(e,t)=>((e,t,o,n)=>{let s=A.none();const r=wM(e,"NodeChange SwitchMode DisabledStateChange",(t=>{const n=t.getComponent();s=A.some(n),o.updateInputValue(n),tp.set(n,!e.selection.isEditable()||qS(e))})),a=e=>({getComponent:y(e)}),i=ye(b),l=Ne("custom-number-input-events"),c=(e,t,n)=>{const r=s.map((e=>Yh.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=s.map((e=>e.element.dom.selectionStart-i)),c=s.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,n),s.each((e=>{Yh.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},d=(e,t)=>c(((e,t)=>e-t),e,t),u=(e,t)=>c(((e,t)=>e+t),e,t),m=e=>po(e.element).fold(A.none,(e=>(Fo(e),A.some(!0)))),p=e=>No(e.element)?(vo(e.element).each((e=>Fo(e))),A.some(!0)):A.none(),h=(o,n,s,r)=>{const i=ye(b),l=t.shared.providers.translate(s),c=Ne("altExecuting"),d=wM(e,"NodeChange SwitchMode DisabledStateChange",(t=>{tp.set(t.getComponent(),!e.selection.isEditable()||qS(e))})),u=e=>{tp.isDisabled(e)||o(!0)};return _v.sketch({dom:{tag:"button",attributes:{"aria-label":l,"data-mce-name":n},classes:r.concat(n)},components:[IB(n,t.shared.providers.icons)],buttonBehaviours:rd([tp.config({}),iv.config(t.shared.providers.tooltips.getConfig({tooltipText:l})),ud(c,[hA({onSetup:d,getApi:a},i),fA({getApi:a},i),Wc(wa(),((e,t)=>{t.event.raw.keyCode!==xN.SPACEBAR&&t.event.raw.keyCode!==xN.ENTER||tp.isDisabled(e)||o(!1)})),Wc(Oa(),u),Wc(ma(),u)])]),eventOrder:{[wa()]:[c,"keying"],[Oa()]:[c,"alloy.base.behaviour"],[ma()]:[c,"alloy.base.behaviour"],[qa()]:["alloy.base.behaviour",c,"tooltipping"],[Xa()]:[c,"tooltipping"]}})},f=dv(h((e=>d(!1,e)),"minus","Decrease font size",[])),v=dv(h((e=>u(!1,e)),"plus","Increase font size",[])),x=dv({dom:{tag:"div",classes:["tox-input-wrapper"]},components:[Zx.sketch({inputBehaviours:rd([tp.config({}),ud(l,[hA({onSetup:r,getApi:a},i),fA({getApi:a},i)]),ud("input-update-display-text",[Wc(NB,((e,t)=>{Yh.setValue(e,t.event.text)})),Wc(ya(),(e=>{o.onAction(Yh.getValue(e))})),Wc(ka(),(e=>{o.onAction(Yh.getValue(e))}))]),wm.config({mode:"special",onEnter:e=>(c(w,!0,!0),A.some(!0)),onEscape:m,onUp:e=>(u(!0,!1),A.some(!0)),onDown:e=>(d(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:rd([fd.config({}),wm.config({mode:"special",onEnter:p,onSpace:p,onEscape:m}),ud("input-wrapper-events",[Wc(va(),(e=>{V([f,v],(t=>{const o=ct(t.get(e).element.dom);No(o)&&Ro(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"],attributes:{...g(n)?{"data-mce-name":n}:{}}},components:[f.asSpec(),x.asSpec(),v.asSpec()],behaviours:rd([fd.config({}),wm.config({mode:"flow",focusInside:Ei.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>No(e.element)?A.none():(Fo(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Rc(e,NB,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{Es(o,["unsupportedLength","empty"]);const s=t(),r=Es(o,["unsupportedLength","empty"]).or(Es(s,["unsupportedLength","empty"])),a=r.map((e=>e.value)).getOr(16),i=fC(e),l=r.map((e=>e.unit)).filter((e=>""!==e)).getOr(i),c=n(a,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(l).step),d=`${(e=>e>=0)(c)?c:a}${l}`;return d!==s&&((e,t)=>{e.dispatch("FontSizeInputTextUpdate",t)})(e,{value:d}),d}}})(e),"fontsizeinput"),fontfamily:(e,t)=>oN(e,t,vN(e),gN,"FontFamilyTextUpdate","fontfamily"),blocks:(e,t)=>oN(e,t,mN(e),dN,"BlocksTextUpdate","blocks"),align:(e,t)=>oN(e,t,lN(e),rN,"AlignTextUpdate","align"),navigateback:(e,t)=>{const o=Tr(xE({type:"button",icon:"chevron-left",tooltip:"Back",onAction:b}));return GR(o,t.shared.providers,[Wc(EB,(e=>{Fc(e,KR)}))])}},IN=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=L(AN,(t=>{const o=P(t.items,(t=>be(e,t)||be(BN,t)));return{name:t.name,items:o}}));return P(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return L(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>(be(e,"name")||be(e,"label"))&&be(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=L(s,(s=>{const r=q(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>fe(t,o.toLowerCase()).orThunk((()=>r.bind((e=>se(e,(e=>fe(t,e+o.toLowerCase()))))))).fold((()=>fe(BN,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o,n)=>fe(DN,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(s=>A.some(s(e,t,o,n)))))(t,s,e,o.toLowerCase()):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:A.from(e.translate(s.name)),label:$e(void 0!==s.label,e.translate(s.label)),items:r}}));return P(a,(e=>e.items.length>0))},FN=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return IN(e,s,n,A.none())}));_R.setToolbars(s,t)}else _R.setToolbar(s,IN(e,o,n,A.none()))},RN=Jt(),NN=RN.os.isiOS()&&RN.os.version.major<=12;var zN=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=ye(0),l=r.outerContainer;zR(e);const d=ct(s.targetNode),u=Co(So(d));Lm(d,r.mothership),((e,t,o)=>{GC(e)&&Lm(o.mainUi.mothership.element,o.popupUi.mothership),zm(t,o.dialogUi.mothership)})(e,u,t),e.on("PostRender",(()=>{_R.setSidebar(l,o.sidebar,TC(e))})),e.on("SkinLoaded",(()=>{FN(e,t,o,n),i.set(e.getWin().innerWidth),_R.setMenubar(l,AR(e,o)),_R.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=ye(Dn(s.innerWidth,s.innerHeight)),i=ye(Dn(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set(Dn(s.innerWidth,s.innerHeight)),pM(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set(Dn(t.offsetWidth,t.offsetHeight)),pM(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=Do(ct(e.getBody()),"load",c);e.on("hide",(()=>{V(o,(e=>{dn(e.element,"display","none")}))})),e.on("show",(()=>{V(o,(e=>{vn(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=_R.getSocket(l).getOrDie("Could not find expected socket element");if(NN){un(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=(e=>{let t=null;return{cancel:()=>{c(t)||(clearTimeout(t),t=null)},throttle:(...o)=>{c(t)&&(t=setTimeout((()=>{t=null,e.apply(null,o)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=Mo(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}CA(e,t),e.addCommand("ToggleSidebar",((t,o)=>{_R.toggleSidebar(l,o),(e=>{e.dispatch("ToggleSidebar")})(e)})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=_R.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(_R.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([gv()],{target:t}),V(a,(e=>{e.broadcastOn([gv()],{target:t})})),c(_R.whichView(l))&&(e.focus(),e.nodeChanged(),_R.refreshToolbar(l)),(e=>{e.dispatch("ToggleView")})(e)}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=_R.whichView(l))&&void 0!==e?e:""}));const g=rC(e);g!==NS.sliding&&g!==NS.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(_R.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{SA(t,e?"setEnabled":"setDisabled")},isEnabled:()=>!tp.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const LN=e=>h(e)?e+"px":e,VN=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},HN=e=>{const t=YS(e),o=KS(e),n=QS(e);return(s=t,/^[0-9\.]+(|px)$/i.test(""+s)?A.some(parseInt(""+s,10)):A.none()).map((e=>VN(e,o,n)));var s},{ToolbarLocation:PN,ToolbarMode:UN}=qC,WN=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=HS.DOM,l=PC(e),c=$C(e),d=QS(e).or(HN(e)),u=n.shared.header,m=u.isPositionedAtTop,g=rC(e),p=g===UN.sliding||g===UN.floating,h=ye(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(y(0),(e=>e.components().length>1?Sn(e.components()[1].element):0)):0,v=()=>{V(a,(e=>{e.broadcastOn([pv()],{})}))},x=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>Ms().width-Fn(t).left-10));dn(e.element,"max-width",o+"px")}));const n=Rn(),a=!(l||l||!(In(r.outerContainer.element).left+En(r.outerContainer.element)>=window.innerWidth-40||hn(r.outerContainer.element,"width").isSome())||(dn(r.outerContainer.element,"position","absolute"),dn(r.outerContainer.element,"left","0px"),vn(r.outerContainer.element,"width"),0));if(p&&_R.refreshToolbar(r.outerContainer),!l){const o=Rn(),i=$e(n.left!==o.left,n);((o,n)=>{s.on((s=>{const a=_R.getToolbar(r.outerContainer),i=b(a),l=Rs(t),c=((e,t)=>GC(e)?Hs(t):A.none())(e,r.outerContainer.element),d=c.fold((()=>l.x),(e=>{const t=Rs(e);return yt(e,rn())?l.x:l.x-t.x})),u=$e(o,Math.ceil(r.outerContainer.element.dom.getBoundingClientRect().width)).filter((e=>e>150)).map((e=>{const t=n.getOr(Rn()),o=window.innerWidth-(d-t.left),s=Math.max(Math.min(e,o),150);return o<e&&dn(r.outerContainer.element,"width",s+"px"),{width:s+"px"}})).getOr({width:"max-content"}),g={position:"absolute",left:Math.round(d)+"px",top:c.fold((()=>m()?Math.max(l.y-Sn(s.element)+i,0):l.bottom),(e=>{var t;const o=Rs(e),n=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=yt(e,rn())?Math.max(l.y-Sn(s.element)+i,0):l.y-o.y+n-Sn(s.element)+i;return m()?r:l.bottom}))+"px"};un(r.outerContainer.element,{...g,...u})}))})(a,i),i.each((e=>{Nn(e.left,o.top)}))}c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch(iC(e)){case PN.auto:const e=_R.getToolbar(r.outerContainer),n=b(e),s=Sn(o.element)-n,a=Rs(t);if(a.y>s)return"top";{const e=uo(t),o=Math.max(e.dom.scrollHeight,Sn(e));return a.bottom<o-s||Ls().bottom<a.bottom-s?"bottom":"top"}case PN.bottom:return"bottom";case PN.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{Ep.setModes(e,[i]),u.setDockingMode(i);const t=m()?_c.TopToBottom:_c.BottomToTop;Go(e.element,Tc,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),dn(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),V(a,(e=>{vn(e.element,"display")})),w(),GC(e)?x((e=>Ep.isDocked(e)?Ep.reset(e):Ep.refresh(e))):x(Ep.refresh)},hide:()=>{h.set(!1),dn(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),V(a,(e=>{dn(e.element,"display","none")}))},update:x,updateMode:()=>{w()&&x(Ep.reset)},repositionPopups:v}},$N=(e,t)=>{const o=Rs(e);return{pos:t?o.y:o.bottom,bounds:o}};var GN=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r}=t,a=Xe(),i=ct(s.targetNode),l=WN(e,i,t,n,a),c=dC(e);LR(e);const d=()=>{if(a.isSet())return void l.show();a.set(_R.getHeader(r.outerContainer).getOrDie());const s=UC(e);GC(e)?(Lm(i,r.mothership),Lm(i,t.popupUi.mothership)):zm(s,r.mothership),zm(s,t.dialogUi.mothership);const d=()=>{FN(e,t,o,n),_R.setMenubar(r.outerContainer,AR(e,o)),l.show(),((e,t,o,n)=>{const s=ye($N(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=$N(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&pM(e,n),o.isVisible()&&(i!==r?o.update(Ep.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(Ep.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=nt((()=>o.update(Ep.refresh)),33);e.on("ScrollWindow",(()=>{const e=Rn().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),GC(e)&&e.on("ElementScroll",(e=>{o.update(Ep.refresh)}));const l=qe();l.set(Do(ct(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};c?e.once("SkinLoaded",d):d()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),CA(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{SA(t,e?"setEnabled":"setDisabled")},isEnabled:()=>!tp.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}});const jN="contexttoolbar-hide",qN=(e,t,o)=>({setInputEnabled:t=>{!t&&o&&Fo(o),tp.set(e,!t)},isInputEnabled:()=>!tp.isDisabled(e),hide:()=>{Fc(e,Ha())},back:()=>{Fc(e,KR)},getValue:()=>t.get().getOrThunk((()=>Yh.getValue(e))),setValue:o=>{e.getSystem().isConnected()?Yh.setValue(e,o):t.set(o)}}),XN=(e,t,o)=>Wc(EB,((n,s)=>{const r=e.get(n),a=qN(r,o,n.element);t.onAction(a,s.event.buttonApi)})),YN=(e,t,o,n)=>{const s=L(t,(t=>dv(((e,t,o,n)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o,n)=>{const{primary:s,...r}=t.original,a=Tr(SE({...r,type:"togglebutton",onAction:b}));return jR(a,o,[XN(e,t,n)])})(e,t,o,n):((e,t,o,n)=>{const{primary:s,...r}=t.original,a=Tr(xE({...r,type:"button",onAction:b}));return GR(a,o,[XN(e,t,n)])})(e,t,o,n))(e,t,o,n))));return{asSpecs:()=>L(s,(e=>e.asSpec())),findPrimary:e=>se(t,((t,o)=>t.primary?A.from(s[o]).bind((t=>t.getOpt(e))).filter(k(tp.isDisabled)):A.none()))}},KN=(e,t,o,n)=>{const{width:s,height:r}=e.initValue();let a=JB;const i=Ne("ratio-event"),l=e=>qN(e,n),c=e=>dO(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),d=()=>!1,u=e.label.getOr("Constrain proportions"),m=t.translate(u),g=jx.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-lock-context-form-size-input","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":m,"data-mce-name":u}},components:[c("lock"),c("unlock")],buttonBehaviours:rd([tp.config({disabled:d}),Pb.config({}),iv.config(t.tooltips.getConfig({tooltipText:m}))])}),p=e=>({dom:{tag:"div",classes:["tox-context-form__group"]},components:e}),h=e=>Gn(e.element,"div.tox-focusable-wrapper").fold(A.none,(e=>(Fo(e),A.some(!0)))),f=e=>Ux.parts.field({factory:Zx,inputClasses:["tox-textfield","tox-toolbar-textfield","tox-textfield-size"],data:e?s:r,inputBehaviours:rd([tp.config({disabled:d}),Pb.config({}),ud("size-input-toolbar-events",[Wc(xa(),((t,o)=>{Rc(t,i,{isField1:e})}))]),wm.config({mode:"special",onEnter:o,onEscape:h})]),selectOnFocus:!1}),v=e=>({dom:{tag:"label",classes:["tox-label"]},components:[Og(t.translate(e))]}),x=e=>({dom:{tag:"div",classes:["tox-focusable-wrapper","tox-toolbar-nav-item"]},components:[e],behaviours:rd([Pb.config({}),fd.config({}),wm.config({mode:"special",onEnter:e=>Xn(e.element,"input").fold(A.none,(e=>(Fo(e),A.some(!0))))})])}),y=x(jx.parts.field1(p([Ux.parts.label(v("Width:")),f(!0)]))),w=x(jx.parts.field2(p([Ux.parts.label(v("Height:")),f(!1)]))),S=ye(b),C=[hA({onBeforeSetup:e=>Xn(e.element,"input").each(Fo),onSetup:e.onSetup,getApi:l},S),bA({getApi:l},S,n)];return jx.sketch({dom:{tag:"div",classes:["tox-context-form__group"]},components:[y,p([g]),w],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,o)=>{YB(Yh.getValue(e)).each((e=>{a(e).each((e=>{Yh.setValue(t,XB(e))}))}))},onInput:e=>Fc(e,kD),coupledFieldBehaviours:rd([fd.config({}),wm.config({mode:"flow",focusInside:Ei.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-focusable-wrapper"}),tp.config({disabled:d,onDisabled:e=>{jx.getField1(e).bind(Ux.getField).each(tp.disable),jx.getField2(e).bind(Ux.getField).each(tp.disable),jx.getLock(e).each(tp.disable)},onEnabled:e=>{jx.getField1(e).bind(Ux.getField).each(tp.enable),jx.getField2(e).bind(Ux.getField).each(tp.enable),jx.getLock(e).each(tp.enable)}}),kA((()=>t.checkUiComponentContext("mode:design"))),ud("size-input-toolbar-events2",[Wc(i,((e,t)=>{const o=t.event.isField1,n=o?jx.getField1(e):jx.getField2(e),s=o?jx.getField2(e):jx.getField1(e),r=n.map(Yh.getValue).getOr(""),i=s.map(Yh.getValue).getOr("");a=QB(r,i)})),Wc(kD,(t=>e.onInput(l(t)))),...C])])})},JN=(e,t,o)=>Ux.sketch({dom:{tag:"div",classes:["tox-context-form__group"]},components:[...e.toArray(),t],fieldBehaviours:rd([tp.config({disabled:()=>o.checkUiComponentContext("mode:design").shouldDisable,onDisabled:e=>{(e=>{Lo(e.element).each((e=>{Gn(e,'[tabindex="-1"]').each((e=>{Fo(e)}))}))})(e),Ux.getField(e).each(tp.disable)},onEnabled:e=>{Ux.getField(e).each(tp.enable)}})])}),QN=(e,t,o,n)=>{const s=ye(b),r=e=>qN(e,n),a=e.label.map((e=>Ux.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Og(t.translate(e))]}))),i=Ux.parts.field({factory:Zx,type:"range",inputClasses:["tox-toolbar-slider__input","tox-toolbar-nav-item"],inputAttributes:{min:String(e.min()),max:String(e.max())},data:e.initValue().toString(),fromInputValue:t=>(e=>{const t=parseFloat(e);return isNaN(t)?A.none():A.some(t)})(t).getOr(e.min()),toInputValue:e=>String(e),inputBehaviours:rd([tp.config({disabled:()=>t.checkUiComponentContext("mode:design").shouldDisable}),kA((()=>t.checkUiComponentContext("mode:design"))),wm.config({mode:"special",onEnter:o,onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())}),ud("slider-events",[hA({onSetup:e.onSetup,getApi:r,onBeforeSetup:wm.focusIn},s),bA({getApi:r},s,n),Wc(Ca(),(t=>{e.onInput(r(t))}))])])});return JN(a,i,t)},ZN=(e,t,o,n)=>{const s=ye(b),r=e=>qN(e,n),a=e.label.map((e=>Ux.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Og(t.translate(e))]}))),i={...e.placeholder.map((e=>({placeholder:t.translate(e)}))).getOr({})},l=Ux.parts.field({factory:Zx,inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-item"],inputAttributes:i,data:e.initValue(),selectOnFocus:!0,inputBehaviours:rd([tp.config({disabled:()=>t.checkUiComponentContext("mode:design").shouldDisable}),kA((()=>t.checkUiComponentContext("mode:design"))),wm.config({mode:"special",onEnter:o,onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())}),ud("input-events",[hA({onSetup:e.onSetup,getApi:e=>Gn(e.element,".tox-toolbar").bind((e=>Xn(e,"button:enabled"))).fold((()=>qN(e,n)),(t=>qN(e,n,t))),onBeforeSetup:wm.focusIn},s),bA({getApi:r},s,n),Wc(Ca(),(t=>{e.onInput(r(t))}))])])});return JN(a,l,t)},ez=(e,t,o)=>{const n=Xe(),s=dv(e(o,(e=>a.findPrimary(e).orThunk((()=>i.findPrimary(e))).map((e=>(Nc(e),!0)))),n)),r=H(t.commands,(e=>"start"===e.align)),a=YN(s,r.pass,o,n),i=YN(s,r.fail,o,n);return P([{title:A.none(),label:A.none(),items:a.asSpecs()},{title:A.none(),label:A.none(),items:[s.asSpec()]},{title:A.none(),label:A.none(),items:i.asSpecs()}],(e=>e.items.length>0))},tz=(e,t)=>{switch(e.type){case"contextform":return ez(C(ZN,e),e,t);case"contextsliderform":return ez(C(QN,e),e,t);case"contextsizeinputform":return ez(C(KN,e),e,t)}},oz=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,nz=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=yo(ct(e.startContainer),e.startOffset).element;return(so(o)?go(o):A.some(o)).filter(no).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Rn();return Fs(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Ns(ct(e.getBody()));return Fs(o.x+t.left,o.y+t.top,t.width,t.height)}},sz=(e,t,o,n=0)=>{const s=Ms(window),r=Rs(ct(e.getContentAreaContainer())),a=IC(e)||NC(e)||LC(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Fs(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=ct(e.getContainer()),i=Xn(a,".tox-editor-header").getOr(a),l=Rs(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Rs(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Fs(i,c,l,d-c)}},rz={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},az={maxHeightFunction:kc(),maxWidthFunction:Oc()},iz=e=>"node"===e,lz=(e,t,o,n,s)=>{const r=nz(e),a=n.lastElement().exists((e=>yt(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=yo(ct(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&yt(n.element,t)})(e,o)?a?xc:pc:a?((e,o)=>{const s=hn(e,"position");dn(e,"position",o);const a=oz(r,Rs(t),-20)&&!n.isReposition()?wc:xc;return s.each((t=>dn(e,"position",t))),a})(t,n.getMode()):("fixed"===n.getMode()?s.y+Rn().top:s.y)+(Sn(t)+12)<=r.y?pc:hc},cz=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...lz(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>iz(n)?[s(e)]:[];return t?{onLtr:e=>[jl,Pl,Ul,Wl,$l,Gl].concat(r(e)),onRtl:e=>[jl,Ul,Pl,$l,Wl,Gl].concat(r(e))}:{onLtr:e=>[Gl,jl,Wl,Pl,$l,Ul].concat(r(e)),onRtl:e=>[Gl,jl,$l,Ul,Wl,Pl].concat(r(e))}},dz=(e,t)=>{const o=P(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=H(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},uz=(e,t)=>{const o={},n=[],s=[],r={},a={},i=re(e);return V(i,(i=>{const l=e[i];"contextform"===l.type||"contextsliderform"===l.type||"contextsizeinputform"===l.type?((e,i)=>{const l=Tr(_r("ContextForm",BE,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,o)=>{var i;(i=o,_r("ContextToolbar",FE,i)).each((i=>{i.launch.isSome()&&(r["toolbar:"+e]={...o.launch,type:"button",onAction:()=>{t(i)}}),"editor"===o.scope?s.push(i):n.push(i),a[e]=i}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},mz="tox-pop--transition",gz=(e,t,o,n)=>{const s=n.backstage,a=s.shared,i=Jt().deviceType.isTouch,l=Xe(),c=Xe(),d=Xe(),u=(e=>{const t=ye([]),o=Xx.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),Xx.getContent(e).each((e=>{vn(e.element,"visibility")})),ss(e.element,QR),vn(e.element,"width")},onHide:()=>{t.set([]),e.onHide()},inlineBehaviours:rd([ud("context-toolbar-events",[Kc(Ta(),((e,t)=>{"width"===t.event.raw.propertyName&&(ss(e.element,QR),vn(e.element,"width"))})),Wc(JR,((e,t)=>{const o=e.element;vn(o,"width");const n=Tn(o),s=Lo(e.element).isSome();vn(o,"left"),vn(o,"right"),vn(o,"max-width"),Xx.setContent(e,t.event.contents),os(o,QR);const r=Tn(o);dn(o,"transition","none"),Xx.reposition(e),vn(o,"transition"),dn(o,"width",n+"px"),t.event.focus.fold((()=>{s&&XR(e)}),(t=>{zo(So(e.element)).fold((()=>Fo(t)),(e=>{yt(e,t)||Fo(t)}))})),setTimeout((()=>{dn(e.element,"width",r+"px")}),0)})),Wc(YR,((e,o)=>{Xx.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:zo(So(e.element))}]))})),Rc(e,JR,{contents:o.event.forwardContents,focus:A.none()})})),Wc(KR,((o,n)=>{e.onBack(),oe(t.get()).each((e=>{t.set(t.get().slice(0,t.get().length-1)),Rc(o,JR,{contents:Mg(e.bar),focus:e.focus})}))}))]),wm.config({mode:"special",onEscape:o=>oe(t.get()).fold((()=>e.onEscape()),(e=>(Fc(o,KR),A.some(!0))))})]),lazySink:()=>Ae.value(e.sink)});return{sketch:o,inSubtoolbar:()=>t.get().length>0}})({sink:o,onEscape:()=>(e.focus(),bM(e),A.some(!0)),onHide:()=>{bM(e)},onBack:()=>{(e=>{e.dispatch("ContextFormSlideBack")})(e)}}),m=Ag(u.sketch),g=()=>{const t=d.get().getOr("node"),o=iz(t)?1:0;return sz(e,a,t,o)},p=()=>!(e.removed||i()&&s.isContextMenuOpen()),h=()=>{if(p()){const t=g(),o=He(d.get(),"node")?((e,t)=>t.filter((e=>sn(e)&&oo(e))).map(Ns).getOrThunk((()=>nz(e))))(e,l.get()):nz(e);return t.height<=0||!oz(o,t,.01)}return!0},f=()=>{l.clear(),c.clear(),d.clear(),Xx.hide(m)},v=()=>{if(Xx.isOpen(m)){const e=m.element;vn(e,"display"),h()?dn(e,"display","none"):(c.set(0),Xx.reposition(m))}},x=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:rd([wm.config({mode:"acyclic"}),ud("pop-dialog-wrap-events",[Jc((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>wm.focusIn(t)))})),Qc((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),y=e=>{const t=S([e]);Rc(m,YR,{forwardContents:x(t)})},w=st((()=>uz(t,y))),S=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...w().formNavigators},i=rC(e)===NS.scrolling?NS.scrolling:NS.default,l=j(L(t,(t=>{return"contexttoolbar"===t.type?((t,o)=>IN(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:","toolbar:"])))(s,(o=t,{...o,launch:o.launch.getOrUndefined(),items:r(o.items)?o.items:L(o.items,RE)})):((e,t)=>tz(e,t))(t,a.providers);var o})));return rR({type:i,uid:Ne("context-toolbar"),initGroups:l,onEscape:A.none,cyclicKeying:!0,providers:a.providers})},C=(t,n)=>{if(O.cancel(),!p())return;const s=S(t),r=t[0].position,u=((t,n)=>{const s="node"===t?a.anchors.node(n):a.anchors.cursor(),r=((e,t,o,n)=>"line"===t?{bubble:wl(12,0,rz),layouts:{onLtr:()=>[ql],onRtl:()=>[Xl]},overrides:az}:{bubble:wl(0,12,rz,1/12),layouts:cz(e,o,n,t),overrides:az})(e,t,i(),{lastElement:l.get,isReposition:()=>He(c.get(),0),getMode:()=>Zf.getMode(o)});return Le(s,r)})(r,n);d.set(r),c.set(1);const f=m.element;vn(f,"display"),(e=>He(Ue(e,l.get(),yt),!0))(n)||(ss(f,mz),Zf.reset(o,m)),Xx.showWithinBounds(m,x(s),{anchor:u,transition:{classes:[mz],mode:"placement"}},(()=>A.some(g()))),n.fold(l.clear,l.set),h()&&dn(f,"display","none")};let k=!1;const O=nt((()=>{!e.hasFocus()||e.removed||k||(rs(m.element,mz)?O.throttle():((e,t)=>{const o=ct(t.getBody()),n=e=>yt(e,o),s=ct(t.selection.getNode());return(e=>!n(e)&&!wt(o,e))(s)?A.none():((e,t,o)=>{const n=dz(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=dz(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>R(e,(e=>e.position===t)),o=t=>P(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=L(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():fs(t,(e=>{if(no(e)){const{contextToolbars:t,contextForms:n}=dz(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>$(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>P(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(w(),e).fold(f,(e=>{C(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",f),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",v),e.on("click focus SetContent",O.throttle),e.on("keyup",(e=>{var t;((t=e.keyCode)!==xN.ENTER&&t!==xN.SPACEBAR||!u.inSubtoolbar())&&O.throttle()})),e.on(jN,f),e.on("contexttoolbar-show",(t=>{const o=w();fe(o.lookupTable,t.toolbarKey).each((o=>{C([o],$e(t.target!==e,t.target)),XR(m)}))})),e.on("focusout",(t=>{VS.setEditorTimeout(e,(()=>{Lo(o.element).isNone()&&Lo(m.element).isNone()&&!e.hasFocus()&&f()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&f()})),e.on("DisabledStateChange",(e=>{e.state&&f()})),e.on("ExecCommand",(({command:e})=>{"toggleview"===e.toLowerCase()&&f()})),e.on("AfterProgressState",(t=>{t.state?f():e.hasFocus()&&O.throttle()})),e.on("dragstart",(()=>{k=!0})),e.on("dragend drop",(()=>{k=!1})),e.on("NodeChange",(e=>{u.inSubtoolbar()?(dn(m.element,"transition","none"),v(),vn(m.element,"transition")):Lo(m.element).fold(O.throttle,b)}))}))},pz=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=Xe();return L(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(He(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},hz=e=>{pz(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:RC,hash:e=>(e=>Es(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:xM(e),onMenuSetup:xM(e)}))(e)),(e=>A.from(nC(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:y(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=ct(e.selection.getNode());return bs(t,(e=>A.some(e).filter(no).bind((e=>Xo(e,"lang").map((t=>({code:t,customCode:Xo(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=qe();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),vM(o.clear,xM(e)(t))},onMenuSetup:xM(e)}))))(e).each((t=>pz(e,t)))},fz=e=>wM(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),bz=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),vM((()=>e.off("PastePlainTextToggle",n)),xM(e)(o))},vz=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},xz=e=>{(e=>{(e=>{nB.each([{name:"bold",text:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:yM(e,t.name),onAction:vz(e,t.name),shortcut:t.shortcut})}));for(let t=1;t<=6;t++){const o="h"+t,n=`Access+${t}`;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:yM(e,o),onAction:vz(e,o),shortcut:n})}})(e),(e=>{nB.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy",context:"any"},{name:"help",text:"Help",action:"mceHelp",icon:"help",shortcut:"Alt+0",context:"any"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A",context:"any"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print",shortcut:"Meta+P",context:"any"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:CM(e,t.action),shortcut:t.shortcut,context:t.context})})),nB.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:xM(e),onAction:CM(e,t.action)})}))})(e),(e=>{nB.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:CM(e,t.action),onSetup:yM(e,t.name)})}))})(e)})(e),(e=>{nB.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C",context:"any"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A",context:"any"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P",context:"any"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:CM(e,t.action),context:t.context})})),nB.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:xM(e),onAction:CM(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:xM(e),onAction:vz(e,"code")})})(e)},yz=(e,t)=>wM(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),wz=e=>wM(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),Sz=(e,t)=>{(e=>{V([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:CM(e,t.cmd),onSetup:yM(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:xM(e),onAction:CM(e,"JustifyNone")})})(e),xz(e),((e,t)=>{((e,t)=>{const o=tN(t,lN(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:xM(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=tN(t,vN(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:xM(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=tN(t,EN(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:xM(e,(()=>n.getStyleItems().length>0)),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=tN(t,mN(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:xM(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=tN(t,_N(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:xM(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:yz(e,"hasUndo"),onAction:CM(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:yz(e,"hasRedo"),onAction:CM(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:yz(e,"hasUndo"),onAction:CM(e,"undo"),shortcut:"Meta+Z"}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:yz(e,"hasRedo"),onAction:CM(e,"redo"),shortcut:"Meta+Y"})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=LM(e),o=VM(e),n=ye(t),s=ye(o);YM(e,"forecolor","forecolor",n),YM(e,"backcolor","hilitecolor",s),KM(e,"forecolor","forecolor","Text color",n),KM(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:CM(e,"mceToggleVisualAid"),context:"any"})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:wz(e),onAction:CM(e,"mceToggleVisualAid"),context:"any"})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:fz(e),onAction:CM(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:xM(e),onAction:CM(e,"indent")})})(e)})(e),hz(e),(e=>{const t=ye(_C(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:bz(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:bz(e,t)})})(e),(e=>{e.ui.registry.addContext("editable",(()=>e.selection.isEditable())),e.ui.registry.addContext("mode",(t=>e.mode.get()===t)),e.ui.registry.addContext("any",E),e.ui.registry.addContext("formatting",(t=>e.formatter.canApply(t))),e.ui.registry.addContext("insert",(t=>e.schema.isValidChild(e.selection.getNode().tagName,t)))})(e)},Cz=e=>r(e)?e.split(/[ ,]/):e,kz=e=>t=>t.options.get(e),Oz=kz("contextmenu_never_use_native"),_z=kz("contextmenu_avoid_overlap"),Tz=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:P(o,(e=>be(t,e)))},Ez=(e,t)=>({type:"makeshift",x:e,y:t}),Az=e=>"longpress"===e.type||0===e.type.indexOf("touch"),Mz=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(Az(e)){const t=e.touches[0];return Ez(t.pageX,t.pageY)}return Ez(e.pageX,e.pageY)})(t):((e,t)=>{const o=HS.DOM.getPos(e);return((e,t,o)=>Ez(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(Az(e)){const t=e.touches[0];return Ez(t.clientX,t.clientY)}return Ez(e.clientX,e.clientY)})(t)):Dz(e),Dz=e=>({type:"selection",root:ct(e.selection.getNode())}),Bz=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(ct(e.selection.getNode())),root:ct(e.getBody())}))(e);case"point":return Mz(e,t);case"selection":return Dz(e)}},Iz=(e,t,o,n,s,r)=>{const a=o(),i=Bz(e,t,r);WB(a,hO.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),Xx.showMenuAt(s,{anchor:i},{menu:{markers:MO("normal")},data:e})}))},Fz={onLtr:()=>[jl,Pl,Ul,Wl,$l,Gl,pc,hc,gc,uc,mc,dc],onRtl:()=>[jl,Ul,Pl,$l,Wl,Gl,pc,hc,mc,dc,gc,uc]},Rz={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},Nz=(e,t,o,n,s,r)=>{const a=Jt(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=Bz(e,t,o);return{bubble:wl(0,"point"===o?12:0,Rz),layouts:Fz,overrides:{maxWidthFunction:Oc(),maxHeightFunction:kc()},...n}})(e,t,r);WB(o,hO.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?Ac.HighlightMenuAndItem:Ac.HighlightNone;Xx.showMenuWithinBounds(s,{anchor:i},{menu:{markers:MO("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(sz(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(jN)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{VS.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Os(e.getWin(),Ss.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},zz=e=>r(e)?"|"===e:"separator"===e.type,Lz={type:"separator"},Vz=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return Lz;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:L(t,Vz)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},Hz=(e,t)=>{if(0===t.length)return e;const o=oe(e).filter((e=>!zz(e))).fold((()=>[]),(e=>[Lz]));return e.concat(o).concat(t).concat([Lz])},Pz=(e,t)=>!(e=>"longpress"===e.type||be(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),Uz=(e,t)=>Pz(e,t)?e.selection.getStart(!0):t.target,Wz=(e,t,o)=>{const n=Jt().deviceType.isTouch,s=Ag(Xx.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:rd([ud("dismissContextMenu",[Wc(Ya(),((t,o)=>{bb.close(t),e.focus()}))])])})),a=()=>Xx.hide(s),i=t=>{if(Oz(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!Oz(e))(e,t)||(e=>0===Tz(e).length)(e))return;const a=((e,t)=>{const o=_z(e),n=Pz(e,t)?"selection":"point";if(tt(o)){const s=Uz(e,t);return ps(ct(s),o)?"node":n}return n})(e,t);(n()?Nz:Iz)(e,t,(()=>{const o=Uz(e,t),n=e.ui.registry.getAll(),s=Tz(e);return((e,t,o)=>{const n=W(t,((t,n)=>fe(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n)&&tt(et(n)))return Hz(t,n.split(" "));if(l(n)&&n.length>0){const e=L(n,Vz);return Hz(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&zz(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},$z=(e,t,o,n,s,r)=>e.fold((()=>Th.snap({sensor:Vp(o-20,n-20),range:Dn(s,r),output:Vp(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return Th.snap({sensor:Vp(s,r),range:Dn(40,40),output:Vp(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),Gz=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>yt(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),jz=e=>dv(_v.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:rd([Th.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),lv.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),qz=(e,t)=>{const o=ye([]),n=ye([]),s=ye(!1),r=Xe(),a=Xe(),i=e=>{const o=Ns(e);return $z(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Ns(e);return $z(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=Gz((()=>L(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=Gz((()=>L(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=jz(c),m=jz(d),g=Ag(u.asSpec()),p=Ag(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);Th.snapTo(t,r),((t,o)=>{const n=o.dom.getBoundingClientRect();vn(t.element,"display");const r=mo(ct(e.getBody())).dom.innerHeight,a=n[s]<0,i=((e,t)=>e[s]>t)(n,r);(a||i)&&dn(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");if(Jt().deviceType.isTouch()){const i=e=>L(e,ct);e.on("TableSelectionChange",(e=>{s.get()||(Bm(t,g),Bm(t,p),s.set(!0));const l=ct(e.start),c=ct(e.finish);r.set(l),a.set(c),A.from(e.otherCells).each((e=>{o.set(i(e.upOrLeftCells)),n.set(i(e.downOrRightCells)),f(l),b(c)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(Rm(g),Rm(p),s.set(!1)),r.clear(),a.clear()}))}},Xz=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:rd([wm.config({mode:"flow",selector:"div[role=button]"}),tp.config({disabled:o.isDisabled}),kA((()=>o.checkUiComponentContext("any"))),Pb.config({}),Zm.config({}),ud("elementPathEvents",[Jc(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>wm.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=fM(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?W(r,((t,n,r)=>{const a=((t,n,s)=>_v.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s}},components:[Og(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:rd([iv.config({...o.tooltips.getConfig({tooltipText:o.translate(["Select the {0} element",n.nodeName.toLowerCase()]),onShow:(e,t)=>{((e,t)=>{const o=A.from(qo(e,"id")).getOrThunk((()=>{const e=Ne("aria");return Go(t,"id",e),e}));Go(e,"aria-describedby",o)})(e.element,t.element)},onHide:e=>{var t;t=e.element,Ko(t,"aria-describedby")}})}),uA(o.isDisabled),kA((()=>o.checkUiComponentContext("any")))])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[Og(` ${s} `)]},a])}),[]):[];Zm.set(t,a)}))}))])]),components:[]}};var Yz;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(Yz||(Yz={}));const Kz=(e,t,o)=>{const n=ct(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:VN(n+t.top,JS(e),ZS(e))};return o===Yz.Both&&(r.width=VN(s+t.left,KS(e),QS(e))),r})(e,t,o,Sn(n),Tn(n));ie(s,((e,t)=>{h(e)&&dn(n,t,LN(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},Jz=(e,t,o,n)=>{const s=Dn(20*o,20*n);return Kz(e,s,t),A.some(!0)},Qz=(e,t)=>{const o=()=>{const o=[],n=AC(e),s=CC(e),r=kC(e)||e.hasPlugin("wordcount");return s&&o.push(Xz(e,{},t)),n&&o.push((()=>{const e=DA("Alt+0");return{dom:{tag:"div",classes:["tox-statusbar__help-text"]},components:[Og(tO.translate(["Press {0} for help",e]))]}})()),r&&o.push((()=>{const o=[];return e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Zm.set(e,[Og(t.translate(["{0} "+n,o[n]]))]);return _v.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:rd([uA(t.isDisabled),kA((()=>t.checkUiComponentContext("any"))),Pb.config({}),Zm.config({}),Yh.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),ud("wordcount-events",[ed((e=>{const t=Yh.getValue(e),n="words"===t.mode?"characters":"words";Yh.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Jc((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=Yh.getValue(t);Yh.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[Na()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),kC(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=poweredby&utm_source=tiny&utm_medium=referral&utm_content=v7",rel:"noopener",target:"_blank","aria-label":e.translate(["Build with {0}","TinyMCE"])},innerHtml:e.translate(["Build with {0}",'<svg height="16" viewBox="0 0 80 16" width="80" xmlns="http://www.w3.org/2000/svg"><g opacity=".8"><path d="m80 3.537v-2.202h-7.976v11.585h7.976v-2.25h-5.474v-2.621h4.812v-2.069h-4.812v-2.443zm-10.647 6.929c-.493.217-1.13.337-1.864.337s-1.276-.156-1.805-.47a3.732 3.732 0 0 1 -1.3-1.298c-.324-.554-.48-1.191-.48-1.877s.156-1.335.48-1.877a3.635 3.635 0 0 1 1.3-1.299 3.466 3.466 0 0 1 1.805-.481c.65 0 .914.06 1.263.18.36.12.698.277.986.47.289.192.578.384.842.6l.12.085v-2.586l-.023-.024c-.385-.35-.855-.614-1.384-.818-.53-.205-1.155-.313-1.877-.313-.721 0-1.6.144-2.333.445a5.773 5.773 0 0 0 -1.937 1.251 5.929 5.929 0 0 0 -1.324 1.9c-.324.735-.48 1.565-.48 2.455s.156 1.72.48 2.454c.325.734.758 1.383 1.324 1.913.553.53 1.215.938 1.937 1.25a6.286 6.286 0 0 0 2.333.434c.819 0 1.384-.108 1.961-.313.59-.216 1.083-.505 1.468-.866l.024-.024v-2.49l-.12.096c-.41.337-.878.626-1.396.866zm-14.869-4.15-4.8-5.04-.024-.025h-.902v11.67h2.502v-6.847l2.827 3.08.385.409.397-.41 2.791-3.067v6.845h2.502v-11.679h-.902l-4.788 5.052z"/><path clip-rule="evenodd" d="m15.543 5.137c0-3.032-2.466-5.113-4.957-5.137-.36 0-.745.024-1.094.096-.157.024-3.85.758-3.85.758-3.032.602-4.62 2.466-4.704 4.788-.024.89-.024 4.27-.024 4.27.036 3.165 2.406 5.138 5.017 5.126.337 0 1.119-.109 1.287-.145.144-.024.385-.084.746-.144.661-.12 1.684-.325 3.067-.602 2.37-.409 4.103-2.009 4.44-4.33.156-1.023.084-4.692.084-4.692zm-3.213 3.308-2.346.457v2.31l-5.859 1.143v-5.75l2.346-.458v3.441l3.513-.686v-3.44l-3.513.685v-2.297l5.859-1.143v5.75zm20.09-3.296-.083-1.023h-2.13v8.794h2.346v-4.884c0-1.107.95-1.985 2.057-1.997 1.095 0 1.901.89 1.901 1.997v4.884h2.346v-5.245c-.012-2.105-1.588-3.777-3.67-3.765a3.764 3.764 0 0 0 -2.778 1.25l.012-.011zm-6.014-4.102 2.346-.458v2.298l-2.346.457z" fill-rule="evenodd"/><path d="m28.752 4.126h-2.346v8.794h2.346z"/><path clip-rule="evenodd" d="m43.777 15.483 4.043-11.357h-2.418l-1.54 4.355-.445 1.324-.36-1.324-1.54-4.355h-2.418l3.151 8.794-1.083 3.08zm-21.028-5.51c0 .722.541 1.034.878 1.034s.638-.048.95-.144l.518 1.708c-.217.145-.879.518-2.13.518a2.565 2.565 0 0 1 -2.562-2.587c-.024-1.082-.024-2.49 0-4.21h-1.54v-2.142h1.54v-1.912l2.346-.458v2.37h2.201v2.142h-2.2v3.693-.012z" fill-rule="evenodd"/></g></svg>\n'.trim()])},behaviours:rd([fd.config({})])}]}),{dom:{tag:"div",classes:["tox-statusbar__right-container"]},components:o}})()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container",...(()=>{const e="tox-statusbar__text-container--flex-start",t="tox-statusbar__text-container--flex-end";if(n){const o="tox-statusbar__text-container-3-cols";return r||s?r&&!s?[o,t]:[o,e]:[o,"tox-statusbar__text-container--space-around"]}return[r&&!s?t:e]})()]},components:o}]:[]};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const n=o(),s=((e,t)=>{const o=(e=>{const t=OC(e);return!1===t?Yz.None:"both"===t?Yz.Both:Yz.Vertical})(e);if(o===Yz.None)return A.none();const n=o===Yz.Both?"Press the arrow keys to resize the editor.":"Press the Up and Down arrow keys to resize the editor.",s=o===Yz.Both?"tox-statusbar__resize-cursor-both":"tox-statusbar__resize-cursor-default";return A.some(dO("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle",s],attributes:{"aria-label":t.translate(n),"data-mce-name":"resize-handle"},behaviours:[Th.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>Kz(e,s,o),blockerClass:"tox-blocker"}),wm.config({mode:"special",onLeft:()=>Jz(e,o,-1,0),onRight:()=>Jz(e,o,1,0),onUp:()=>Jz(e,o,0,-1),onDown:()=>Jz(e,o,0,1)}),Pb.config({}),fd.config({}),iv.config(t.tooltips.getConfig({tooltipText:t.translate("Resize")}))]},t.icons))})(e,t);return n.concat(s.toArray())})()}},Zz=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),eL=(e,t)=>{const o=e.inline,n=o?GN:zN,s=$C(e)?UF:BF,r=(()=>{const e=Xe(),t=Xe(),o=Xe();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>yt(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=Xe(),i=Xe(),l=Xe(),c=Jt().deviceType.isTouch()?["tox-platform-touch"]:[],d=VC(e),u=rC(e),m=dv({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=dv({dom:{tag:"div",classes:["tox-bottom-anchorbar"]}}),p=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(_R.getHeader),h=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),f=r.lazyGetInOuterOrDie("bottom anchor bar",g.getOpt),b=r.lazyGetInOuterOrDie("toolbar",_R.getToolbar),v=r.lazyGetInOuterOrDie("throbber",_R.getThrobber),x=((e,t,o,n)=>{const s=ye(!1),r=(e=>{const t=ye(VC(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),a={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:tO.translate,isDisabled:()=>!t.ui.isEnabled(),getOption:t.options.get,tooltips:oF(e.dialog),checkUiComponentContext:e=>{if(qS(t))return{contextType:"disabled",shouldDisable:!0};const[o,n=""]=e.split(":"),s=t.ui.registry.getAll().contexts;return{contextType:o,shouldDisable:!fe(s,o).fold((()=>fe(s,"mode").map((e=>e("design"))).getOr(!1)),(e=>"!"===n.charAt(0)?!e(n.slice(1)):e(n)))}}},i=TF(t),l=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=ye([]),s=ye([]),r=ye(!1);return e.on("PreInit",(s=>{const r=ZI(e),a=tF(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=tF(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),c=(e=>({colorPicker:$I(e),hasCustomColors:GI(e),getColors:jI(e),getColorCols:qI(e)}))(t),d=(e=>({isDraggableModal:XI(e)}))(t),u={shared:{providers:a,anchors:WI(t,o,n,r.isPositionedAtTop),header:r},urlinput:i,styles:l,colorinput:c,dialog:d,isContextMenuOpen:()=>s.get(),setContextMenuState:e=>s.set(e)},m=e=>A.none(),g={...u,shared:{...u.shared,interpreter:e=>NI(e,{},g,m),getSink:e.popup}},p={...u,shared:{...u.shared,interpreter:e=>NI(e,{},p,m),getSink:e.dialog}};return{popup:g,dialog:p}})({popup:()=>Ae.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>Ae.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,h,f),y=()=>{const t=(()=>{const t={attributes:{[Tc]:d?_c.BottomToTop:_c.TopToBottom}},o=_R.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:x.popup,onEscape:()=>{e.focus()}}),n=_R.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:x.popup.shared.getSink,providers:x.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:b,lazyHeader:()=>p().getOrDie("Could not find header element"),...t}),s=_R.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:x.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=LC(e),a=NC(e),i=IC(e),l=EC(e),c=r||a||i,g=[(h=l,_R.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]},promotionLink:h})),o];var h;return _R.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(c?[]:["tox-editor-header--empty"]),...t},components:j([i?g:[],r?[s]:a?[n]:[],PC(e)?[]:[m.asSpec()]]),sticky:$C(e),editor:e,sharedBackstage:x.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[_R.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),_R.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=_R.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:x.popup}),r=_R.parts.viewWrapper({backstage:x.popup}),i=SC(e)&&!o?A.some(Qz(e,x.popup.shared.providers)):A.none(),l=j([d?[]:[t],o?[]:[n],d?[t]:[]]),h=_R.parts.editorContainer({components:j([l,o?[]:[g.asSpec()]])}),f=WC(e),v={role:"application",...tO.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},y=Ag(_R.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[h,...o?[]:[r,...i.toArray()],s],behaviours:rd([kA((()=>x.popup.shared.providers.checkUiComponentContext("any"))),tp.config({disableClass:"tox-tinymce--disabled"}),wm.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=Cv(y);return a.set(w),{mothership:w,outerContainer:y}},w=t=>{const o=LN((e=>(e=>{const t=((e,t)=>{if("number"==typeof t)return A.from(t);const o=/^([0-9.]+)(pt|em|px)$/.exec(t.trim());if(o){const t=o[2],n=Number.parseFloat(o[1]);if(Number.isNaN(n)||n<0)return A.none();if("em"===t)return A.from(n*Number.parseFloat(window.getComputedStyle(e.dom).fontSize));if("pt"===t)return A.from(.75*n);if("px"===t)return A.from(n)}return A.none()})(ct(e.targetElm),XS(e)),o=JS(e),n=ZS(e);return t.map((e=>VN(e,o,n)))})(e).getOr(XS(e)))(e)),n=LN((e=>HN(e).getOr(YS(e)))(e));return e.inline||(bn("div","width",n)&&dn(t.element,"width",n),bn("div","height",o)?dn(t.element,"height",o):dn(t.element,"height","400px")),o};return{popups:{backstage:x.popup,getMothership:()=>Zz("popups",l)},dialogs:{backstage:x.dialog,getMothership:()=>Zz("dialogs",i)},renderUI:()=>{const o=y(),a=(()=>{const t=UC(e),o=yt(rn(),t)&&"grid"===gn(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...tO.isRtl()?{dir:"rtl"}:{}}},behaviours:rd([Zf.config({useFixed:()=>s.isDocked(p)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Hc([Wc(ja(),(e=>{dn(e.element,"width",document.body.clientWidth+"px")}))])},a=Ag(Le(n,o?r:{})),l=Cv(a);return i.set(l),{sink:a,mothership:l}})(),d=GC(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...tO.isRtl()?{dir:"rtl"}:{}}},behaviours:rd([Zf.config({useFixed:()=>s.isDocked(p),getBounds:()=>t.getPopupSinkBounds()})])},o=Ag(e),n=Cv(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(a);r.dialogUi.set(a),r.popupUi.set(d),r.mainUi.set(o);return(t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;le(aC(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),g=zC(e),h={menuItems:l,menus:jC(e),menubar:mC(e),toolbar:g.getOrThunk((()=>gC(e))),allowToolbarGroups:u===NS.floating,buttons:i,sidebar:d,views:m};var f;f=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{_R.focusMenubar(f)})),e.addShortcut("alt+F10","focus toolbar",(()=>{_R.focusToolbar(f)})),e.addCommand("ToggleToolbarDrawer",((e,t)=>{(null==t?void 0:t.skipFocus)?_R.toggleToolbarDrawerWithoutFocusing(f):_R.toggleToolbarDrawer(f)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>_R.isToolbarDrawerToggled(f))),((e,t,o)=>{const n=(e,n)=>{V([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{V([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(gv(),{target:e.target}),a=Io(),i=Mo(a,"touchstart",r),l=Mo(a,"touchmove",(e=>n(Wa(),e))),c=Mo(a,"touchend",(e=>n($a(),e))),d=Mo(a,"mousedown",r),u=Mo(a,"mouseup",(e=>{0===e.raw.button&&s(hv(),{target:e.target})})),m=e=>s(gv(),{target:ct(e.target)}),g=e=>{0===e.button&&s(hv(),{target:ct(e.target)})},p=()=>{V(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(Ga(),Bo(e)),f=e=>{s(pv(),{}),n(ja(),Bo(e))},b=So(ct(e.getElement())),v=Do(b,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=QC(e,t.element).map((e=>[e.element,...e.others])).getOr([]);R(s,(e=>yt(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Za(),o))}}))})),x=()=>s(pv(),{}),y=t=>{t.state&&s(gv(),{target:ct(e.getContainer())})},w=e=>{s(gv(),{target:ct(e.relatedTarget.getContainer())})},S=t=>e.dispatch("focusin",t),C=t=>e.dispatch("focusout",t);e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",x),e.on("AfterProgressState",y),e.on("DismissPopups",w),V([t,...o],(e=>{e.element.dom.addEventListener("focusin",S),e.element.dom.addEventListener("focusout",C)}))})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",x),e.off("AfterProgressState",y),e.off("DismissPopups",w),V([t,...o],(e=>{e.element.dom.removeEventListener("focusin",S),e.element.dom.removeEventListener("focusout",C)})),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind(),v.unbind()})),e.on("detach",(()=>{V([t,...o],Hm),V([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,x.popup.shared,p),Sz(e,x.popup),Wz(e,x.popup.shared.getSink,x.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();V(re(t),(o=>{const n=t[o],s=()=>He(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}},context:"any"})}))})(e),QF(e,v,x.popup.shared),gz(e,c,r.sink,{backstage:x.popup}),qz(e,r.sink);const b={targetNode:e.getElement(),height:w(o.outerContainer)};return n.render(e,t,h,x.popup,b)})({popupUi:d,dialogUi:a,mainUi:o,uiMotherships:r.getUiMotherships()})}}},tL=e=>{const t=[],o={};return ie(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?Ae.error(t):Ae.value(o)},oL=(e,t,o,n)=>{const s=dv(Ax.sketch((s=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:L(e.items,(e=>FI(s,e,t,o,n)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[s.asSpec()]}],behaviours:rd([wm.config({mode:"acyclic",useTabstopAt:k(mB)}),(r=s,zg.config({find:r.getOpt})),HD(s,{postprocess:e=>tL(e).fold((e=>(console.error(e),{})),w)}),ud("dialog-body-panel",[Wc(xa(),((e,t)=>{e.getSystem().broadcastOn([vB],{newFocus:A.some(t.event.target)})}))])])};var r},nL=(e,t)=>{dn(e,"height",t+"px"),dn(e,"flex-basis",t+"px")},sL=(e,t,o)=>{Gn(e,'[role="dialog"]').each((e=>{Xn(e,'[role="tablist"]').each((n=>{o.get().map((o=>(dn(t,"height","0"),dn(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=uo(e).dom,s=Gn(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===gn(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Sn(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Tn(o)?Math.max(Sn(o),a):a,l=parseInt(gn(e,"margin-top"),10)||0,c=parseInt(gn(e,"margin-bottom"),10)||0;return r-(Sn(e)+l+c-i)})(e,t,n))))).each((e=>{nL(t,e)}))}))}))},rL=e=>Xn(e,'[role="tabpanel"]'),aL="send-data-to-section",iL="send-data-to-view",lL=(e,t,o,n)=>{const s=ye({}),r=e=>{const t=Yh.getValue(e),o=tL(t).getOr({}),n=s.get(),r=Le(n,o);s.set(r)},a=e=>{const t=s.get();Yh.setValue(e,t)},i=ye(null),l=L(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[Og(o.shared.providers.translate(e.title))],view:()=>[Ax.sketch((s=>({dom:{tag:"div",classes:["tox-form"]},components:L(e.items,(e=>FI(s,e,t,o,n))),formBehaviours:rd([wm.config({mode:"acyclic",useTabstopAt:k(mB)}),ud("TabView.form.events",[Jc(a),Qc(r)]),ob.config({channels:la([{key:aL,value:{onReceive:r}},{key:iL,value:{onReceive:a}}])})])})))]}))),c=(e=>{const t=Xe(),o=[Jc((o=>{const n=o.element;rL(n).each((s=>{dn(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>L(e,((n,s)=>{Zm.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Zm.set(o,[]),r.height})))(e,s,o),r=(e=>te(Z(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),sL(n,s,t),vn(s,"visibility"),((e,t)=>{te(e).each((e=>ES.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{sL(n,s,t)}))}))})),Wc(ja(),(e=>{const o=e.element;rL(o).each((e=>{sL(o,e,t)}))})),Wc(BD,((e,o)=>{const n=e.element;rL(n).each((e=>{const o=zo(So(e));dn(e,"visibility","hidden");const s=hn(e,"height").map((e=>parseInt(e,10)));vn(e,"height"),vn(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),sL(n,e,t)):s.each((t=>{nL(e,t)})),vn(e,"visibility"),o.each(Fo)}))}))];return{extraEvents:o,selectFirst:!1}})(l);return ES.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=Yh.getValue(t);Rc(e,DD,{name:n,oldName:i.get()}),i.set(n)},tabs:l,components:[ES.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[SS.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:rd([Pb.config({})])}),ES.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:c.selectFirst,tabSectionBehaviours:rd([ud("tabpanel",c.extraEvents),wm.config({mode:"acyclic"}),zg.config({find:e=>te(ES.getViewItems(e))}),PD(A.none(),(e=>(e.getSystem().broadcastOn([aL],{}),s.get())),((e,t)=>{s.set(t),e.getSystem().broadcastOn([iL],{})}))])})},cL=(e,t,o,n,s,r)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:rd([LD(0),ab.config({channel:`${hB}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[lL(t,e.initialData,n,r)]:[oL(t,e.initialData,n,r)]},initialData:e})])}),dL=US.deviceType.isTouch(),uL=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),mL=(e,t)=>sy.parts.close(_v.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:rd([Pb.config({})])})),gL=()=>sy.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),pL=(e,t)=>sy.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:cv(`<p>${eO(t.translate(e))}</p>`)}]}]}),hL=e=>sy.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),fL=(e,t)=>[rh.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),rh.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],bL=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return sy.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!mB(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:cv(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:dL?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:rd([fd.config({}),ud("dialog-events",e.dialogEvents.concat([Kc(xa(),((e,t)=>{Fg.isBlocked(e)||wm.focusIn(e)})),Wc(Ja(),((e,t)=>{e.getSystem().broadcastOn([vB],{newFocus:t.event.newFocus})}))])),ud("scroll-lock",[Jc((()=>{os(rn(),s)})),Qc((()=>{ss(rn(),s)}))]),...e.extraBehaviours]),eventOrder:{[Na()]:["dialog-events"],[qa()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[Xa()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},vL=e=>_v.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),"data-mce-name":"close"}},buttonBehaviours:rd([Pb.config({}),iv.config(e.tooltips.getConfig({tooltipText:e.translate("Close")}))]),components:[dO("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{Fc(e,_D)}}),xL=(e,t,o,n)=>({dom:{tag:"h1",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:rd([ab.config({channel:`${pB}-${t}`,initialData:e,renderComponents:e=>[Og(n.translate(e.title))]})])}),yL=()=>({dom:cv('<div class="tox-dialog__draghandle"></div>')}),wL=(e,t,o)=>((e,t,o)=>{const n=sy.parts.title(xL(e,t,A.none(),o)),s=sy.parts.draghandle(yL()),r=sy.parts.close(vL(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return rh.sketch({dom:cv('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),SL=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:`${n.getOr(0)}px`,position:"absolute"}},behaviours:t,components:[{dom:cv('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),CL=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{const n=Xn(e().element,".tox-dialog__header").map((e=>Sn(e)));sy.setBusy(e(),((e,s)=>SL(o.message,s,t,n)))},onUnblock:()=>{sy.setIdle(e())}}),kL="tox-dialog--fullscreen",OL="tox-dialog--width-lg",_L="tox-dialog--width-md",TL=e=>{switch(e){case"large":return A.some(OL);case"medium":return A.some(_L);default:return A.none()}},EL=(e,t)=>{const o=ct(t.element.dom);rs(o,kL)||(is(o,[OL,_L]),TL(e).each((e=>os(o,e))))},AL=(e,t)=>{const o=ct(e.element.dom),n=ls(o),s=$(n,(e=>e===OL||e===_L)).or(TL(t));((e,t)=>{V(t,(t=>{((e,t)=>{const o=Qn(e)?e.dom.classList.toggle(t):((e,t)=>F(Zn(e),t)?ts(e,t):es(e,t))(e,t);ns(e)})(e,t)}))})(o,[kL,...s.toArray()])},ML=(e,t,o)=>Ag(bL({...e,firstTabstop:1,lazySink:o.shared.getSink,extraBehaviours:[WD({}),...e.extraBehaviours],onEscape:e=>{Fc(e,_D)},dialogEvents:t,eventOrder:{[Ra()]:[ab.name(),ob.name()],[qa()]:["scroll-lock",ab.name(),"messages","dialog-events","alloy.base.behaviour"],[Xa()]:["alloy.base.behaviour","dialog-events","messages",ab.name(),"scroll-lock"]}})),DL=(e,t={})=>L(e,(e=>"menu"===e.type?(e=>{const o=L(e.items,(e=>{const o=fe(t,e.name).getOr(ye(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),BL=e=>W(e,((e,t)=>"menu"===t.type?W(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),IL=(e,t)=>[qc(xa(),uB),e(OD,((e,o,n,s)=>{No(s.element)&&zo(So(s.element)).each(Ro),t.onClose(),o.onClose()})),e(_D,((e,t,o,n)=>{t.onCancel(e),Fc(n,OD)})),Wc(MD,((e,o)=>t.onUnblock())),Wc(AD,((e,o)=>t.onBlock(o.event)))],FL=(e,t,o)=>{const n=(t,o)=>Wc(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{ab.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...IL(n,t),n(ED,((e,t)=>t.onSubmit(e))),n(CD,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(TD,((e,t,n,s)=>{const r=()=>s.getSystem().isConnected()?wm.focusIn(s):void 0,a=e=>Yo(e,"disabled")||Xo(e,"aria-disabled").exists((e=>"true"===e)),i=So(s.element),l=zo(i);t.onAction(e,{name:n.name,value:n.value}),zo(i).fold(r,(e=>{a(e)||l.exists((t=>wt(e,t)&&a(t)))?r():o().toOptional().filter((t=>!wt(t.element,e))).each(r)}))})),n(DD,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Qc((t=>{const o=e();Yh.setValue(t,o.getData())}))]},RL=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=H(o,(e=>"start"===e.align)),s=(e,t)=>rh.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:L(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},NL=(e,t,o)=>({dom:cv('<div class="tox-dialog__footer"></div>'),components:[],behaviours:rd([ab.config({channel:`${fB}-${t}`,initialData:e,updateState:(e,t)=>{const n=L(t.buttons,(e=>{const t=dv(((e,t)=>yI(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>$(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:RL})])}),zL=(e,t,o)=>sy.parts.footer(NL(e,t,o)),LL=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=zg.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return Ax.getField(o,t).orThunk((()=>{const o=e.getFooter().bind((e=>ab.getState(e).get()));return o.bind((e=>e.lookupByName(t)))}))}return A.none()},VL=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...Yh.getValue(n),...le(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=Le(r,t),i=((e,t)=>{const o=e.getRoot();return ab.getState(o).get().map((e=>Tr(_r("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();Yh.setValue(l,i),ie(o,((e,t)=>{be(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{LL(e,t).each(o?tp.enable:tp.disable)},focus:t=>{LL(e,t).each(fd.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Rc(t,AD,{message:e})}))},unblock:()=>{n((e=>{Fc(e,MD)}))},showTab:t=>{n((o=>{const n=e.getBody();ab.getState(n).get().exists((e=>e.isTabPanel()))&&zg.getCurrent(n).each((e=>{ES.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=DL(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${gB}-${a}`],i),n.getSystem().broadcastOn([`${pB}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${hB}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${fB}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{Fc(e,OD)}))},toggleFullscreen:e.toggleFullscreen};return s},HL=(e,t,o,n=!1,s)=>{const r=Ne("dialog"),a=Ne("dialog-label"),i=Ne("dialog-content"),l=e.internalDialog,c=ye(l.size),d=TL(c.get()).toArray(),u=dv(((e,t,o,n)=>rh.sketch({dom:cv('<div class="tox-dialog__header"></div>'),components:[xL(e,t,A.some(o),n),yL(),vL(n)],containerBehaviours:rd([Th.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>Yn(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"},onDrag:(e,t)=>{e.getSystem().broadcastOn([pv()],{target:t})}})])}))({title:l.title,draggable:!0},r,a,o.shared.providers)),m=dv(((e,t,o,n,s,r)=>cL(e,t,A.some(o),n,s,r))({body:l.body,initialData:l.initialData},r,i,o,n,(e=>LL(x,e)))),g=DL(l.buttons),p=BL(g),h=$e(0!==g.length,dv(((e,t,o)=>NL(e,t,o))({buttons:g},r,o))),f=FL((()=>w),{onBlock:e=>{Fg.block(v,((t,n)=>{const s=u.getOpt(v).map((e=>Sn(e.element)));return SL(e.message,n,o.shared.providers,s)}))},onUnblock:()=>{Fg.unblock(v)},onClose:()=>t.closeWindow()},o.shared.getSink),b=Jt().os,v=Ag({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline",...d],attributes:{role:"dialog",...b.isMacOS()?{"aria-label":l.title}:{"aria-labelledby":a}}},eventOrder:{[Ra()]:[ab.name(),ob.name()],[Na()]:["execute-on-form"],[qa()]:["reflecting","execute-on-form"]},behaviours:rd([wm.config({mode:"cyclic",onEscape:e=>(Fc(e,OD),A.some(!0)),useTabstopAt:e=>!mB(e)&&("button"!==eo(e)||"disabled"!==qo(e,"disabled")),firstTabstop:1}),ab.config({channel:`${gB}-${r}`,updateState:(e,t)=>(c.set(t.internalDialog.size),EL(t.internalDialog.size,e),s(),A.some(t)),initialData:e}),fd.config({}),ud("execute-on-form",f.concat([Kc(xa(),((e,t)=>{wm.focusIn(e)})),Wc(Ja(),((e,t)=>{e.getSystem().broadcastOn([vB],{newFocus:t.event.newFocus})}))])),Fg.config({getRoot:()=>A.some(v)}),Zm.config({}),WD({})]),components:[u.asSpec(),m.asSpec(),...h.map((e=>e.asSpec())).toArray()]}),x={getId:y(r),getRoot:y(v),getFooter:()=>h.map((e=>e.get(v))),getBody:()=>m.get(v),getFormWrapper:()=>{const e=m.get(v);return zg.getCurrent(e).getOr(e)},toggleFullscreen:()=>{AL(v,c.get())}},w=VL(x,t.redial,p);return{dialog:v,instanceApi:w}};var PL=tinymce.util.Tools.resolve("tinymce.util.URI");const UL=["insertContent","setContent","execCommand","close","block","unblock"],WL=e=>a(e)&&-1!==UL.indexOf(e.mceAction),$L=(e,t,o,n)=>{const s=Ne("dialog"),i=wL(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[cB(A.none(),{dom:{tag:"iframe",attributes:{src:e.url}},behaviours:rd([Pb.config({}),fd.config({})])})]}],behaviours:rd([wm.config({mode:"acyclic",useTabstopAt:k(mB)})])};return sy.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some(zL({buttons:e},s,n)))),u=((e,t)=>{const o=(e,t)=>Wc(e,((e,o)=>{n(e,((n,s)=>{t(y,n,o.event,e)}))})),n=(e,t)=>{ab.getState(e).get().each((o=>{t(o,e)}))};return[...IL(o,t),o(TD,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})(0,CL((()=>x),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new PL(e.url,{base_uri:new PL(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=qe(),v=[ab.config({channel:`${gB}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),ud("messages",[Jc((()=>{const t=Mo(ct(window),"message",(t=>{if(h.isSameOrigin(new PL(t.raw.origin))){const n=t.raw.data;WL(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,y,n):(e=>!WL(e)&&a(e)&&be(e,"mceAction"))(n)&&e.onMessage(y,n)}}));b.set(t)})),Qc(b.clear)]),ob.config({channels:{[bB]:{onReceive:(e,t)=>{Xn(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],x=ML({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},u,n),y=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Rc(t,AD,{message:e})}))},unblock:()=>{t((e=>{Fc(e,MD)}))},close:()=>{t((e=>{Fc(e,OD)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([bB],e)}))}}})(x);return{dialog:x,instanceApi:y}},GL=(e,t)=>Tr(_r("data",t,e)),jL=e=>ps(e,".tox-alert-dialog")||ps(e,".tox-confirm-dialog"),qL=(e,t,o,n)=>t&&o?[]:[Ep.config({contextual:{lazyContext:()=>A.some(Rs(ct(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition",onHide:n},modes:["top"],lazyViewport:t=>QC(e,t.element).map((e=>({bounds:ZC(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:In(e.element).top})}))).getOrThunk((()=>({bounds:Ls(),optScrollEnv:A.none()})))})],XL=e=>{const t=e.editor,o=$C(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{sy.hide(l),n()},r=dv(yI({context:"any",name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=gL(),i=mL(s,t.providers),l=Ag(bL({lazySink:()=>t.getSink(),header:uL(a,i),body:pL(o,t.providers),footer:A.some(hL(fL([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Wc(_D,s)],eventOrder:{}}));sy.show(l);const c=r.get(l);fd.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{sy.hide(c),n(e)},r=dv(yI({context:"any",name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=yI({context:"any",name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=gL(),l=mL((()=>s(!1)),t.providers),c=Ag(bL({lazySink:()=>t.getSink(),header:uL(i,l),body:pL(o,t.providers),footer:A.some(hL(fL([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Wc(_D,(()=>s(!1))),Wc(ED,(()=>s(!0)))],eventOrder:{}}));sy.show(c);const d=r.get(c);fd.focus(d)}}})(e.backstages.dialog),r=(t,o)=>pE.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=Ne("dialog"),s=e.internalDialog,r=wL(s.title,n,o),a=ye(s.size),i=TL(a.get()).toArray(),l=((e,t,o,n)=>{const s=cL(e,t,A.none(),o,!1,n);return sy.parts.body(s)})({body:s.body,initialData:s.initialData},n,o,(e=>LL(h,e))),c=DL(s.buttons),d=BL(c),u=$e(0!==c.length,zL({buttons:c},n,o)),m=FL((()=>f),CL((()=>p),o.shared.providers,t),o.shared.getSink),g={id:n,header:r,body:l,footer:u,extraClasses:i,extraBehaviours:[ab.config({channel:`${gB}-${n}`,updateState:(e,t)=>(a.set(t.internalDialog.size),EL(t.internalDialog.size,e),A.some(t)),initialData:e})],extraStyles:{}},p=ML(g,m,o),h={getId:y(n),getRoot:y(p),getBody:()=>sy.getBody(p),getFooter:()=>sy.getFooter(p),getFormWrapper:()=>{const e=sy.getBody(p);return zg.getCurrent(e).getOr(e)},toggleFullscreen:()=>{AL(p,a.get())}},f=VL(h,t.redial,d);return{dialog:p,instanceApi:f}})({dataValidator:s,initialData:r,internalDialog:t},{redial:pE.redial,closeWindow:()=>{sy.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return sy.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a)=>pE.open(((n,i,l)=>{const c=GL(i,l),d=Xe(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{Xx.reposition(e),o&&u||Ep.refresh(e)})),g=HL({dataValidator:l,initialData:c,internalDialog:n},{redial:pE.redial,closeWindow:()=>{d.on(Xx.hide),t.off("ResizeEditor",m),t.off("ScrollWindow",p),d.clear(),r(g.instanceApi)}},e.backstages.popup,a.ariaAttrs,m),p=()=>g.dialog.getSystem().broadcastOn([pv()],{target:g.dialog.element}),h=Ag(Xx.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:a.persistent?{event:"doNotDismissYet"}:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:rd([ud("window-manager-inline-events",[Wc(Ya(),((e,t)=>{Fc(g.dialog,_D)}))]),...qL(t,o,u,(()=>g.dialog.getSystem().broadcastOn([gv()],{target:g.dialog.element})))]),isExtraPart:(e,t)=>jL(t)}));return d.set(h),Xx.showWithinBounds(h,Mg(g.dialog),{anchor:s},(()=>{const e=t.inline?rn():ct(t.getContainer()),o=Rs(e);return A.some(o)})),o&&u||(Ep.refresh(h),t.on("ResizeEditor",m)),t.on("ScrollWindow",p),g.instanceApi.setData(c),wm.focusIn(g.dialog),g.instanceApi}),n),i=(o,n,s,r)=>pE.open(((o,a,i)=>{const l=GL(a,i),c=Xe(),d=e.backstages.popup.shared.header.isPositionedAtTop(),u=()=>c.on((e=>{Xx.reposition(e),Ep.refresh(e)})),m=HL({dataValidator:i,initialData:l,internalDialog:o},{redial:pE.redial,closeWindow:()=>{c.on(Xx.hide),t.off("ResizeEditor ScrollWindow ElementScroll",u),c.clear(),s(m.instanceApi)}},e.backstages.popup,r.ariaAttrs,u),g=Ag(Xx.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:r.persistent?{event:"doNotDismissYet"}:{},...d?{}:{fireRepositionEventInstead:{}},inlineBehaviours:rd([ud("window-manager-inline-events",[Wc(Ya(),((e,t)=>{Fc(m.dialog,_D)}))]),Ep.config({contextual:{lazyContext:()=>A.some(Rs(ct(t.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top","bottom"],lazyViewport:e=>QC(t,e.element).map((e=>({bounds:ZC(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:In(e.element).top})}))).getOrThunk((()=>({bounds:Ls(),optScrollEnv:A.none()})))})]),isExtraPart:(e,t)=>jL(t)}));return c.set(g),Xx.showWithinBounds(g,Mg(m.dialog),{anchor:n},(()=>e.backstages.popup.shared.getSink().toOptional().bind((e=>{const o=QC(t,e.element).map((e=>ZC(e))).getOr(Ls()),n=Rs(ct(t.getContentAreaContainer())),s=zs(n,o);return A.some(Fs(s.x,s.y,s.width,s.height-15))})))),Ep.refresh(g),t.on("ResizeEditor ScrollWindow ElementScroll ResizeWindow",u),m.instanceApi.setData(l),wm.focusIn(m.dialog),m.instanceApi}),o);return{open:(t,o,n)=>{if(!u(o)){if("toolbar"===o.inline)return a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o);if("bottom"===o.inline)return i(t,e.backstages.popup.shared.anchors.inlineBottomDialog(),n,o);if("cursor"===o.inline)return a(t,e.backstages.popup.shared.anchors.cursor(),n,o)}return r(t,n)},openUrl:(o,n)=>((o,n)=>pE.openUrl((o=>{const s=$L(o,{closeWindow:()=>{sy.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return sy.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}},YL=e=>{GS(e),(e=>{const t=e.options.register,o=e=>{return f(e,r)?{value:(t=e,MM(t.map(((e,t)=>t%2==0?"#"+(e=>{return(t=e,XA(t)?A.some({value:YA(t)}):A.none()).orThunk((()=>dM(e).map(JA))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return JA(rM(s,r,a,i))}));var t})(e).value:e)))),valid:!0}:{valid:!1,message:"Must be an array of strings."};var t},n=e=>h(e)&&e>0?{value:e,valid:!0}:{valid:!1,message:"Must be a positive number."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_raw",{processor:e=>f(e,r)?{value:MM(e),valid:!0}:{valid:!1,message:"Must be an array of strings."}}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:n,default:FM(e)}),t("color_cols_foreground",{processor:n,default:RM(e,EM)}),t("color_cols_background",{processor:n,default:RM(e,AM)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:BM}),t("color_default_background",{processor:"string",default:BM})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:Cz(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)};LS.add("silver",(e=>{YL(e);let t=()=>Ls();const{dialogs:o,popups:n,renderUI:s}=eL(e,{getPopupSinkBounds:()=>t()});bD(e,n.backstage.shared);const r=XL({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}}),a=Xe();return{renderUI:()=>{const o=s();return QC(e,n.getMothership().element).each((e=>{t=()=>ZC(e)})),o},getWindowManagerImpl:y(r),getNotificationManagerImpl:()=>gO(e,{backstage:n.backstage},n.getMothership(),a),getPromotionElement:()=>Xn(ct(e.getContainer()),".tox-promotion").map((e=>e.dom)).getOrNull()}}))}();