!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(r=o=e,(a=String).prototype.isPrototypeOf(r)||(null===(s=o.constructor)||void 0===s?void 0:s.name)===a.name)?"string":t;var r,o,a,s})(t)===e,r=t("string"),o=t("object"),a=t("array"),s=e=>!(e=>null==e)(e),i=e=>"function"==typeof e;class n{constructor(e,t){this.tag=e,this.value=t}static some(e){return new n(!0,e)}static none(){return n.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?n.some(e(this.value)):n.none()}bind(e){return this.tag?e(this.value):n.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:n.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return s(e)?n.some(e):n.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}n.singletonNone=new n(!1),Array.prototype.slice;const l=Array.prototype.push,c=(e,t)=>{for(let r=0,o=e.length;r<o;r++)t(e[r],r)},m=e=>{const t=[];for(let r=0,o=e.length;r<o;++r){if(!a(e[r]))throw new Error("Arr.flatten item "+r+" was not an array, input: "+e);l.apply(t,e[r])}return t};i(Array.from)&&Array.from;const u=Object.keys,d=Object.hasOwnProperty,h=(e,t)=>p(e,t)?n.from(e[t]):n.none(),p=(e,t)=>d.call(e,t),g=e=>t=>t.options.get(e),b=g("audio_template_callback"),w=g("video_template_callback"),f=g("iframe_template_callback"),v=g("media_live_embeds"),y=g("media_filter_html"),x=g("media_url_resolver"),_=g("media_alt_source"),k=g("media_poster"),j=g("media_dimensions");var A=tinymce.util.Tools.resolve("tinymce.util.Tools"),O=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),S=tinymce.util.Tools.resolve("tinymce.html.DomParser");const $=O.DOM,C=e=>e.replace(/px$/,""),T=e=>{const t=e.attr("style"),r=t?$.parseStyle(t):{};return{type:"ephox-embed-iri",source:e.attr("data-ephox-embed-iri"),altsource:"",poster:"",width:h(r,"max-width").map(C).getOr(""),height:h(r,"max-height").map(C).getOr("")}},z=(e,t)=>{let r={};for(let o=S({validate:!1,forced_root_block:!1},t).parse(e);o;o=o.walk())if(1===o.type){const e=o.name;if(o.attr("data-ephox-embed-iri")){r=T(o);break}r.source||"param"!==e||(r.source=o.attr("movie")),"iframe"!==e&&"object"!==e&&"embed"!==e&&"video"!==e&&"audio"!==e||(r.type||(r.type=e),r=A.extend(o.attributes.map,r)),"source"===e&&(r.source?r.altsource||(r.altsource=o.attr("src")):r.source=o.attr("src")),"img"!==e||r.poster||(r.poster=o.attr("src"))}return r.source=r.source||r.src||"",r.altsource=r.altsource||"",r.poster=r.poster||"",r},D=e=>{var t;const r=null!==(t=e.toLowerCase().split(".").pop())&&void 0!==t?t:"";return h({mp3:"audio/mpeg",m4a:"audio/x-m4a",wav:"audio/wav",mp4:"video/mp4",webm:"video/webm",ogg:"video/ogg",swf:"application/x-shockwave-flash"},r).getOr("")};var F=tinymce.util.Tools.resolve("tinymce.html.Node"),M=tinymce.util.Tools.resolve("tinymce.html.Serializer");const N=(e,t={})=>S({forced_root_block:!1,validate:!1,allow_conditional_comments:!0,...t},e),P=O.DOM,R=e=>/^[0-9.]+$/.test(e)?e+"px":e,E=(e,t)=>{const r=t.attr("style"),o=r?P.parseStyle(r):{};s(e.width)&&(o["max-width"]=R(e.width)),s(e.height)&&(o["max-height"]=R(e.height)),t.attr("style",P.serializeStyle(o))},U=["source","altsource"],L=(e,t,r,o)=>{let a=0,s=0;const i=N(o);i.addNodeFilter("source",(e=>a=e.length));const n=i.parse(e);for(let e=n;e;e=e.walk())if(1===e.type){const o=e.name;if(e.attr("data-ephox-embed-iri")){E(t,e);break}switch(o){case"video":case"object":case"embed":case"img":case"iframe":void 0!==t.height&&void 0!==t.width&&(e.attr("width",t.width),e.attr("height",t.height))}if(r)switch(o){case"video":e.attr("poster",t.poster),e.attr("src",null);for(let r=a;r<2;r++)if(t[U[r]]){const o=new F("source",1);o.attr("src",t[U[r]]),o.attr("type",t[U[r]+"mime"]||null),e.append(o)}break;case"iframe":e.attr("src",t.source);break;case"object":const r=e.getAll("img").length>0;if(t.poster&&!r){e.attr("src",t.poster);const r=new F("img",1);r.attr("src",t.poster),r.attr("width",t.width),r.attr("height",t.height),e.append(r)}break;case"source":if(s<2&&(e.attr("src",t[U[s]]),e.attr("type",t[U[s]+"mime"]||null),!t[U[s]])){e.remove();continue}s++;break;case"img":t.poster||e.remove()}}return M({},o).serialize(n)},I=[{regex:/youtu\.be\/([\w\-_\?&=.]+)/i,type:"iframe",w:560,h:314,url:"www.youtube.com/embed/$1",allowFullscreen:!0},{regex:/youtube\.com(.+)v=([^&]+)(&([a-z0-9&=\-_]+))?/i,type:"iframe",w:560,h:314,url:"www.youtube.com/embed/$2?$4",allowFullscreen:!0},{regex:/youtube.com\/embed\/([a-z0-9\?&=\-_]+)/i,type:"iframe",w:560,h:314,url:"www.youtube.com/embed/$1",allowFullscreen:!0},{regex:/vimeo\.com\/([0-9]+)\?h=(\w+)/,type:"iframe",w:425,h:350,url:"player.vimeo.com/video/$1?h=$2&title=0&byline=0&portrait=0&color=8dc7dc",allowFullscreen:!0},{regex:/vimeo\.com\/(.*)\/([0-9]+)\?h=(\w+)/,type:"iframe",w:425,h:350,url:"player.vimeo.com/video/$2?h=$3&title=0&amp;byline=0",allowFullscreen:!0},{regex:/vimeo\.com\/([0-9]+)/,type:"iframe",w:425,h:350,url:"player.vimeo.com/video/$1?title=0&byline=0&portrait=0&color=8dc7dc",allowFullscreen:!0},{regex:/vimeo\.com\/(.*)\/([0-9]+)/,type:"iframe",w:425,h:350,url:"player.vimeo.com/video/$2?title=0&amp;byline=0",allowFullscreen:!0},{regex:/maps\.google\.([a-z]{2,3})\/maps\/(.+)msid=(.+)/,type:"iframe",w:425,h:350,url:'maps.google.com/maps/ms?msid=$2&output=embed"',allowFullscreen:!1},{regex:/dailymotion\.com\/video\/([^_]+)/,type:"iframe",w:480,h:270,url:"www.dailymotion.com/embed/video/$1",allowFullscreen:!0},{regex:/dai\.ly\/([^_]+)/,type:"iframe",w:480,h:270,url:"www.dailymotion.com/embed/video/$1",allowFullscreen:!0}],B=(e,t)=>{const r=(e=>{const t=e.match(/^(https?:\/\/|www\.)(.+)$/i);return t&&t.length>1?"www."===t[1]?"https://":t[1]:"https://"})(t),o=e.regex.exec(t);let a=r+e.url;if(s(o))for(let e=0;e<o.length;e++)a=a.replace("$"+e,(()=>o[e]?o[e]:""));return a.replace(/\?$/,"")},G=e=>{const t=I.filter((t=>t.regex.test(e)));return t.length>0?A.extend({},t[0],{url:B(t[0],e)}):null},W=(e,t)=>{var r;const o=A.extend({},t);if(!o.source&&(A.extend(o,z(null!==(r=o.embed)&&void 0!==r?r:"",e.schema)),!o.source))return"";o.altsource||(o.altsource=""),o.poster||(o.poster=""),o.source=e.convertURL(o.source,"source"),o.altsource=e.convertURL(o.altsource,"source"),o.sourcemime=D(o.source),o.altsourcemime=D(o.altsource),o.poster=e.convertURL(o.poster,"poster");const a=G(o.source);if(a&&(o.source=a.url,o.type=a.type,o.allowfullscreen=a.allowFullscreen,o.width=o.width||String(a.w),o.height=o.height||String(a.h)),o.embed)return L(o.embed,o,!0,e.schema);{const t=b(e),r=w(e),a=f(e);return o.width=o.width||"300",o.height=o.height||"150",A.each(o,((t,r)=>{o[r]=e.dom.encode(""+t)})),"iframe"===o.type?((e,t)=>{if(t)return t(e);{const t=e.allowfullscreen?' allowFullscreen="1"':"";return'<iframe src="'+e.source+'" width="'+e.width+'" height="'+e.height+'"'+t+"></iframe>"}})(o,a):"application/x-shockwave-flash"===o.sourcemime?(e=>{let t='<object data="'+e.source+'" width="'+e.width+'" height="'+e.height+'" type="application/x-shockwave-flash">';return e.poster&&(t+='<img src="'+e.poster+'" width="'+e.width+'" height="'+e.height+'" />'),t+="</object>",t})(o):-1!==o.sourcemime.indexOf("audio")?((e,t)=>t?t(e):'<audio controls="controls" src="'+e.source+'">'+(e.altsource?'\n<source src="'+e.altsource+'"'+(e.altsourcemime?' type="'+e.altsourcemime+'"':"")+" />\n":"")+"</audio>")(o,t):((e,t)=>t?t(e):'<video width="'+e.width+'" height="'+e.height+'"'+(e.poster?' poster="'+e.poster+'"':"")+' controls="controls">\n<source src="'+e.source+'"'+(e.sourcemime?' type="'+e.sourcemime+'"':"")+" />\n"+(e.altsource?'<source src="'+e.altsource+'"'+(e.altsourcemime?' type="'+e.altsourcemime+'"':"")+" />\n":"")+"</video>")(o,r)}},q=e=>e.hasAttribute("data-mce-object")||e.hasAttribute("data-ephox-embed-iri"),H={},J=e=>t=>W(e,t),K=(e,t)=>{const r=x(e);return r?((e,t,r)=>new Promise(((o,a)=>{const s=r=>(r.html&&(H[e.source]=r),o({url:e.source,html:r.html?r.html:t(e)}));H[e.source]?s(H[e.source]):r({url:e.source}).then(s).catch(a)})))(t,J(e),r):((e,t)=>Promise.resolve({html:t(e),url:e.source}))(t,J(e))},Q=(e,t)=>{const r={};return h(e,"dimensions").each((e=>{c(["width","height"],(o=>{h(t,o).orThunk((()=>h(e,o))).each((e=>r[o]=e))}))})),r},V=(e,t)=>{const r=t&&"dimensions"!==t?((e,t)=>h(t,e).bind((e=>h(e,"meta"))))(t,e).getOr({}):{},a=((e,t,r)=>a=>{const s=()=>h(e,a),i=()=>h(t,a),l=e=>h(e,"value").bind((e=>e.length>0?n.some(e):n.none()));return{[a]:(a===r?s().bind((e=>o(e)?l(e).orThunk(i):i().orThunk((()=>n.from(e))))):i().orThunk((()=>s().bind((e=>o(e)?l(e):n.from(e)))))).getOr("")}})(e,r,t);return{...a("source"),...a("altsource"),...a("poster"),...a("embed"),...Q(e,r)}},X=e=>{const t={...e,source:{value:h(e,"source").getOr("")},altsource:{value:h(e,"altsource").getOr("")},poster:{value:h(e,"poster").getOr("")}};return c(["width","height"],(r=>{h(e,r).each((e=>{const o=t.dimensions||{};o[r]=e,t.dimensions=o}))})),t},Y=e=>t=>{const r=t&&t.msg?"Media embed handler error: "+t.msg:"Media embed handler threw unknown error.";e.notificationManager.open({type:"error",text:r})},Z=(e,t)=>o=>{if(r(o.url)&&o.url.trim().length>0){const r=o.html,a={...z(r,t.schema),source:o.url,embed:r};e.setData(X(a))}},ee=(e,t)=>{const r=e.dom.select("*[data-mce-object]");e.insertContent(t),((e,t)=>{const r=e.dom.select("*[data-mce-object]");for(let e=0;e<t.length;e++)for(let o=r.length-1;o>=0;o--)t[e]===r[o]&&r.splice(o,1);e.selection.select(r[0])})(e,r),e.nodeChanged()},te=(e,t)=>s(t)&&"ephox-embed-iri"===t&&s(G(e)),re=(e,t)=>((e,t)=>e.width!==t.width||e.height!==t.height)(e,t)&&te(t.source,e.type),oe=e=>{const t=(e=>{const t=e.selection.getNode(),r=q(t)?e.serializer.serialize(t,{selection:!0}):"",o=z(r,e.schema),a=(()=>{if(te(o.source,o.type)){const r=e.dom.getRect(t);return{width:r.w.toString().replace(/px$/,""),height:r.h.toString().replace(/px$/,"")}}return{}})();return{embed:r,...o,...a}})(e),r=(e=>{let t=e;return{get:()=>t,set:e=>{t=e}}})(t),o=X(t),a=j(e)?[{type:"sizeinput",name:"dimensions",label:"Constrain proportions",constrain:!0}]:[],s={title:"General",name:"general",items:m([[{name:"source",type:"urlinput",filetype:"media",label:"Source",picker_text:"Browse files"}],a])},i=[];_(e)&&i.push({name:"altsource",type:"urlinput",filetype:"media",label:"Alternative source URL"}),k(e)&&i.push({name:"poster",type:"urlinput",filetype:"image",label:"Media poster (Image URL)"});const n={title:"Advanced",name:"advanced",items:i},l=[s,{title:"Embed",items:[{type:"textarea",name:"embed",label:"Paste your embed code below:"}]}];i.length>0&&l.push(n);const c={type:"tabpanel",tabs:l},u=e.windowManager.open({title:"Insert/Edit Media",size:"normal",body:c,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:t=>{const o=V(t.getData());((e,t,r)=>{var o,a;t.embed=re(e,t)&&j(r)?W(r,{...t,embed:""}):L(null!==(o=t.embed)&&void 0!==o?o:"",t,!1,r.schema),t.embed&&(e.source===t.source||(a=t.source,p(H,a)))?ee(r,t.embed):K(r,t).then((e=>{ee(r,e.html)})).catch(Y(r))})(r.get(),o,e),t.close()},onChange:(t,o)=>{switch(o.name){case"source":((t,r)=>{const o=V(r.getData(),"source");t.source!==o.source&&(Z(u,e)({url:o.source,html:""}),K(e,o).then(Z(u,e)).catch(Y(e)))})(r.get(),t);break;case"embed":(t=>{var r;const o=V(t.getData()),a=z(null!==(r=o.embed)&&void 0!==r?r:"",e.schema);t.setData(X(a))})(t);break;case"dimensions":case"altsource":case"poster":((t,r,o)=>{const a=V(t.getData(),r),s=re(o,a)&&j(e)?{...a,embed:""}:a,i=W(e,s);t.setData(X({...s,embed:i}))})(t,o.name,r.get())}r.set(V(t.getData()))},initialData:o})};var ae=tinymce.util.Tools.resolve("tinymce.Env");const se=e=>{const t=e.name;return"iframe"===t||"video"===t||"audio"===t},ie=(e,t,r,o=null)=>{const a=e.attr(r);return s(a)?a:p(t,r)?null:o},ne=(e,t,r)=>{const o="img"===t.name||"video"===e.name,a=o?"300":null,s="audio"===e.name?"30":"150",i=o?s:null;t.attr({width:ie(e,r,"width",a),height:ie(e,r,"height",i)})},le=(e,t)=>{const r=t.name,o=new F("img",1);return me(e,t,o),ne(t,o,{}),o.attr({style:t.attr("style"),src:ae.transparentSrc,"data-mce-object":r,class:"mce-object mce-object-"+r}),o},ce=(e,t)=>{var r;const o=t.name,a=new F("span",1);a.attr({contentEditable:"false",style:t.attr("style"),"data-mce-object":o,class:"mce-preview-object mce-object-"+o}),me(e,t,a);const i=e.dom.parseStyle(null!==(r=t.attr("style"))&&void 0!==r?r:""),n=new F(o,1);if(ne(t,n,i),n.attr({src:t.attr("src"),style:t.attr("style"),class:t.attr("class")}),"iframe"===o)n.attr({allowfullscreen:t.attr("allowfullscreen"),frameborder:"0",sandbox:t.attr("sandbox"),referrerpolicy:t.attr("referrerpolicy")});else{c(["controls","crossorigin","currentTime","loop","muted","poster","preload"],(e=>{n.attr(e,t.attr(e))}));const r=a.attr("data-mce-html");s(r)&&((e,t,r,o)=>{const a=N(e.schema).parse(o,{context:t});for(;a.firstChild;)r.append(a.firstChild)})(e,o,n,unescape(r))}const l=new F("span",1);return l.attr("class","mce-shim"),a.append(n),a.append(l),a},me=(e,t,r)=>{var o;const a=null!==(o=t.attributes)&&void 0!==o?o:[];let s=a.length;for(;s--;){const t=a[s].name;let o=a[s].value;"width"===t||"height"===t||"style"===t||(n="data-mce-",(i=t).length>=9&&i.substr(0,9)===n)||("data"!==t&&"src"!==t||(o=e.convertURL(o,t)),r.attr("data-mce-p-"+t,o))}var i,n;const l=M({inner:!0},e.schema),m=new F("div",1);c(t.children(),(e=>m.append(e)));const u=l.serialize(m);u&&(r.attr("data-mce-html",escape(u)),r.empty())},ue=e=>{const t=e.attr("class");return r(t)&&/\btiny-pageembed\b/.test(t)},de=e=>{let t=e;for(;t=t.parent;)if(t.attr("data-ephox-embed-iri")||ue(t))return!0;return!1},he=(e,t,r)=>{const o=(0,e.options.get)("xss_sanitization"),a=y(e);return N(e.schema,{sanitize:o,validate:a}).parse(r,{context:t})},pe=e=>t=>{const r=()=>{t.setEnabled(e.selection.isEditable())};return e.on("NodeChange",r),r(),()=>{e.off("NodeChange",r)}};e.add("media",(e=>((e=>{const t=e.options.register;t("audio_template_callback",{processor:"function"}),t("video_template_callback",{processor:"function"}),t("iframe_template_callback",{processor:"function"}),t("media_live_embeds",{processor:"boolean",default:!0}),t("media_filter_html",{processor:"boolean",default:!0}),t("media_url_resolver",{processor:"function"}),t("media_alt_source",{processor:"boolean",default:!0}),t("media_poster",{processor:"boolean",default:!0}),t("media_dimensions",{processor:"boolean",default:!0})})(e),(e=>{e.addCommand("mceMedia",(()=>{oe(e)}))})(e),(e=>{const t=()=>e.execCommand("mceMedia");e.ui.registry.addToggleButton("media",{tooltip:"Insert/edit media",icon:"embed",onAction:t,onSetup:t=>{const r=e.selection;t.setActive(q(r.getNode()));const o=r.selectorChangedWithUnbind("img[data-mce-object],span[data-mce-object],div[data-ephox-embed-iri]",t.setActive).unbind,a=pe(e)(t);return()=>{o(),a()}}}),e.ui.registry.addMenuItem("media",{icon:"embed",text:"Media...",onAction:t,onSetup:pe(e)})})(e),(e=>{e.on("ResolveName",(e=>{let t;1===e.target.nodeType&&(t=e.target.getAttribute("data-mce-object"))&&(e.name=t)}))})(e),(e=>{e.on("PreInit",(()=>{const{schema:t,serializer:r,parser:o}=e,a=t.getBoolAttrs();c("webkitallowfullscreen mozallowfullscreen".split(" "),(e=>{a[e]={}})),((e,t)=>{const r=u(e);for(let o=0,a=r.length;o<a;o++){const a=r[o];t(e[a],a)}})({embed:["wmode"]},((e,r)=>{const o=t.getElementRule(r);o&&c(e,(e=>{o.attributes[e]={},o.attributesOrder.push(e)}))})),o.addNodeFilter("iframe,video,audio,object,embed",(e=>t=>{let r,o=t.length;for(;o--;)r=t[o],r.parent&&(r.parent.attr("data-mce-object")||(se(r)&&v(e)?de(r)||r.replace(ce(e,r)):de(r)||r.replace(le(e,r))))})(e)),r.addAttributeFilter("data-mce-object",((t,r)=>{var o;let a=t.length;for(;a--;){const s=t[a];if(!s.parent)continue;const i=s.attr(r),n=new F(i,1);if("audio"!==i){const e=s.attr("class");e&&-1!==e.indexOf("mce-preview-object")&&s.firstChild?n.attr({width:s.firstChild.attr("width"),height:s.firstChild.attr("height")}):n.attr({width:s.attr("width"),height:s.attr("height")})}n.attr({style:s.attr("style")});const l=null!==(o=s.attributes)&&void 0!==o?o:[];let m=l.length;for(;m--;){const e=l[m].name;0===e.indexOf("data-mce-p-")&&n.attr(e.substr(11),l[m].value)}const u=s.attr("data-mce-html");if(u){const t=he(e,i,unescape(u));c(t.children(),(e=>n.append(e)))}s.replace(n)}}))})),e.on("SetContent",(()=>{const t=e.dom;c(t.select("span.mce-preview-object"),(e=>{0===t.select("span.mce-shim",e).length&&t.add(e,"span",{class:"mce-shim"})}))}))})(e),(e=>{e.on("mousedown",(t=>{const r=e.dom.getParent(t.target,".mce-preview-object");r&&"2"===e.dom.getAttrib(r,"data-mce-selected")&&t.stopImmediatePropagation()})),e.on("click keyup touchend",(()=>{const t=e.selection.getNode();t&&e.dom.hasClass(t,"mce-preview-object")&&e.dom.getAttrib(t,"data-mce-selected")&&t.setAttribute("data-mce-selected","2")})),e.on("ObjectResized",(t=>{const r=t.target;if(r.getAttribute("data-mce-object")){let o=r.getAttribute("data-mce-html");o&&(o=unescape(o),r.setAttribute("data-mce-html",escape(L(o,{width:String(t.width),height:String(t.height)},!1,e.schema))))}}))})(e),(e=>({showDialog:()=>{oe(e)}}))(e))))}();