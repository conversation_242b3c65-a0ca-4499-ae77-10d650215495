# LearnLegend Project - Comprehensive .gitignore
# This file covers backend (Node.js/Bun), frontend (React/Vite), and mobile (Flutter) projects

# ===================================
# GENERAL FILES
# ===================================
# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*~

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.metadata
.classpath
.settings/
*.sublime-project
*.sublime-workspace

# Temporary files
*.tmp
*.temp
*.log
*.pid
*.seed
*.pid.lock

# ===================================
# BACKEND (Node.js/Bun/Fastify)
# ===================================
backend/node_modules/
backend/npm-debug.log*
backend/yarn-debug.log*
backend/yarn-error.log*
backend/lerna-debug.log*
backend/.npm
backend/.eslintcache
backend/.nyc_output
backend/lib-cov
backend/coverage/
backend/.grunt
backend/bower_components
backend/.lock-wscript
backend/build/Release
backend/.node_repl_history
backend/*.tgz
backend/.yarn-integrity
backend/.env
backend/.env.local
backend/.env.development.local
backend/.env.test.local
backend/.env.production.local
backend/.parcel-cache
backend/.cache
backend/dist/
backend/build/

# Bun specific
backend/bun.lockb
backend/.bun

# Database
backend/*.db
backend/*.sqlite
backend/*.sqlite3

# Uploads (keep structure but ignore actual files)
backend/uploads/*
!backend/uploads/.gitkeep
!backend/uploads/subjects/.gitkeep
!backend/uploads/slider/.gitkeep

# Logs
backend/logs/
backend/*.log
backend/npm-debug.log*
backend/yarn-debug.log*
backend/yarn-error.log*

# Runtime data
backend/pids
backend/*.pid
backend/*.seed
backend/*.pid.lock

# ===================================
# FRONTEND (React/Vite)
# ===================================
frontend/node_modules/
frontend/dist/
frontend/build/
frontend/.env
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/.pnpm-debug.log*
frontend/.vite
frontend/.cache
frontend/.parcel-cache

# Vite specific
frontend/dist-ssr
frontend/*.local

# ===================================
# MOBILE (Flutter)
# ===================================
mobile/.dart_tool/
mobile/.flutter-plugins
mobile/.flutter-plugins-dependencies
mobile/.packages
mobile/.pub-cache/
mobile/.pub/
mobile/build/
mobile/ios/Flutter/Generated.xcconfig
mobile/ios/Flutter/flutter_export_environment.sh
mobile/ios/Runner/GeneratedPluginRegistrant.*
mobile/android/app/debug
mobile/android/app/profile
mobile/android/app/release
mobile/android/.gradle
mobile/android/captures/
mobile/android/gradlew
mobile/android/gradlew.bat
mobile/android/local.properties
mobile/android/**/GeneratedPluginRegistrant.java
mobile/android/key.properties
mobile/*.jks

# Flutter/Dart/Pub related
mobile/**/doc/api/
mobile/.dart_tool/
mobile/.flutter-plugins
mobile/.flutter-plugins-dependencies
mobile/.packages
mobile/.pub-cache/
mobile/.pub/
mobile/build/
mobile/flutter_*.png
mobile/flutter_*.log*
mobile/lib/generated_plugin_registrant.dart

# iOS related
mobile/ios/Flutter/Generated.xcconfig
mobile/ios/Flutter/flutter_export_environment.sh
mobile/ios/Runner/GeneratedPluginRegistrant.*
mobile/ios/.symlinks/
mobile/ios/Flutter/App.framework
mobile/ios/Flutter/Flutter.framework
mobile/ios/Flutter/Flutter.podspec
mobile/ios/ServiceDefinitions.json
mobile/ios/Runner/GoogleService-Info.plist

# Android related
mobile/android/app/debug
mobile/android/app/profile
mobile/android/app/release
mobile/android/.gradle
mobile/android/captures/
mobile/android/gradlew
mobile/android/gradlew.bat
mobile/android/local.properties
mobile/android/**/GeneratedPluginRegistrant.java
mobile/android/key.properties
mobile/android/.project
mobile/android/.settings/
mobile/android/app/google-services.json

# Web related (Flutter web)
mobile/web/

# macOS related
mobile/macos/Flutter/GeneratedPluginRegistrant.swift
mobile/macos/.symlinks/

# Windows related
mobile/windows/flutter/generated_plugin_registrant.cc
mobile/windows/flutter/generated_plugin_registrant.h

# Linux related
mobile/linux/flutter/generated_plugin_registrant.cc
mobile/linux/flutter/generated_plugin_registrant.h

# ===================================
# SECURITY & SENSITIVE DATA
# ===================================
# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# API Keys and secrets
**/config/secrets.json
**/config/keys.json
**/*secret*
**/*key*
**/*password*
**/google-services.json
**/GoogleService-Info.plist

# Certificates and keys
*.pem
*.key
*.crt
*.p12
*.jks
*.keystore

# ===================================
# DEPLOYMENT & CI/CD
# ===================================
# Docker
Dockerfile.prod
docker-compose.prod.yml
.dockerignore

# Deployment
deploy/
deployment/
.deploy/

# CI/CD
.github/workflows/*.yml
!.github/workflows/ci.yml

# ===================================
# DOCUMENTATION & ASSETS
# ===================================
# Documentation builds
docs/build/
docs/_build/
docs/.doctrees/

# Large assets (keep small samples)
**/*.mp4
**/*.mov
**/*.avi
**/*.mkv
**/*.webm
**/*.zip
**/*.tar.gz
**/*.rar

# ===================================
# TESTING
# ===================================
# Test coverage
coverage/
.nyc_output/
.coverage
htmlcov/
.pytest_cache/
.cache/

# Test databases
test.db
test.sqlite
test.sqlite3

# ===================================
# MISC
# ===================================
# Backup files
*.bak
*.backup
*.old
*.orig

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# System files
.fuse_hidden*
.directory
.Trash-*
.nfs*
