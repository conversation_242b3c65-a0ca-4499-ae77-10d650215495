import { prisma } from './src/lib/prisma.js'
import { hashPassword } from './src/utils/auth.js'

async function createAdmin() {
  try {
    console.log('Creating admin user...')
    
    // Check if admin already exists
    const existingAdmin = await prisma.admin.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (existingAdmin) {
      console.log('Admin user already exists!')
      return
    }
    
    // Hash password
    const hashedPassword = await hashPassword('admin123')
    
    // Create admin
    const admin = await prisma.admin.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin User'
      }
    })
    
    console.log('Admin user created successfully:', {
      id: admin.id,
      email: admin.email,
      name: admin.name
    })
    
  } catch (error) {
    console.error('Failed to create admin:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAdmin()
