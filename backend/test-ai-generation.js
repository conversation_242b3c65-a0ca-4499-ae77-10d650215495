import 'dotenv/config'
import aiService from './src/services/aiService.js'

async function testAIGeneration() {
  console.log('Testing AI Generation Service...')

  try {
    // Load prompts first
    await aiService.loadPrompts()
    console.log('✅ Prompts loaded successfully')
    // Test chapter generation
    console.log('\n1. Testing Chapter Generation...')
    const chapters = await aiService.generateChapters(
      'Computer Science',
      'Data Structures',
      5
    )
    
    console.log('Generated chapters:', JSON.stringify(chapters, null, 2))
    
    // Test MCQ generation
    console.log('\n2. Testing MCQ Generation...')
    const mcqs = await aiService.generateMCQs(
      'Computer Science',
      'Data Structures',
      'Arrays and Linked Lists',
      3
    )
    
    console.log('Generated MCQs:', JSON.stringify(mcqs, null, 2))
    
    // Test Viva Q&A generation
    console.log('\n3. Testing Viva Q&A Generation...')
    const viva = await aiService.generateVivaQA(
      'Computer Science',
      'Data Structures',
      'Arrays and Linked Lists',
      3
    )
    
    console.log('Generated Viva Q&A:', JSON.stringify(viva, null, 2))
    
    console.log('\n✅ All AI generation tests passed!')
    
  } catch (error) {
    console.error('❌ AI generation test failed:', error)
    process.exit(1)
  }
}

// Run the test
testAIGeneration()
