{"name": "learnlegend-backend", "version": "1.0.0", "description": "Backend for LearnLegend admin panel", "main": "index.js", "scripts": {"dev": "bun --watch index.js", "start": "bun index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/jwt": "^9.1.0", "@fastify/multipart": "^9.0.3", "@fastify/static": "^8.2.0", "@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.11.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "bullmq": "^5.56.0", "dotenv": "^17.0.1", "fastify": "^5.4.0", "firebase-admin": "^13.4.0", "ioredis": "^5.6.1", "multer": "^2.0.1", "openai": "^5.8.2", "prisma": "^6.11.0", "react-router-dom": "^7.6.3", "sharp": "^0.34.2", "zod": "^3.25.67"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}