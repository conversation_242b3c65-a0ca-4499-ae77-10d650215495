{"name": "learnlegend-backend", "version": "1.0.0", "description": "Backend for LearnLegend admin panel", "main": "index.js", "scripts": {"dev": "bun --watch index.js", "start": "bun index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/jwt": "^9.1.0", "@prisma/client": "^6.11.0", "bcryptjs": "^3.0.2", "fastify": "^5.4.0", "prisma": "^6.11.0", "zod": "^3.25.67"}}