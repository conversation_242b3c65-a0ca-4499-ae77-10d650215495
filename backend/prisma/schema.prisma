// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Admin users for the admin panel
model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

// Regular users (students/learners)
model User {
  id              String    @id @default(cuid())
  googleId        String?   @unique
  email           String    @unique
  name            String
  profilePicture  String?
  fcmToken        String?
  theme           String    @default("light") // light, dark
  language        String    @default("en") // en, bn
  isActive        Boolean   @default(true)
  lastLoginAt     DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  subscriptions   UserSubscription[]
  subjectAccess   UserSubjectAccess[]

  @@map("users")
}

// Domain model
model Domain {
  id          String    @id @default(cuid())
  name        String
  description String?
  order       Int       @default(0)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  subjects Subject[]

  @@map("domains")
}

// Subject model with enhanced fields for public website
model Subject {
  id          String  @id @default(cuid())
  name        String
  slug        String  @unique // URL-friendly slug for public pages
  description String?
  imageUrl    String? // Featured image for subject
  order       Int     @default(0)
  isFeatured  Boolean @default(false) // For homepage featured subjects
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  domainId String
  domain   Domain @relation(fields: [domainId], references: [id], onDelete: Cascade)

  chapters    Chapter[]
  planSubjects PlanSubject[]
  subjectPricing SubjectPricing[]
  userSubjectAccess UserSubjectAccess[]

  @@map("subjects")
}

// Slider images for homepage carousel
model SliderImage {
  id          String  @id @default(cuid())
  title       String
  description String?
  imageUrl    String
  linkUrl     String?
  order       Int     @default(0)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("slider_images")
}

// Chapter model
model Chapter {
  id          String  @id @default(cuid())
  title       String
  description String?
  content     String? @db.Text
  reference   String? // Reference field for AI generation
  order       Int     @default(0)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  mcqs    MCQ[]
  vivaQAs VivaQA[]

  @@map("chapters")
}

// MCQ model
model MCQ {
  id          String   @id @default(cuid())
  question    String
  optionA     String
  optionB     String
  optionC     String
  optionD     String
  correctAnswer String // A, B, C, or D
  explanation String?  @db.Text // Keep as TEXT to avoid length issues
  reference   String? // Reference field for AI generation
  order       Int      @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  chapterId String
  chapter   Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@map("mcqs")
}

// Viva Q&A model
model VivaQA {
  id        String   @id @default(cuid())
  question  String
  answer    String   @db.Text
  reference String? // Reference field for AI generation
  order     Int      @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  chapterId String
  chapter   Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@map("viva_qas")
}

// Subscription plans
model SubscriptionPlan {
  id          String  @id @default(cuid())
  name        String
  description String?
  price       Float
  duration    Int     // Duration in days
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  planSubjects PlanSubject[]
  userSubscriptions UserSubscription[]

  @@map("subscription_plans")
}

// Junction table for subscription plans and subjects
model PlanSubject {
  id     String @id @default(cuid())
  planId String
  subjectId String

  // Relations
  plan    SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  subject Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([planId, subjectId])
  @@map("plan_subjects")
}

// Individual subject pricing
model SubjectPricing {
  id        String @id @default(cuid())
  subjectId String
  price     Float
  isActive  Boolean @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  subject Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([subjectId])
  @@map("subject_pricing")
}

// User subscriptions
model UserSubscription {
  id        String   @id @default(cuid())
  userId    String
  planId    String
  startDate DateTime @default(now())
  endDate   DateTime
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)

  @@map("user_subscriptions")
}

// User subject access (for individual subject purchases)
model UserSubjectAccess {
  id           String   @id @default(cuid())
  userId       String
  subjectId    String
  purchaseDate DateTime @default(now())
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  subject Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([userId, subjectId])
  @@map("user_subject_access")
}
