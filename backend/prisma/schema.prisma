// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Admin users for the admin panel
model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

// Regular users (students/learners)
model User {
  id              String    @id @default(cuid())
  googleId        String?   @unique
  email           String    @unique
  name            String
  nickname        String?   // Display name for leaderboards
  profilePicture  String?
  fcmToken        String?
  theme           String    @default("light") // light, dark
  language        String    @default("en") // en, bn
  isActive        Boolean   @default(true)
  lastLoginAt     DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  subscriptions     UserSubscription[]
  subjectAccess     UserSubjectAccess[]
  chapterProgress   ChapterProgress[]
  readingSessions   ReadingSession[]
  mcqAttempts       MCQAttempt[]
  mcqExams          MCQExam[]
  vivaAttempts      VivaAttempt[]
  achievements      UserAchievement[]
  studyPlans        StudyPlan[]
  dailyGoals        DailyGoal[]
  chatMessages      ChatMessage[]
  chatParticipants  ChatParticipant[]
  userSessions      UserSession[]
  deviceFingerprints DeviceFingerprint[]

  @@map("users")
}

// Domain model
model Domain {
  id          String    @id @default(cuid())
  name        String
  description String?
  order       Int       @default(0)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  subjects Subject[]

  @@map("domains")
}

// Subject model with enhanced fields for public website
model Subject {
  id          String  @id @default(cuid())
  name        String
  slug        String  @unique // URL-friendly slug for public pages
  description String?
  imageUrl    String? // Featured image for subject
  order       Int     @default(0)
  isFeatured  Boolean @default(false) // For homepage featured subjects
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  domainId String
  domain   Domain @relation(fields: [domainId], references: [id], onDelete: Cascade)

  chapters          Chapter[]
  planSubjects      PlanSubject[]
  subjectPricing    SubjectPricing[]
  userSubjectAccess UserSubjectAccess[]
  chatRooms         ChatRoom[]
  leaderboards      Leaderboard[]

  @@map("subjects")
}

// Slider images for homepage carousel
model SliderImage {
  id          String  @id @default(cuid())
  title       String
  description String?
  imageUrl    String
  linkUrl     String?
  order       Int     @default(0)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("slider_images")
}

// Chapter model
model Chapter {
  id          String  @id @default(cuid())
  title       String
  description String?
  content     String? @db.Text
  reference   String? // Reference field for AI generation
  order       Int     @default(0)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  mcqs            MCQ[]
  vivaQAs         VivaQA[]
  chapterProgress ChapterProgress[]
  readingSessions ReadingSession[]
  studyPlanItems  StudyPlanItem[]

  @@map("chapters")
}

// MCQ model
model MCQ {
  id          String   @id @default(cuid())
  question    String
  optionA     String
  optionB     String
  optionC     String
  optionD     String
  correctAnswer String // A, B, C, or D
  explanation String?  @db.Text // Keep as TEXT to avoid length issues
  reference   String? // Reference field for AI generation
  order       Int      @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  chapterId String
  chapter   Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  mcqAttempts MCQAttempt[]

  @@map("mcqs")
}

// Viva Q&A model
model VivaQA {
  id        String   @id @default(cuid())
  question  String
  answer    String   @db.Text
  reference String? // Reference field for AI generation
  order     Int      @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  chapterId String
  chapter   Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  vivaAttempts VivaAttempt[]

  @@map("viva_qas")
}

// Subscription plans
model SubscriptionPlan {
  id          String  @id @default(cuid())
  name        String
  description String?
  price       Float
  duration    Int     // Duration in days
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  planSubjects PlanSubject[]
  userSubscriptions UserSubscription[]

  @@map("subscription_plans")
}

// Junction table for subscription plans and subjects
model PlanSubject {
  id     String @id @default(cuid())
  planId String
  subjectId String

  // Relations
  plan    SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  subject Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([planId, subjectId])
  @@map("plan_subjects")
}

// Individual subject pricing
model SubjectPricing {
  id        String @id @default(cuid())
  subjectId String
  price     Float
  isActive  Boolean @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  subject Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([subjectId])
  @@map("subject_pricing")
}

// User subscriptions
model UserSubscription {
  id        String   @id @default(cuid())
  userId    String
  planId    String
  startDate DateTime @default(now())
  endDate   DateTime
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)

  @@map("user_subscriptions")
}

// User subject access (for individual subject purchases)
model UserSubjectAccess {
  id           String   @id @default(cuid())
  userId       String
  subjectId    String
  purchaseDate DateTime @default(now())
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  subject Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([userId, subjectId])
  @@map("user_subject_access")
}

// ===== LMS FEATURES MODELS =====

// Chapter Progress Tracking
model ChapterProgress {
  id              String   @id @default(cuid())
  userId          String
  chapterId       String
  isCompleted     Boolean  @default(false)
  readingProgress Float    @default(0) // Percentage (0-100)
  timeSpent       Int      @default(0) // Time in seconds
  lastPosition    Int      @default(0) // Last scroll position
  completedAt     DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  chapter Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@unique([userId, chapterId])
  @@map("chapter_progress")
}

// Reading Sessions for detailed tracking
model ReadingSession {
  id        String   @id @default(cuid())
  userId    String
  chapterId String
  startTime DateTime @default(now())
  endTime   DateTime?
  duration  Int      @default(0) // Duration in seconds
  createdAt DateTime @default(now())

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  chapter Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@map("reading_sessions")
}

// MCQ Individual Attempts
model MCQAttempt {
  id           String   @id @default(cuid())
  userId       String
  mcqId        String
  examId       String?  // Reference to MCQExam if part of an exam
  selectedAnswer String // A, B, C, or D
  isCorrect    Boolean
  timeSpent    Int      @default(0) // Time in seconds
  createdAt    DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  mcq  MCQ  @relation(fields: [mcqId], references: [id], onDelete: Cascade)
  exam MCQExam? @relation(fields: [examId], references: [id], onDelete: Cascade)

  @@map("mcq_attempts")
}

// MCQ Exam Sessions
model MCQExam {
  id          String   @id @default(cuid())
  userId      String
  chapterId   String?  // Null for random exams
  subjectId   String?  // For subject-wide random exams
  examType    String   // 'chapter', 'random', 'subject'
  totalQuestions Int
  correctAnswers Int   @default(0)
  score       Float    @default(0) // Percentage score
  timeSpent   Int      @default(0) // Total time in seconds
  isCompleted Boolean  @default(false)
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  attempts MCQAttempt[]

  @@map("mcq_exams")
}

// Viva Q&A Attempts
model VivaAttempt {
  id           String   @id @default(cuid())
  userId       String
  vivaQAId     String
  answerText   String?  @db.Text // Text answer
  audioUrl     String?  // Audio recording URL
  transcript   String?  @db.Text // Speech-to-text transcript
  aiScore      Float?   // AI-generated score (0-10)
  aiFeedback   String?  @db.Text // AI-generated feedback
  timeSpent    Int      @default(0) // Time in seconds
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  vivaQA VivaQA @relation(fields: [vivaQAId], references: [id], onDelete: Cascade)

  @@map("viva_attempts")
}

// User Achievements and Badges
model UserAchievement {
  id          String   @id @default(cuid())
  userId      String
  type        String   // 'badge', 'milestone', 'streak'
  name        String
  description String?
  iconUrl     String?
  earnedAt    DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_achievements")
}

// Leaderboard System
model Leaderboard {
  id         String   @id @default(cuid())
  subjectId  String
  userId     String
  score      Float    @default(0)
  rank       Int      @default(0)
  period     String   // 'daily', 'weekly', 'monthly', 'all-time'
  lastUpdated DateTime @default(now())
  createdAt  DateTime @default(now())

  // Relations
  subject Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([subjectId, userId, period])
  @@map("leaderboards")
}

// Study Plans
model StudyPlan {
  id          String   @id @default(cuid())
  userId      String
  name        String
  description String?
  targetDate  DateTime?
  isActive    Boolean  @default(true)
  isCompleted Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user  User @relation(fields: [userId], references: [id], onDelete: Cascade)
  items StudyPlanItem[]

  @@map("study_plans")
}

// Study Plan Items
model StudyPlanItem {
  id           String   @id @default(cuid())
  studyPlanId  String
  chapterId    String
  order        Int      @default(0)
  isCompleted  Boolean  @default(false)
  completedAt  DateTime?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  studyPlan StudyPlan @relation(fields: [studyPlanId], references: [id], onDelete: Cascade)
  chapter   Chapter   @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@unique([studyPlanId, chapterId])
  @@map("study_plan_items")
}

// Daily Goals
model DailyGoal {
  id          String   @id @default(cuid())
  userId      String
  date        DateTime @db.Date
  goalType    String   // 'chapters', 'mcqs', 'time'
  targetValue Int      // Target number or minutes
  currentValue Int     @default(0)
  isCompleted Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date, goalType])
  @@map("daily_goals")
}

// Chat Rooms (Subject-based)
model ChatRoom {
  id        String   @id @default(cuid())
  subjectId String
  name      String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  subject      Subject           @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  messages     ChatMessage[]
  participants ChatParticipant[]

  @@unique([subjectId])
  @@map("chat_rooms")
}

// Chat Messages
model ChatMessage {
  id         String   @id @default(cuid())
  chatRoomId String
  userId     String
  message    String   @db.Text
  messageType String  @default("text") // 'text', 'file', 'image'
  fileUrl    String?
  isDeleted  Boolean  @default(false)
  deletedAt  DateTime?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  chatRoom ChatRoom @relation(fields: [chatRoomId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("chat_messages")
}

// Chat Participants
model ChatParticipant {
  id         String   @id @default(cuid())
  chatRoomId String
  userId     String
  role       String   @default("member") // 'member', 'moderator', 'admin'
  canSend    Boolean  @default(true) // Based on subscription
  joinedAt   DateTime @default(now())
  lastSeen   DateTime @default(now())

  // Relations
  chatRoom ChatRoom @relation(fields: [chatRoomId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([chatRoomId, userId])
  @@map("chat_participants")
}

// User Sessions (Multi-device management)
model UserSession {
  id              String   @id @default(cuid())
  userId          String
  sessionToken    String   @unique
  deviceInfo      String?  // Browser/device information
  ipAddress       String?
  isActive        Boolean  @default(true)
  lastActivity    DateTime @default(now())
  createdAt       DateTime @default(now())
  expiresAt       DateTime

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// Device Fingerprints
model DeviceFingerprint {
  id          String   @id @default(cuid())
  userId      String
  fingerprint String   // Unique device/browser fingerprint
  deviceInfo  String?  // Browser, OS, screen resolution, etc.
  isActive    Boolean  @default(true)
  lastSeen    DateTime @default(now())
  createdAt   DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, fingerprint])
  @@map("device_fingerprints")
}
