// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Admin User Model
model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

// Domain Model (e.g., IT, Govt Job, Web Dev)
model Domain {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?   @db.Text
  isActive    Boolean   @default(true)
  order       Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  subjects    Subject[]

  @@map("domains")
}

// Subject Model (under each domain)
model Subject {
  id          String    @id @default(cuid())
  name        String
  slug        String?   // URL-friendly slug for public pages
  description String?   @db.Text
  imageUrl    String?   // Featured image for subject
  isActive    Boolean   @default(true)
  isFeatured  Boolean   @default(false) // For homepage featured subjects
  order       Int       @default(0)
  domainId    String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  domain      Domain    @relation(fields: [domainId], references: [id], onDelete: Cascade)
  chapters    Chapter[]

  // Subscription system relationships
  subjectPricing    SubjectPricing?
  planSubjects      PlanSubject[]
  userSubjectAccess UserSubjectAccess[]

  @@unique([domainId, name])
  @@unique([slug])
  @@map("subjects")
}

// Slider/Carousel Image Model for Homepage
model SliderImage {
  id          String    @id @default(cuid())
  title       String
  description String?   @db.Text
  imageUrl    String
  linkUrl     String?   // Optional link when clicked
  isActive    Boolean   @default(true)
  order       Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("slider_images")
}

// Chapter Model (under each subject)
model Chapter {
  id          String    @id @default(cuid())
  title       String
  description String?   @db.Text
  content     String?   @db.LongText
  audioFilePath String? @db.Text
  isActive    Boolean   @default(true)
  isDraft     Boolean   @default(false)
  isAIGenerated Boolean @default(false)
  order       Int       @default(0)
  subjectId   String
  createdBy   String?
  updatedBy   String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  subject     Subject   @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  mcqs        MCQ[]
  vivaQAs     VivaQA[]

  @@unique([subjectId, title])
  @@map("chapters")
}

// MCQ Model (Multiple Choice Questions)
model MCQ {
  id          String    @id @default(cuid())
  question    String    @db.Text
  optionA     String    @db.Text
  optionB     String    @db.Text
  optionC     String    @db.Text
  optionD     String    @db.Text
  correctAnswer String  // A, B, C, or D
  explanation String?   @db.Text
  reference   String?   @db.Text
  isActive    Boolean   @default(true)
  isDraft     Boolean   @default(false)
  isAIGenerated Boolean @default(false)
  order       Int       @default(0)
  chapterId   String
  createdBy   String?
  updatedBy   String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  chapter     Chapter   @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@map("mcqs")
}

// Viva Q&A Model (Question and Answer pairs)
model VivaQA {
  id          String    @id @default(cuid())
  question    String    @db.Text
  answer      String    @db.Text
  reference   String?   @db.Text
  isActive    Boolean   @default(true)
  isDraft     Boolean   @default(false)
  isAIGenerated Boolean @default(false)
  order       Int       @default(0)
  chapterId   String
  createdBy   String?
  updatedBy   String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  chapter     Chapter   @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@map("viva_qas")
}

// Enhanced Subscription Plan Model
model SubscriptionPlan {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?   @db.Text
  price       Decimal   @db.Decimal(10, 2)
  duration    Int       // Duration in days
  discountPercentage Decimal? @db.Decimal(5, 2) // Optional discount percentage
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relationships
  userSubscriptions UserSubscription[]
  planSubjects      PlanSubject[]

  @@map("subscription_plans")
}

// Plan-Subject Relationship (Many-to-Many)
model PlanSubject {
  id        String   @id @default(cuid())
  planId    String
  subjectId String
  createdAt DateTime @default(now())

  plan      SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  subject   Subject          @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([planId, subjectId])
  @@map("plan_subjects")
}

// Individual Subject Pricing
model SubjectPricing {
  id        String   @id @default(cuid())
  subjectId String   @unique
  price     Decimal  @db.Decimal(10, 2)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  subject   Subject  @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@map("subject_pricing")
}

// User Model (Students)
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String?  // Optional for manual user creation by admin
  name      String
  phone     String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  subscriptions     UserSubscription[]
  subjectAccess     UserSubjectAccess[]

  @@map("users")
}

// User Subscription Model (linking users to subscription plans)
model UserSubscription {
  id        String   @id @default(cuid())
  userId    String
  planId    String
  startDate DateTime @default(now())
  endDate   DateTime
  isActive  Boolean  @default(true)
  createdBy String?  // Admin who created this subscription
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan      SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)

  @@unique([userId, planId])
  @@map("user_subscriptions")
}

// User-Subject Access Permissions (for individual subject purchases)
model UserSubjectAccess {
  id        String   @id @default(cuid())
  userId    String
  subjectId String
  accessType String  // "PURCHASED", "GRANTED", "SUBSCRIPTION"
  startDate DateTime @default(now())
  endDate   DateTime? // Null for permanent access
  isActive  Boolean  @default(true)
  createdBy String?  // Admin who granted this access
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  subject   Subject  @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([userId, subjectId])
  @@map("user_subject_access")
}
