// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Admin User Model
model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

// Domain Model (e.g., IT, Govt Job, Web Dev)
model Domain {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?   @db.Text
  isActive    Boolean   @default(true)
  order       Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  subjects    Subject[]

  @@map("domains")
}

// Subject Model (under each domain)
model Subject {
  id          String    @id @default(cuid())
  name        String
  description String?   @db.Text
  isActive    Boolean   @default(true)
  order       Int       @default(0)
  domainId    String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  domain      Domain    @relation(fields: [domainId], references: [id], onDelete: Cascade)
  chapters    Chapter[]

  @@unique([domainId, name])
  @@map("subjects")
}

// Chapter Model (under each subject)
model Chapter {
  id          String    @id @default(cuid())
  title       String
  description String?   @db.Text
  content     String?   @db.LongText
  isActive    Boolean   @default(true)
  isDraft     Boolean   @default(false)
  isAIGenerated Boolean @default(false)
  order       Int       @default(0)
  subjectId   String
  createdBy   String?
  updatedBy   String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  subject     Subject   @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  mcqs        MCQ[]
  vivaQAs     VivaQA[]

  @@unique([subjectId, title])
  @@map("chapters")
}

// MCQ Model (Multiple Choice Questions)
model MCQ {
  id          String    @id @default(cuid())
  question    String    @db.Text
  optionA     String    @db.Text
  optionB     String    @db.Text
  optionC     String    @db.Text
  optionD     String    @db.Text
  correctAnswer String  // A, B, C, or D
  explanation String?   @db.Text
  isActive    Boolean   @default(true)
  isDraft     Boolean   @default(false)
  isAIGenerated Boolean @default(false)
  order       Int       @default(0)
  chapterId   String
  createdBy   String?
  updatedBy   String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  chapter     Chapter   @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@map("mcqs")
}

// Viva Q&A Model (Question and Answer pairs)
model VivaQA {
  id          String    @id @default(cuid())
  question    String    @db.Text
  answer      String    @db.Text
  isActive    Boolean   @default(true)
  isDraft     Boolean   @default(false)
  isAIGenerated Boolean @default(false)
  order       Int       @default(0)
  chapterId   String
  createdBy   String?
  updatedBy   String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  chapter     Chapter   @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@map("viva_qas")
}

// Subscription Plan Model
model SubscriptionPlan {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?   @db.Text
  price       Decimal   @db.Decimal(10, 2)
  duration    Int       // Duration in days
  features    Json?     // JSON array of features
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  userSubscriptions UserSubscription[]

  @@map("subscription_plans")
}

// User Model (Students)
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  phone     String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  subscriptions UserSubscription[]

  @@map("users")
}

// User Subscription Model (linking users to subscription plans)
model UserSubscription {
  id        String   @id @default(cuid())
  userId    String
  planId    String
  startDate DateTime @default(now())
  endDate   DateTime
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan      SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)

  @@unique([userId, planId])
  @@map("user_subscriptions")
}
