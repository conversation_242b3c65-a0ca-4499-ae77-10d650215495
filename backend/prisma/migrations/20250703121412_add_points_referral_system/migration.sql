/*
  Warnings:

  - A unique constraint covering the columns `[referralCode]` on the table `users` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `finalPrice` to the `user_subject_access` table without a default value. This is not possible if the table is not empty.
  - Added the required column `finalPrice` to the `user_subscriptions` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `user_achievements` ADD COLUMN `pointsAwarded` INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `user_subject_access` ADD COLUMN `pointsUsed` INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `user_subscriptions` ADD COLUMN `pointsUsed` INTEGER NOT NULL DEFAULT 0;

-- Add finalPrice column with default value first, then update existing records
ALTER TABLE `user_subject_access` ADD COLUMN `finalPrice` DOUBLE NOT NULL DEFAULT 0;
ALTER TABLE `user_subscriptions` ADD COLUMN `finalPrice` DOUBLE NOT NULL DEFAULT 0;

-- Update existing records to set finalPrice based on subject pricing and subscription plan pricing
UPDATE `user_subject_access` usa
JOIN `subject_pricing` sp ON usa.subjectId = sp.subjectId
SET usa.finalPrice = sp.price
WHERE usa.finalPrice = 0;

UPDATE `user_subscriptions` us
JOIN `subscription_plans` sp ON us.planId = sp.id
SET us.finalPrice = sp.price
WHERE us.finalPrice = 0;

-- AlterTable
ALTER TABLE `users` ADD COLUMN `lastLoginDate` DATETIME(3) NULL,
    ADD COLUMN `loginStreak` INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN `pointBalance` INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN `referralCode` VARCHAR(191) NULL,
    ADD COLUMN `referredBy` VARCHAR(191) NULL,
    ADD COLUMN `totalPointsEarned` INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE `point_transactions` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `category` VARCHAR(191) NOT NULL,
    `amount` INTEGER NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `metadata` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `referrals` (
    `id` VARCHAR(191) NOT NULL,
    `referrerUserId` VARCHAR(191) NOT NULL,
    `referredUserId` VARCHAR(191) NOT NULL,
    `referralCode` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'pending',
    `signupReward` INTEGER NOT NULL DEFAULT 10,
    `purchaseReward` DOUBLE NULL,
    `isFirstPurchase` BOOLEAN NOT NULL DEFAULT false,
    `rewardedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `referrals_referrerUserId_referredUserId_key`(`referrerUserId`, `referredUserId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `users_referralCode_key` ON `users`(`referralCode`);

-- AddForeignKey
ALTER TABLE `point_transactions` ADD CONSTRAINT `point_transactions_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `referrals` ADD CONSTRAINT `referrals_referrerUserId_fkey` FOREIGN KEY (`referrerUserId`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `referrals` ADD CONSTRAINT `referrals_referredUserId_fkey` FOREIGN KEY (`referredUserId`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
