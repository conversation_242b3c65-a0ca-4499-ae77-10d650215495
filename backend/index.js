import Fastify from 'fastify'
import cors from '@fastify/cors'
import jwt from '@fastify/jwt'
import routes from './src/routes/index.js'
import queueManager from './src/services/queueManager.js'
import { prisma } from './src/lib/prisma.js'

const fastify = Fastify({
  logger: true
})

// Register CORS
await fastify.register(cors, {
  origin: true,
  credentials: true
})

// Register JWT
await fastify.register(jwt, {
  secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production'
})

// Register Prisma
fastify.decorate('prisma', prisma)

// Register routes
await fastify.register(routes, { prefix: '/api' })

// Health check route
fastify.get('/health', async (request, reply) => {
  const queueHealth = await queueManager.healthCheck()

  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    queue: queueHealth
  }
})

// Initialize queue manager
async function startServer() {
  try {
    // Initialize queue manager
    console.log('Initializing queue manager...')
    await queueManager.initialize()
    console.log('Queue manager initialized successfully')

    // Start the server
    const address = await fastify.listen({
      port: process.env.PORT || 3000,
      host: '0.0.0.0'
    })

    console.log(`Server listening on ${address}`)

    // Graceful shutdown
    const gracefulShutdown = async (signal) => {
      console.log(`Received ${signal}, shutting down gracefully...`)

      try {
        await queueManager.shutdown()
        await fastify.close()
        console.log('Server shut down successfully')
        process.exit(0)
      } catch (error) {
        console.error('Error during shutdown:', error)
        process.exit(1)
      }
    }

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))

  } catch (error) {
    console.error('Failed to start server:', error)
    process.exit(1)
  }
}

startServer()
