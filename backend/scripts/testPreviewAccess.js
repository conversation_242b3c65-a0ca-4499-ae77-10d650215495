import { AccessControlService } from '../src/services/accessControlService.js'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function testPreviewAccess() {
  try {
    console.log('🧪 Testing Preview Access for Non-Subscribed Users...\n')

    // Create a user without any subscriptions or access
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Preview Test User',
        phone: '+1234567899',
        password: await bcrypt.hash('password123', 10)
      }
    })

    console.log(`👤 Created test user: ${testUser.email}`)

    // Get test subject and chapters
    const subject = await prisma.subject.findFirst({
      include: {
        chapters: {
          orderBy: { createdAt: 'asc' },
          take: 5 // Get up to 5 chapters for testing
        }
      }
    })

    if (!subject || subject.chapters.length === 0) {
      console.log('❌ No subject with chapters found for testing.')
      return
    }

    console.log(`📚 Testing with subject: ${subject.name} (${subject.chapters.length} chapters)\n`)

    // Test 1: Subject access (should be denied)
    console.log('🔍 Test 1: Subject Access (should be denied)')
    console.log('=' .repeat(50))

    const subjectAccess = await AccessControlService.checkSubjectAccess(testUser.id, subject.id)
    const subjectStatus = subjectAccess.hasAccess ? '✅' : '❌'
    console.log(`${subjectStatus} Subject Access: ${subjectAccess.accessType || 'NONE'} (${subjectAccess.source || 'none'})`)

    // Test 2: Chapter access (first 2 should have preview, rest should be denied)
    console.log('\n🔍 Test 2: Chapter Access (first 2 should have preview)')
    console.log('=' .repeat(50))

    for (let i = 0; i < subject.chapters.length; i++) {
      const chapter = subject.chapters[i]
      const chapterAccess = await AccessControlService.checkChapterAccess(testUser.id, chapter.id)
      
      const status = chapterAccess.hasAccess ? '✅' : '❌'
      const preview = chapterAccess.isPreview ? ' (PREVIEW)' : ''
      const type = chapterAccess.accessType || 'NONE'
      
      console.log(`  ${status} Chapter ${i + 1}: ${chapter.title} - ${type}${preview}`)
    }

    // Test 3: MCQ and Viva access (should all be denied)
    console.log('\n🔍 Test 3: MCQ and Viva Access (should all be denied)')
    console.log('=' .repeat(50))

    for (let i = 0; i < Math.min(subject.chapters.length, 3); i++) {
      const chapter = subject.chapters[i]
      
      const mcqAccess = await AccessControlService.checkMCQAccess(testUser.id, chapter.id)
      const vivaAccess = await AccessControlService.checkVivaAccess(testUser.id, chapter.id)
      
      const mcqStatus = mcqAccess.hasAccess ? '✅' : '❌'
      const vivaStatus = vivaAccess.hasAccess ? '✅' : '❌'
      
      console.log(`  Chapter ${i + 1}: ${chapter.title}`)
      console.log(`    ${mcqStatus} MCQ Access: ${mcqAccess.accessType || 'NONE'}`)
      console.log(`    ${vivaStatus} Viva Access: ${vivaAccess.accessType || 'NONE'}`)
    }

    // Test 4: Chapter filtering (should show preview for first 2, denied for rest)
    console.log('\n🔍 Test 4: Chapter Filtering (preview for first 2)')
    console.log('=' .repeat(50))

    const filteredChapters = await AccessControlService.filterChaptersByAccess(
      testUser.id, 
      subject.chapters, 
      subject.id
    )

    filteredChapters.forEach((chapter, index) => {
      const status = chapter.hasAccess ? '✅' : '❌'
      const preview = chapter.isPreview ? ' (PREVIEW)' : ''
      const type = chapter.accessType || 'NONE'
      
      console.log(`  ${status} Chapter ${index + 1}: ${chapter.title} - ${type}${preview}`)
    })

    // Test 5: User accessible subjects (should be empty)
    console.log('\n🔍 Test 5: User Accessible Subjects (should be empty)')
    console.log('=' .repeat(50))

    const accessibleSubjects = await AccessControlService.getUserAccessibleSubjects(testUser.id)
    
    if (accessibleSubjects.length === 0) {
      console.log('  ✅ No accessible subjects (as expected)')
    } else {
      console.log('  ❌ Unexpected accessible subjects found:')
      accessibleSubjects.forEach(subject => {
        console.log(`    - ${subject.name} (${subject.accessType} via ${subject.source})`)
      })
    }

    console.log('\n🎉 Preview Access Testing Completed!')
    console.log('\n📋 Summary:')
    console.log('- Subject access: Correctly denied')
    console.log('- Chapter access: First 2 chapters have preview access, rest denied')
    console.log('- MCQ/Viva access: All correctly denied (no preview for interactive content)')
    console.log('- Chapter filtering: Correctly shows preview status')
    console.log('- User accessible subjects: Correctly empty')

    // Clean up test user
    await prisma.user.delete({
      where: { id: testUser.id }
    })
    console.log('\n🧹 Cleaned up test user')

  } catch (error) {
    console.error('❌ Error testing preview access:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the test
testPreviewAccess()
  .catch((error) => {
    console.error('❌ Testing failed:', error)
    process.exit(1)
  })
