import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function seedSubscriptionData() {
  try {
    console.log('🌱 Starting subscription system seeding...')

    // Get existing domains and subjects
    const domains = await prisma.domain.findMany({
      include: {
        subjects: true
      }
    })

    if (domains.length === 0) {
      console.log('❌ No domains found. Please run the main seeder first.')
      return
    }

    console.log(`📚 Found ${domains.length} domains`)

    // Create subscription plans
    const plans = [
      {
        name: 'Basic Plan',
        description: 'Access to 2 subjects of your choice',
        price: 29.99,
        duration: 30, // 30 days
        discountPercentage: 0
      },
      {
        name: 'Standard Plan',
        description: 'Access to 5 subjects with 10% discount',
        price: 99.99,
        duration: 90, // 90 days
        discountPercentage: 10
      },
      {
        name: 'Premium Plan',
        description: 'Access to all subjects with 20% discount',
        price: 199.99,
        duration: 365, // 1 year
        discountPercentage: 20
      }
    ]

    console.log('📋 Creating subscription plans...')

    const createdPlans = []
    for (const planData of plans) {
      const plan = await prisma.subscriptionPlan.create({
        data: planData
      })
      createdPlans.push(plan)
      console.log(`✅ Created plan: ${plan.name}`)
    }

    // Assign subjects to plans
    console.log('🔗 Assigning subjects to plans...')

    // Basic Plan - first 2 subjects
    const basicSubjects = domains.flatMap(d => d.subjects).slice(0, 2)
    if (basicSubjects.length > 0) {
      await prisma.planSubject.createMany({
        data: basicSubjects.map(subject => ({
          planId: createdPlans[0].id,
          subjectId: subject.id
        }))
      })
      console.log(`✅ Assigned ${basicSubjects.length} subjects to Basic Plan`)
    }

    // Standard Plan - first 5 subjects
    const standardSubjects = domains.flatMap(d => d.subjects).slice(0, 5)
    if (standardSubjects.length > 0) {
      await prisma.planSubject.createMany({
        data: standardSubjects.map(subject => ({
          planId: createdPlans[1].id,
          subjectId: subject.id
        }))
      })
      console.log(`✅ Assigned ${standardSubjects.length} subjects to Standard Plan`)
    }

    // Premium Plan - all subjects
    const allSubjects = domains.flatMap(d => d.subjects)
    if (allSubjects.length > 0) {
      await prisma.planSubject.createMany({
        data: allSubjects.map(subject => ({
          planId: createdPlans[2].id,
          subjectId: subject.id
        }))
      })
      console.log(`✅ Assigned ${allSubjects.length} subjects to Premium Plan`)
    }

    // Create individual subject pricing
    console.log('💰 Creating individual subject pricing...')

    const subjectPricing = []
    for (const domain of domains) {
      for (const subject of domain.subjects) {
        const pricing = await prisma.subjectPricing.create({
          data: {
            subjectId: subject.id,
            price: Math.floor(Math.random() * 20) + 10 // Random price between $10-$30
          }
        })
        subjectPricing.push(pricing)
      }
    }
    console.log(`✅ Created pricing for ${subjectPricing.length} subjects`)

    // Create test users
    console.log('👥 Creating test users...')

    const testUsers = [
      {
        email: '<EMAIL>',
        name: 'John Doe',
        phone: '+1234567890',
        password: await bcrypt.hash('password123', 10)
      },
      {
        email: '<EMAIL>',
        name: 'Jane Smith',
        phone: '+1234567891',
        password: await bcrypt.hash('password123', 10)
      },
      {
        email: '<EMAIL>',
        name: 'Bob Johnson',
        phone: '+1234567892',
        password: await bcrypt.hash('password123', 10)
      }
    ]

    const createdUsers = []
    for (const userData of testUsers) {
      const user = await prisma.user.create({
        data: userData
      })
      createdUsers.push(user)
      console.log(`✅ Created user: ${user.email}`)
    }

    // Create test subscriptions
    console.log('📝 Creating test subscriptions...')

    // User 1 - Basic Plan
    const subscription1 = await prisma.userSubscription.create({
      data: {
        userId: createdUsers[0].id,
        planId: createdPlans[0].id,
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        isActive: true
      }
    })
    console.log(`✅ Created subscription for ${createdUsers[0].email} - Basic Plan`)

    // User 2 - Standard Plan
    const subscription2 = await prisma.userSubscription.create({
      data: {
        userId: createdUsers[1].id,
        planId: createdPlans[1].id,
        startDate: new Date(),
        endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        isActive: true
      }
    })
    console.log(`✅ Created subscription for ${createdUsers[1].email} - Standard Plan`)

    // Create individual subject access for User 3
    if (allSubjects.length > 0) {
      const subjectAccess = await prisma.userSubjectAccess.create({
        data: {
          userId: createdUsers[2].id,
          subjectId: allSubjects[0].id,
          accessType: 'PURCHASED',
          isActive: true
        }
      })
      console.log(`✅ Created individual subject access for ${createdUsers[2].email}`)
    }

    console.log('🎉 Subscription system seeding completed successfully!')
    console.log('\n📊 Summary:')
    console.log(`- Created ${createdPlans.length} subscription plans`)
    console.log(`- Created pricing for ${subjectPricing.length} subjects`)
    console.log(`- Created ${createdUsers.length} test users`)
    console.log(`- Created 2 subscriptions and 1 individual access`)
    console.log('\n🔑 Test User Credentials:')
    console.log('- <EMAIL> / password123 (Basic Plan)')
    console.log('- <EMAIL> / password123 (Standard Plan)')
    console.log('- <EMAIL> / password123 (Individual Access)')

  } catch (error) {
    console.error('❌ Error seeding subscription data:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeder
seedSubscriptionData()
  .catch((error) => {
    console.error('❌ Seeding failed:', error)
    process.exit(1)
  })
