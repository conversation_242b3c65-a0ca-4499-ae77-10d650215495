import { AccessControlService } from '../src/services/accessControlService.js'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testAccessControl() {
  try {
    console.log('🧪 Testing Access Control System...\n')

    // Get test data
    const users = await prisma.user.findMany({
      select: { id: true, email: true }
    })

    const subjects = await prisma.subject.findMany({
      include: {
        chapters: {
          select: { id: true, title: true },
          take: 3 // Get first 3 chapters for testing
        }
      }
    })

    if (users.length === 0 || subjects.length === 0) {
      console.log('❌ No test data found. Please run the seeders first.')
      return
    }

    console.log(`📊 Found ${users.length} users and ${subjects.length} subjects\n`)

    // Test 1: Check subject access for each user
    console.log('🔍 Test 1: Subject Access Control')
    console.log('=' .repeat(50))

    for (const user of users) {
      console.log(`\n👤 User: ${user.email}`)
      
      for (const subject of subjects) {
        const access = await AccessControlService.checkSubjectAccess(user.id, subject.id)
        const status = access.hasAccess ? '✅' : '❌'
        const type = access.accessType || 'NONE'
        const source = access.source || 'none'
        
        console.log(`  ${status} ${subject.name}: ${type} (${source})`)
      }
    }

    // Test 2: Check chapter access (including preview)
    console.log('\n\n🔍 Test 2: Chapter Access Control (including preview)')
    console.log('=' .repeat(50))

    const testSubject = subjects[0]
    if (testSubject.chapters.length > 0) {
      for (const user of users) {
        console.log(`\n👤 User: ${user.email} - Subject: ${testSubject.name}`)
        
        for (let i = 0; i < Math.min(testSubject.chapters.length, 3); i++) {
          const chapter = testSubject.chapters[i]
          const access = await AccessControlService.checkChapterAccess(user.id, chapter.id)
          const status = access.hasAccess ? '✅' : '❌'
          const preview = access.isPreview ? ' (PREVIEW)' : ''
          const type = access.accessType || 'NONE'
          
          console.log(`  ${status} Chapter ${i + 1}: ${chapter.title} - ${type}${preview}`)
        }
      }
    }

    // Test 3: Check MCQ and Viva access
    console.log('\n\n🔍 Test 3: MCQ and Viva Access Control')
    console.log('=' .repeat(50))

    if (testSubject.chapters.length > 0) {
      const testChapter = testSubject.chapters[0]
      
      for (const user of users) {
        console.log(`\n👤 User: ${user.email} - Chapter: ${testChapter.title}`)
        
        const mcqAccess = await AccessControlService.checkMCQAccess(user.id, testChapter.id)
        const vivaAccess = await AccessControlService.checkVivaAccess(user.id, testChapter.id)
        
        const mcqStatus = mcqAccess.hasAccess ? '✅' : '❌'
        const vivaStatus = vivaAccess.hasAccess ? '✅' : '❌'
        
        console.log(`  ${mcqStatus} MCQ Access: ${mcqAccess.accessType || 'NONE'}`)
        console.log(`  ${vivaStatus} Viva Access: ${vivaAccess.accessType || 'NONE'}`)
      }
    }

    // Test 4: Get user accessible subjects
    console.log('\n\n🔍 Test 4: User Accessible Subjects Summary')
    console.log('=' .repeat(50))

    for (const user of users) {
      console.log(`\n👤 User: ${user.email}`)
      
      const accessibleSubjects = await AccessControlService.getUserAccessibleSubjects(user.id)
      
      if (accessibleSubjects.length === 0) {
        console.log('  ❌ No accessible subjects')
      } else {
        accessibleSubjects.forEach(subject => {
          console.log(`  ✅ ${subject.name} (${subject.accessType} via ${subject.source})`)
        })
      }
    }

    // Test 5: Filter chapters by access
    console.log('\n\n🔍 Test 5: Chapter Filtering by Access')
    console.log('=' .repeat(50))

    if (testSubject.chapters.length > 0) {
      for (const user of users) {
        console.log(`\n👤 User: ${user.email} - Subject: ${testSubject.name}`)
        
        const filteredChapters = await AccessControlService.filterChaptersByAccess(
          user.id, 
          testSubject.chapters, 
          testSubject.id
        )
        
        filteredChapters.forEach((chapter, index) => {
          const status = chapter.hasAccess ? '✅' : '❌'
          const preview = chapter.isPreview ? ' (PREVIEW)' : ''
          console.log(`  ${status} Chapter ${index + 1}: ${chapter.title}${preview}`)
        })
      }
    }

    console.log('\n🎉 Access Control Testing Completed!')

  } catch (error) {
    console.error('❌ Error testing access control:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the test
testAccessControl()
  .catch((error) => {
    console.error('❌ Testing failed:', error)
    process.exit(1)
  })
