import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedSliderData() {
  try {
    console.log('Seeding slider data...')

    // Create sample slider images
    const sliderImages = [
      {
        title: 'Master Computer Science',
        description: 'Comprehensive courses covering algorithms, data structures, and programming fundamentals',
        imageUrl: '/uploads/slider/slider_sample_1.jpg',
        linkUrl: '/computer-science',
        isActive: true,
        order: 0
      },
      {
        title: 'Excel in Mathematics',
        description: 'From basic arithmetic to advanced calculus and statistics',
        imageUrl: '/uploads/slider/slider_sample_2.jpg',
        linkUrl: '/mathematics',
        isActive: true,
        order: 1
      },
      {
        title: 'Explore Physics',
        description: 'Understand the fundamental laws of nature through interactive learning',
        imageUrl: '/uploads/slider/slider_sample_3.jpg',
        linkUrl: '/physics',
        isActive: true,
        order: 2
      },
      {
        title: 'Learn Chemistry',
        description: 'Discover the building blocks of matter and chemical reactions',
        imageUrl: '/uploads/slider/slider_sample_4.jpg',
        linkUrl: '/chemistry',
        isActive: false,
        order: 3
      }
    ]

    // Clear existing slider images
    await prisma.sliderImage.deleteMany()
    console.log('Cleared existing slider images')

    // Create new slider images
    for (const sliderData of sliderImages) {
      const slider = await prisma.sliderImage.create({
        data: sliderData
      })
      console.log(`Created slider: ${slider.title}`)
    }

    // Update some subjects to be featured and add sample image URLs
    const subjects = await prisma.subject.findMany({
      take: 4,
      orderBy: { createdAt: 'asc' }
    })

    if (subjects.length > 0) {
      // Update first 3 subjects to be featured with sample images
      for (let i = 0; i < Math.min(3, subjects.length); i++) {
        const subject = subjects[i]
        await prisma.subject.update({
          where: { id: subject.id },
          data: {
            isFeatured: true,
            imageUrl: `/uploads/subjects/subject_sample_${i + 1}.jpg`,
            slug: subject.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
          }
        })
        console.log(`Updated subject ${subject.name} to be featured with image and slug`)
      }

      // Update remaining subjects with slugs
      for (let i = 3; i < subjects.length; i++) {
        const subject = subjects[i]
        await prisma.subject.update({
          where: { id: subject.id },
          data: {
            slug: subject.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
          }
        })
        console.log(`Updated subject ${subject.name} with slug`)
      }
    }

    console.log('Slider data seeded successfully!')

  } catch (error) {
    console.error('Error seeding slider data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedSliderData()
