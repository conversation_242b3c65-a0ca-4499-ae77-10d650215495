import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedBasicData() {
  try {
    console.log('🌱 Starting basic data seeding...')

    // Create domains
    const domains = [
      {
        name: 'Computer Science',
        description: 'Programming, algorithms, and software development',
        order: 0
      },
      {
        name: 'Mathematics',
        description: 'Pure and applied mathematics',
        order: 1
      },
      {
        name: 'Physics',
        description: 'Classical and modern physics',
        order: 2
      }
    ]

    console.log('📚 Creating domains...')
    const createdDomains = []
    for (const domainData of domains) {
      const domain = await prisma.domain.create({
        data: domainData
      })
      createdDomains.push(domain)
      console.log(`✅ Created domain: ${domain.name}`)
    }

    // Create subjects
    const subjects = [
      // Computer Science subjects
      {
        name: 'Data Structures',
        slug: 'data-structures',
        description: 'Learn about arrays, linked lists, trees, and graphs',
        domainId: createdDomains[0].id,
        order: 0
      },
      {
        name: 'Algorithms',
        slug: 'algorithms',
        description: 'Sorting, searching, and optimization algorithms',
        domainId: createdDomains[0].id,
        order: 1
      },
      // Mathematics subjects
      {
        name: '<PERSON><PERSON>',
        slug: 'calculus',
        description: 'Differential and integral calculus',
        domainId: createdDomains[1].id,
        order: 0
      },
      {
        name: 'Linear Algebra',
        slug: 'linear-algebra',
        description: 'Vectors, matrices, and linear transformations',
        domainId: createdDomains[1].id,
        order: 1
      },
      // Physics subjects
      {
        name: 'Classical Mechanics',
        slug: 'classical-mechanics',
        description: 'Newton\'s laws and motion dynamics',
        domainId: createdDomains[2].id,
        order: 0
      }
    ]

    console.log('📖 Creating subjects...')
    const createdSubjects = []
    for (const subjectData of subjects) {
      const subject = await prisma.subject.create({
        data: subjectData
      })
      createdSubjects.push(subject)
      console.log(`✅ Created subject: ${subject.name}`)
    }

    // Create chapters for each subject
    console.log('📝 Creating chapters...')
    const createdChapters = []
    
    for (const subject of createdSubjects) {
      const chapters = [
        {
          title: `Introduction to ${subject.name}`,
          description: `Basic concepts and fundamentals of ${subject.name}`,
          content: `This chapter covers the fundamental concepts of ${subject.name}. Students will learn the basic principles and terminology used throughout the course.`,
          subjectId: subject.id,
          order: 0
        },
        {
          title: `Advanced ${subject.name}`,
          description: `Advanced topics and applications in ${subject.name}`,
          content: `This chapter delves into more complex topics in ${subject.name}, building upon the foundation established in the introduction.`,
          subjectId: subject.id,
          order: 1
        },
        {
          title: `${subject.name} Applications`,
          description: `Real-world applications and case studies`,
          content: `This chapter explores practical applications of ${subject.name} concepts in real-world scenarios and industry use cases.`,
          subjectId: subject.id,
          order: 2
        }
      ]

      for (const chapterData of chapters) {
        const chapter = await prisma.chapter.create({
          data: chapterData
        })
        createdChapters.push(chapter)
        console.log(`✅ Created chapter: ${chapter.title}`)
      }
    }

    // Create sample MCQs
    console.log('❓ Creating sample MCQs...')
    let mcqCount = 0
    
    for (const chapter of createdChapters.slice(0, 5)) { // Only first 5 chapters
      const mcqs = [
        {
          question: `What is the main concept covered in ${chapter.title}?`,
          optionA: 'Basic principles',
          optionB: 'Advanced techniques',
          optionC: 'Historical background',
          optionD: 'Future trends',
          correctAnswer: 'A',
          explanation: 'The main concept focuses on basic principles and fundamentals.',
          chapterId: chapter.id,
          order: 0
        },
        {
          question: `Which approach is most effective for learning ${chapter.title}?`,
          optionA: 'Memorization only',
          optionB: 'Practice and understanding',
          optionC: 'Reading textbooks',
          optionD: 'Watching videos',
          correctAnswer: 'B',
          explanation: 'Practice combined with understanding leads to better learning outcomes.',
          chapterId: chapter.id,
          order: 1
        }
      ]

      for (const mcqData of mcqs) {
        await prisma.mCQ.create({
          data: mcqData
        })
        mcqCount++
      }
    }
    console.log(`✅ Created ${mcqCount} MCQs`)

    // Create sample Viva Q&As
    console.log('💬 Creating sample Viva Q&As...')
    let vivaCount = 0
    
    for (const chapter of createdChapters.slice(0, 3)) { // Only first 3 chapters
      const vivas = [
        {
          question: `Explain the key concepts covered in ${chapter.title}.`,
          answer: `The key concepts in ${chapter.title} include fundamental principles, practical applications, and theoretical foundations. Students should understand how these concepts relate to the broader subject area and their real-world implications.`,
          chapterId: chapter.id,
          order: 0
        },
        {
          question: `What are the practical applications of ${chapter.title}?`,
          answer: `The practical applications include industry use cases, problem-solving techniques, and real-world implementations. These applications demonstrate the relevance and importance of the theoretical concepts in professional settings.`,
          chapterId: chapter.id,
          order: 1
        }
      ]

      for (const vivaData of vivas) {
        await prisma.vivaQA.create({
          data: vivaData
        })
        vivaCount++
      }
    }
    console.log(`✅ Created ${vivaCount} Viva Q&As`)

    console.log('🎉 Basic data seeding completed successfully!')
    console.log('\n📊 Summary:')
    console.log(`- Created ${createdDomains.length} domains`)
    console.log(`- Created ${createdSubjects.length} subjects`)
    console.log(`- Created ${createdChapters.length} chapters`)
    console.log(`- Created ${mcqCount} MCQs`)
    console.log(`- Created ${vivaCount} Viva Q&As`)

  } catch (error) {
    console.error('❌ Error seeding basic data:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeder
seedBasicData()
  .catch((error) => {
    console.error('❌ Seeding failed:', error)
    process.exit(1)
  })
