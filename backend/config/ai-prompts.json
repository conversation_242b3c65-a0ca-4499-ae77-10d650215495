{"chapter_generation": {"name": "Chapter Generation", "description": "Generate chapter titles for a given domain and subject", "prompt": "Generate {count} educational chapter titles for the subject '{subject}' in the domain '{domain}'. Each chapter should be comprehensive and cover important topics. Return the response as a JSON array of objects with 'title' and 'description' fields. Example format: [{\"title\": \"Chapter Title\", \"description\": \"Brief description of what this chapter covers\"}]. Only return valid JSON, no additional text.", "variables": ["count", "subject", "domain"]}, "chapter_content": {"name": "Chapter Content Generation", "description": "Generate detailed content for a specific chapter", "prompt": "Generate comprehensive educational content for the chapter titled '{title}' in the subject '{subject}' under the domain '{domain}'. The content should be detailed, informative, and suitable for learning. Include key concepts, explanations, and examples. Return the response as a JSON object with a 'content' field containing the chapter content. Example format: {\"content\": \"Detailed chapter content here...\"}. Only return valid JSON, no additional text.", "variables": ["title", "subject", "domain"]}, "mcq_generation": {"name": "MCQ Generation", "description": "Generate multiple choice questions for a chapter", "prompt": "Generate {count} multiple choice questions (MCQs) for the chapter '{chapter}' in the subject '{subject}' under the domain '{domain}'. Each MCQ should have 4 options (A, B, C, D) with one correct answer and an explanation. If reference sources are provided, include them appropriately: {reference}. Return the response as a JSON object with an 'mcqs' field containing an array of MCQ objects. Example format: {\"mcqs\": [{\"question\": \"Question text?\", \"optionA\": \"Option A\", \"optionB\": \"Option B\", \"optionC\": \"Option C\", \"optionD\": \"Option D\", \"correctAnswer\": \"A\", \"explanation\": \"Explanation of why A is correct\", \"reference\": \"Reference source if provided\"}]}. IMPORTANT: Use exactly this format with 'mcqs' as the root field and 'optionA', 'optionB', 'optionC', 'optionD', 'correctAnswer', 'reference' as the field names. Only return valid JSON, no additional text.", "variables": ["count", "chapter", "subject", "domain", "reference"]}, "viva_generation": {"name": "Viva Q&A Generation", "description": "Generate viva/interview questions and answers for a chapter", "prompt": "Generate {count} viva/interview style questions and detailed answers for the chapter '{chapter}' in the subject '{subject}' under the domain '{domain}'. Questions should be comprehensive and answers should be detailed and informative. If reference sources are provided, include them appropriately: {reference}. Return the response as a JSON object with a 'viva' field containing an array of Q&A objects. Example format: {\"viva\": [{\"question\": \"Interview question?\", \"answer\": \"Detailed answer explaining the concept thoroughly\", \"reference\": \"Reference source if provided\"}]}. Only return valid JSON, no additional text.", "variables": ["count", "chapter", "subject", "domain", "reference"]}, "bulk_content_generation": {"name": "Bulk Content Generation", "description": "Generate complete content including chapters, MCQs, and viva Q&A", "prompt": "Generate comprehensive educational content for the subject '{subject}' in the domain '{domain}'. Create {chapter_count} chapters, and for each chapter generate {mcq_count} MCQs and {viva_count} viva questions. Return the response as a JSON object with the following structure: {\"chapters\": [{\"title\": \"Chapter Title\", \"description\": \"Chapter description\", \"content\": \"Detailed content\", \"mcqs\": [{\"question\": \"Question?\", \"optionA\": \"A\", \"optionB\": \"B\", \"optionC\": \"C\", \"optionD\": \"D\", \"correctAnswer\": \"A\", \"explanation\": \"Explanation\"}], \"viva\": [{\"question\": \"Question?\", \"answer\": \"Answer\"}]}]}. Only return valid JSON, no additional text.", "variables": ["subject", "domain", "chapter_count", "mcq_count", "viva_count"]}}