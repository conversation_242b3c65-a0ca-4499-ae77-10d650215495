import { aiGenerationWorker, aiGenerationQueue } from './jobQueue.js'

class QueueManager {
  constructor() {
    this.workers = []
    this.queues = []
    this.isInitialized = false
  }

  async initialize() {
    if (this.isInitialized) {
      console.log('Queue manager already initialized')
      return
    }

    try {
      console.log('Initializing queue manager...')
      
      // Register workers
      this.workers.push(aiGenerationWorker)
      
      // Register queues
      this.queues.push(aiGenerationQueue)
      
      // Set up global error handlers
      this.setupErrorHandlers()
      
      this.isInitialized = true
      console.log('Queue manager initialized successfully')
      
      // Log queue stats periodically
      this.startStatsLogging()
      
    } catch (error) {
      console.error('Failed to initialize queue manager:', error)
      throw error
    }
  }

  setupErrorHandlers() {
    // Global error handler for workers
    this.workers.forEach(worker => {
      worker.on('error', (error) => {
        console.error(`Worker error in ${worker.name}:`, error)
      })

      worker.on('stalled', (jobId) => {
        console.warn(`Job ${jobId} stalled in worker ${worker.name}`)
      })
    })

    // Global error handler for queues
    this.queues.forEach(queue => {
      queue.on('error', (error) => {
        console.error(`Queue error in ${queue.name}:`, error)
      })
    })
  }

  startStatsLogging() {
    // Log queue statistics every 5 minutes
    setInterval(async () => {
      try {
        for (const queue of this.queues) {
          const waiting = await queue.getWaiting()
          const active = await queue.getActive()
          const completed = await queue.getCompleted()
          const failed = await queue.getFailed()

          console.log(`Queue ${queue.name} stats:`, {
            waiting: waiting.length,
            active: active.length,
            completed: completed.length,
            failed: failed.length
          })
        }
      } catch (error) {
        console.error('Failed to log queue stats:', error)
      }
    }, 5 * 60 * 1000) // 5 minutes
  }

  async shutdown() {
    if (!this.isInitialized) {
      return
    }

    console.log('Shutting down queue manager...')
    
    try {
      // Close all workers
      await Promise.all(
        this.workers.map(async (worker) => {
          console.log(`Closing worker ${worker.name}...`)
          await worker.close()
        })
      )

      // Close all queues
      await Promise.all(
        this.queues.map(async (queue) => {
          console.log(`Closing queue ${queue.name}...`)
          await queue.close()
        })
      )

      this.isInitialized = false
      console.log('Queue manager shut down successfully')
    } catch (error) {
      console.error('Error during queue manager shutdown:', error)
      throw error
    }
  }

  // Health check for queues
  async healthCheck() {
    const health = {
      status: 'healthy',
      queues: [],
      workers: [],
      timestamp: new Date().toISOString()
    }

    try {
      // Check queue health
      for (const queue of this.queues) {
        const waiting = await queue.getWaiting()
        const active = await queue.getActive()
        const failed = await queue.getFailed()

        health.queues.push({
          name: queue.name,
          waiting: waiting.length,
          active: active.length,
          failed: failed.length,
          status: 'healthy'
        })
      }

      // Check worker health
      for (const worker of this.workers) {
        health.workers.push({
          name: worker.name,
          status: worker.isRunning() ? 'running' : 'stopped'
        })
      }

      // Determine overall health
      const hasFailedJobs = health.queues.some(q => q.failed > 10) // More than 10 failed jobs
      const hasStoppedWorkers = health.workers.some(w => w.status === 'stopped')
      
      if (hasFailedJobs || hasStoppedWorkers) {
        health.status = 'degraded'
      }

    } catch (error) {
      console.error('Health check failed:', error)
      health.status = 'unhealthy'
      health.error = error.message
    }

    return health
  }

  // Get detailed queue information
  async getQueueInfo() {
    const info = {
      queues: [],
      workers: [],
      timestamp: new Date().toISOString()
    }

    try {
      for (const queue of this.queues) {
        const waiting = await queue.getWaiting()
        const active = await queue.getActive()
        const completed = await queue.getCompleted()
        const failed = await queue.getFailed()

        info.queues.push({
          name: queue.name,
          counts: {
            waiting: waiting.length,
            active: active.length,
            completed: completed.length,
            failed: failed.length
          },
          jobs: {
            waiting: waiting.slice(0, 5).map(job => ({
              id: job.id,
              name: job.name,
              data: job.data,
              createdAt: new Date(job.timestamp)
            })),
            active: active.slice(0, 5).map(job => ({
              id: job.id,
              name: job.name,
              progress: job.progress,
              processedOn: job.processedOn ? new Date(job.processedOn) : null
            })),
            failed: failed.slice(0, 5).map(job => ({
              id: job.id,
              name: job.name,
              failedReason: job.failedReason,
              failedOn: job.finishedOn ? new Date(job.finishedOn) : null
            }))
          }
        })
      }

      for (const worker of this.workers) {
        info.workers.push({
          name: worker.name,
          isRunning: worker.isRunning(),
          concurrency: worker.opts.concurrency || 1
        })
      }

    } catch (error) {
      console.error('Failed to get queue info:', error)
      info.error = error.message
    }

    return info
  }

  // Retry failed jobs
  async retryFailedJobs(queueName, limit = 10) {
    try {
      const queue = this.queues.find(q => q.name === queueName)
      if (!queue) {
        throw new Error(`Queue ${queueName} not found`)
      }

      const failedJobs = await queue.getFailed(0, limit - 1)
      let retriedCount = 0

      for (const job of failedJobs) {
        try {
          await job.retry()
          retriedCount++
        } catch (error) {
          console.error(`Failed to retry job ${job.id}:`, error)
        }
      }

      return {
        success: true,
        retriedCount,
        totalFailed: failedJobs.length
      }
    } catch (error) {
      console.error('Failed to retry jobs:', error)
      throw error
    }
  }

  // Clean completed jobs
  async cleanCompletedJobs(queueName, olderThan = 24 * 60 * 60 * 1000) { // 24 hours
    try {
      const queue = this.queues.find(q => q.name === queueName)
      if (!queue) {
        throw new Error(`Queue ${queueName} not found`)
      }

      const cleanedCount = await queue.clean(olderThan, 100, 'completed')
      
      return {
        success: true,
        cleanedCount
      }
    } catch (error) {
      console.error('Failed to clean completed jobs:', error)
      throw error
    }
  }
}

// Singleton instance
const queueManager = new QueueManager()

export default queueManager
