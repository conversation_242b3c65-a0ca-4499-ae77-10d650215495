import { Queue, Worker } from 'bullmq'
import Redis from 'ioredis'
import aiService from './aiService.js'
import { prisma } from '../lib/prisma.js'

// Redis connection
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  lazyConnect: true
})

// Job queues
export const aiGenerationQueue = new Queue('ai-generation', {
  connection: redis,
  defaultJobOptions: {
    removeOnComplete: 50,
    removeOnFail: 20,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000
    }
  }
})

// Job types
export const JOB_TYPES = {
  GENERATE_CHAPTERS: 'generate-chapters',
  GENERATE_CHAPTER_CONTENT: 'generate-chapter-content',
  GENERATE_MCQS: 'generate-mcqs',
  GENERATE_VIVA: 'generate-viva',
  GENERATE_BULK_CONTENT: 'generate-bulk-content'
}

// Job status tracking
export const JOB_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed'
}

// Job processors
const jobProcessors = {
  [JOB_TYPES.GENERATE_CHAPTERS]: async (job) => {
    const { domainId, subjectId, count, options, adminId } = job.data
    
    try {
      // Get domain and subject info
      const domain = await prisma.domain.findUnique({ where: { id: domainId } })
      const subject = await prisma.subject.findUnique({ where: { id: subjectId } })
      
      if (!domain || !subject) {
        throw new Error('Domain or subject not found')
      }

      // Update job progress
      await job.updateProgress(10)

      // Generate chapters using AI
      const result = await aiService.generateChapters(domain.name, subject.name, count, options)
      
      await job.updateProgress(50)

      // Save generated chapters to database
      const chapters = []
      if (result && Array.isArray(result)) {
        for (const chapterData of result) {
          const chapter = await prisma.chapter.create({
            data: {
              title: chapterData.title,
              description: chapterData.description,
              content: chapterData.content || '',
              subjectId: subjectId,
              isActive: false, // Set as draft initially
              isDraft: true,
              isAIGenerated: true,
              order: chapters.length + 1,
              createdBy: adminId
            }
          })
          chapters.push(chapter)
        }
      }

      await job.updateProgress(100)

      return {
        success: true,
        message: `Generated ${chapters.length} chapters successfully`,
        data: { chapters }
      }
    } catch (error) {
      console.error('Chapter generation job failed:', error)
      throw error
    }
  },

  [JOB_TYPES.GENERATE_CHAPTER_CONTENT]: async (job) => {
    const { chapterId, options, adminId } = job.data
    
    try {
      // Get chapter info
      const chapter = await prisma.chapter.findUnique({
        where: { id: chapterId },
        include: {
          subject: {
            include: { domain: true }
          }
        }
      })
      
      if (!chapter) {
        throw new Error('Chapter not found')
      }

      await job.updateProgress(10)

      // Generate content using AI
      const result = await aiService.generateChapterContent(
        chapter.title,
        chapter.subject.name,
        chapter.subject.domain.name,
        options
      )
      
      await job.updateProgress(50)

      // Update chapter with generated content
      const updatedChapter = await prisma.chapter.update({
        where: { id: chapterId },
        data: {
          content: result.content || '',
          updatedBy: adminId
        }
      })

      await job.updateProgress(100)

      return {
        success: true,
        message: 'Chapter content generated successfully',
        data: { chapter: updatedChapter }
      }
    } catch (error) {
      console.error('Chapter content generation job failed:', error)
      throw error
    }
  },

  [JOB_TYPES.GENERATE_MCQS]: async (job) => {
    const { chapterId, count, options, adminId } = job.data
    
    try {
      // Get chapter info
      const chapter = await prisma.chapter.findUnique({
        where: { id: chapterId },
        include: {
          subject: {
            include: { domain: true }
          }
        }
      })
      
      if (!chapter) {
        throw new Error('Chapter not found')
      }

      await job.updateProgress(10)

      // Generate MCQs using AI
      const result = await aiService.generateMCQs(
        chapter.title,
        chapter.subject.name,
        chapter.subject.domain.name,
        count,
        options
      )
      
      await job.updateProgress(50)

      // Save generated MCQs to database
      const mcqs = []
      if (result && result.mcqs && Array.isArray(result.mcqs)) {
        for (const mcqData of result.mcqs) {
          const mcq = await prisma.mCQ.create({
            data: {
              question: mcqData.question,
              optionA: mcqData.optionA,
              optionB: mcqData.optionB,
              optionC: mcqData.optionC,
              optionD: mcqData.optionD,
              correctAnswer: mcqData.correctAnswer,
              explanation: mcqData.explanation || '',
              chapterId: chapterId,
              isActive: false, // Set as draft initially
              isDraft: true,
              isAIGenerated: true,
              order: mcqs.length + 1,
              createdBy: adminId
            }
          })
          mcqs.push(mcq)
        }
      }

      await job.updateProgress(100)

      return {
        success: true,
        message: `Generated ${mcqs.length} MCQs successfully`,
        data: { mcqs }
      }
    } catch (error) {
      console.error('MCQ generation job failed:', error)
      throw error
    }
  },

  [JOB_TYPES.GENERATE_VIVA]: async (job) => {
    const { chapterId, count, options, adminId } = job.data
    
    try {
      // Get chapter info
      const chapter = await prisma.chapter.findUnique({
        where: { id: chapterId },
        include: {
          subject: {
            include: { domain: true }
          }
        }
      })
      
      if (!chapter) {
        throw new Error('Chapter not found')
      }

      await job.updateProgress(10)

      // Generate Viva Q&A using AI
      const result = await aiService.generateVivaQA(
        chapter.title,
        chapter.subject.name,
        chapter.subject.domain.name,
        count,
        options
      )
      
      await job.updateProgress(50)

      // Save generated Viva Q&A to database
      const vivaQAs = []
      if (result && result.viva && Array.isArray(result.viva)) {
        for (const vivaData of result.viva) {
          const vivaQA = await prisma.vivaQA.create({
            data: {
              question: vivaData.question,
              answer: vivaData.answer,
              chapterId: chapterId,
              isActive: false, // Set as draft initially
              isDraft: true,
              isAIGenerated: true,
              order: vivaQAs.length + 1,
              createdBy: adminId
            }
          })
          vivaQAs.push(vivaQA)
        }
      }

      await job.updateProgress(100)

      return {
        success: true,
        message: `Generated ${vivaQAs.length} Viva Q&As successfully`,
        data: { vivaQAs }
      }
    } catch (error) {
      console.error('Viva Q&A generation job failed:', error)
      throw error
    }
  },

  [JOB_TYPES.GENERATE_BULK_CONTENT]: async (job) => {
    const { domainId, subjectId, chapterCount, mcqCount, vivaCount, options, adminId } = job.data
    
    try {
      // Get domain and subject info
      const domain = await prisma.domain.findUnique({ where: { id: domainId } })
      const subject = await prisma.subject.findUnique({ where: { id: subjectId } })
      
      if (!domain || !subject) {
        throw new Error('Domain or subject not found')
      }

      await job.updateProgress(5)

      // Generate bulk content using AI
      const result = await aiService.generateBulkContent(
        subject.name,
        domain.name,
        chapterCount,
        mcqCount,
        vivaCount,
        options
      )
      
      await job.updateProgress(30)

      const createdData = {
        chapters: [],
        mcqs: [],
        vivaQAs: []
      }

      // Process generated chapters
      if (result && result.chapters && Array.isArray(result.chapters)) {
        for (let i = 0; i < result.chapters.length; i++) {
          const chapterData = result.chapters[i]
          
          // Create chapter
          const chapter = await prisma.chapter.create({
            data: {
              title: chapterData.title,
              description: chapterData.description || '',
              content: chapterData.content || '',
              subjectId: subjectId,
              isActive: false,
              createdBy: adminId
            }
          })
          createdData.chapters.push(chapter)

          // Create MCQs for this chapter
          if (chapterData.mcqs && Array.isArray(chapterData.mcqs)) {
            for (const mcqData of chapterData.mcqs) {
              const mcq = await prisma.mCQ.create({
                data: {
                  question: mcqData.question,
                  optionA: mcqData.optionA,
                  optionB: mcqData.optionB,
                  optionC: mcqData.optionC,
                  optionD: mcqData.optionD,
                  correctAnswer: mcqData.correctAnswer,
                  explanation: mcqData.explanation || '',
                  chapterId: chapter.id,
                  isActive: false,
                  createdBy: adminId
                }
              })
              createdData.mcqs.push(mcq)
            }
          }

          // Create Viva Q&As for this chapter
          if (chapterData.viva && Array.isArray(chapterData.viva)) {
            for (const vivaData of chapterData.viva) {
              const vivaQA = await prisma.vivaQA.create({
                data: {
                  question: vivaData.question,
                  answer: vivaData.answer,
                  chapterId: chapter.id,
                  isActive: false,
                  createdBy: adminId
                }
              })
              createdData.vivaQAs.push(vivaQA)
            }
          }

          // Update progress
          await job.updateProgress(30 + ((i + 1) / result.chapters.length) * 70)
        }
      }

      return {
        success: true,
        message: `Generated ${createdData.chapters.length} chapters, ${createdData.mcqs.length} MCQs, and ${createdData.vivaQAs.length} Viva Q&As successfully`,
        data: createdData
      }
    } catch (error) {
      console.error('Bulk content generation job failed:', error)
      throw error
    }
  }
}

// Create worker
export const aiGenerationWorker = new Worker('ai-generation', async (job) => {
  const processor = jobProcessors[job.name]
  if (!processor) {
    throw new Error(`Unknown job type: ${job.name}`)
  }
  
  return await processor(job)
}, {
  connection: redis,
  concurrency: 2 // Process 2 jobs concurrently
})

// Worker event handlers
aiGenerationWorker.on('completed', (job, result) => {
  console.log(`Job ${job.id} completed:`, result.message)
})

aiGenerationWorker.on('failed', (job, err) => {
  console.error(`Job ${job.id} failed:`, err.message)
})

aiGenerationWorker.on('progress', (job, progress) => {
  console.log(`Job ${job.id} progress: ${progress}%`)
})

// Queue management functions
export const addGenerationJob = async (jobType, data, options = {}) => {
  try {
    const job = await aiGenerationQueue.add(jobType, data, {
      priority: options.priority || 0,
      delay: options.delay || 0,
      ...options
    })
    
    console.log(`Added job ${job.id} of type ${jobType}`)
    return job
  } catch (error) {
    console.error('Failed to add job:', error)
    throw error
  }
}

export const getJobStatus = async (jobId) => {
  try {
    const job = await aiGenerationQueue.getJob(jobId)
    if (!job) {
      return null
    }

    const state = await job.getState()
    const progress = job.progress || 0
    
    return {
      id: job.id,
      name: job.name,
      data: job.data,
      state,
      progress,
      createdAt: new Date(job.timestamp),
      processedAt: job.processedOn ? new Date(job.processedOn) : null,
      finishedAt: job.finishedOn ? new Date(job.finishedOn) : null,
      result: job.returnvalue,
      error: job.failedReason
    }
  } catch (error) {
    console.error('Failed to get job status:', error)
    throw error
  }
}

export const cancelJob = async (jobId) => {
  try {
    const job = await aiGenerationQueue.getJob(jobId)
    if (job) {
      await job.remove()
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to cancel job:', error)
    throw error
  }
}

export const getQueueStats = async () => {
  try {
    const waiting = await aiGenerationQueue.getWaiting()
    const active = await aiGenerationQueue.getActive()
    const completed = await aiGenerationQueue.getCompleted()
    const failed = await aiGenerationQueue.getFailed()

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length
    }
  } catch (error) {
    console.error('Failed to get queue stats:', error)
    throw error
  }
}
