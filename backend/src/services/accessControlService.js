import { prisma } from '../lib/prisma.js'

export class AccessControlService {
  /**
   * Check if user has access to a subject
   * @param {string} userId - User ID
   * @param {string} subjectId - Subject ID
   * @returns {Promise<{hasAccess: boolean, accessType: string, source: string, plan?: object}>}
   */
  static async checkSubjectAccess(userId, subjectId) {
    if (!userId) {
      return { hasAccess: false, accessType: null, source: null }
    }

    // Check direct subject access
    const directAccess = await prisma.userSubjectAccess.findFirst({
      where: {
        userId,
        subjectId,
        isActive: true,
        OR: [
          { endDate: null }, // Permanent access
          { endDate: { gt: new Date() } } // Not expired
        ]
      }
    })

    if (directAccess) {
      return { 
        hasAccess: true, 
        accessType: directAccess.accessType,
        source: 'direct'
      }
    }

    // Check subscription-based access
    const subscriptionAccess = await prisma.userSubscription.findFirst({
      where: {
        userId,
        isActive: true,
        endDate: { gt: new Date() },
        plan: {
          planSubjects: {
            some: {
              subjectId
            }
          }
        }
      },
      include: {
        plan: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (subscriptionAccess) {
      return { 
        hasAccess: true, 
        accessType: 'SUBSCRIPTION',
        source: 'subscription',
        plan: subscriptionAccess.plan
      }
    }

    return { 
      hasAccess: false,
      accessType: null,
      source: null
    }
  }

  /**
   * Check if user has access to a chapter
   * @param {string} userId - User ID
   * @param {string} chapterId - Chapter ID
   * @returns {Promise<{hasAccess: boolean, isPreview: boolean, accessType: string, source: string}>}
   */
  static async checkChapterAccess(userId, chapterId) {
    // Get chapter with subject info
    const chapter = await prisma.chapter.findUnique({
      where: { id: chapterId },
      include: {
        subject: {
          include: {
            chapters: {
              select: { id: true },
              orderBy: { createdAt: 'asc' }
            }
          }
        }
      }
    })

    if (!chapter) {
      return { hasAccess: false, isPreview: false, accessType: null, source: null }
    }

    // Check subject access
    const subjectAccess = await this.checkSubjectAccess(userId, chapter.subjectId)

    if (subjectAccess.hasAccess) {
      return {
        hasAccess: true,
        isPreview: false,
        accessType: subjectAccess.accessType,
        source: subjectAccess.source,
        plan: subjectAccess.plan
      }
    }

    // Check if this is one of the first two chapters (preview access)
    const chapterIndex = chapter.subject.chapters.findIndex(c => c.id === chapterId)
    const isPreviewChapter = chapterIndex < 2

    if (isPreviewChapter) {
      return {
        hasAccess: true,
        isPreview: true,
        accessType: 'PREVIEW',
        source: 'preview'
      }
    }

    return { 
      hasAccess: false, 
      isPreview: false, 
      accessType: null, 
      source: null 
    }
  }

  /**
   * Check if user has access to MCQs in a chapter
   * @param {string} userId - User ID
   * @param {string} chapterId - Chapter ID
   * @returns {Promise<{hasAccess: boolean, isPreview: boolean, accessType: string, source: string}>}
   */
  static async checkMCQAccess(userId, chapterId) {
    const chapterAccess = await this.checkChapterAccess(userId, chapterId)
    
    // MCQs are only available with full access, not preview
    if (chapterAccess.hasAccess && !chapterAccess.isPreview) {
      return chapterAccess
    }

    return { 
      hasAccess: false, 
      isPreview: false, 
      accessType: null, 
      source: null 
    }
  }

  /**
   * Check if user has access to Viva Q&A in a chapter
   * @param {string} userId - User ID
   * @param {string} chapterId - Chapter ID
   * @returns {Promise<{hasAccess: boolean, isPreview: boolean, accessType: string, source: string}>}
   */
  static async checkVivaAccess(userId, chapterId) {
    const chapterAccess = await this.checkChapterAccess(userId, chapterId)
    
    // Viva Q&A is only available with full access, not preview
    if (chapterAccess.hasAccess && !chapterAccess.isPreview) {
      return chapterAccess
    }

    return { 
      hasAccess: false, 
      isPreview: false, 
      accessType: null, 
      source: null 
    }
  }

  /**
   * Get user's accessible subjects with access details
   * @param {string} userId - User ID
   * @returns {Promise<Array>}
   */
  static async getUserAccessibleSubjects(userId) {
    if (!userId) {
      return []
    }

    // Get direct subject access
    const directAccess = await prisma.userSubjectAccess.findMany({
      where: {
        userId,
        isActive: true,
        OR: [
          { endDate: null },
          { endDate: { gt: new Date() } }
        ]
      },
      include: {
        subject: {
          include: {
            domain: true
          }
        }
      }
    })

    // Get subscription-based access
    const subscriptionAccess = await prisma.userSubscription.findMany({
      where: {
        userId,
        isActive: true,
        endDate: { gt: new Date() }
      },
      include: {
        plan: {
          include: {
            planSubjects: {
              include: {
                subject: {
                  include: {
                    domain: true
                  }
                }
              }
            }
          }
        }
      }
    })

    // Combine and deduplicate subjects
    const accessibleSubjects = new Map()

    // Add direct access subjects
    directAccess.forEach(access => {
      accessibleSubjects.set(access.subject.id, {
        ...access.subject,
        accessType: access.accessType,
        source: 'direct',
        endDate: access.endDate
      })
    })

    // Add subscription-based subjects
    subscriptionAccess.forEach(subscription => {
      subscription.plan.planSubjects.forEach(planSubject => {
        if (!accessibleSubjects.has(planSubject.subject.id)) {
          accessibleSubjects.set(planSubject.subject.id, {
            ...planSubject.subject,
            accessType: 'SUBSCRIPTION',
            source: 'subscription',
            plan: {
              id: subscription.plan.id,
              name: subscription.plan.name
            },
            endDate: subscription.endDate
          })
        }
      })
    })

    return Array.from(accessibleSubjects.values())
  }

  /**
   * Filter chapters based on user access
   * @param {string} userId - User ID
   * @param {Array} chapters - Array of chapters
   * @param {string} subjectId - Subject ID
   * @returns {Promise<Array>}
   */
  static async filterChaptersByAccess(userId, chapters, subjectId) {
    const subjectAccess = await this.checkSubjectAccess(userId, subjectId)

    if (subjectAccess.hasAccess) {
      // User has full access to all chapters
      return chapters.map(chapter => ({
        ...chapter,
        hasAccess: true,
        isPreview: false,
        accessType: subjectAccess.accessType,
        source: subjectAccess.source
      }))
    }

    // User only has preview access to first 2 chapters
    return chapters.map((chapter, index) => ({
      ...chapter,
      hasAccess: index < 2,
      isPreview: index < 2,
      accessType: index < 2 ? 'PREVIEW' : null,
      source: index < 2 ? 'preview' : null
    }))
  }

  /**
   * Check if user can perform admin actions
   * @param {string} userId - User ID
   * @returns {Promise<boolean>}
   */
  static async isAdmin(userId) {
    if (!userId) return false

    const admin = await prisma.admin.findUnique({
      where: { id: userId }
    })

    return !!admin
  }
}
