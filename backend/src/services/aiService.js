import OpenAI from 'openai'
import { GoogleGenerativeAI } from '@google/generative-ai'
import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

class AIService {
  constructor() {
    this.openai = null
    this.gemini = null
    this.prompts = null
    this.provider = process.env.AI_PROVIDER || 'openai'
    this.maxRetries = parseInt(process.env.AI_MAX_RETRIES) || 3
    this.timeout = parseInt(process.env.AI_TIMEOUT) || 30000
    
    this.initializeProviders()
    this.loadPrompts()
  }

  async initializeProviders() {
    try {
      // Initialize OpenAI
      if (process.env.OPENAI_API_KEY) {
        this.openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
          timeout: this.timeout
        })
      }

      // Initialize Gemini
      if (process.env.GEMINI_API_KEY) {
        this.gemini = new GoogleGenerativeAI(process.env.GEMINI_API_KEY)
      }
    } catch (error) {
      console.error('Failed to initialize AI providers:', error)
    }
  }

  async loadPrompts() {
    try {
      const promptsPath = path.join(__dirname, '../../config/ai-prompts.json')
      const promptsData = await fs.readFile(promptsPath, 'utf8')
      this.prompts = JSON.parse(promptsData)
    } catch (error) {
      console.error('Failed to load AI prompts:', error)
      this.prompts = {}
    }
  }

  getPrompt(promptType, variables = {}) {
    if (!this.prompts[promptType]) {
      throw new Error(`Prompt type '${promptType}' not found`)
    }

    let prompt = this.prompts[promptType].prompt
    
    // Replace variables in the prompt
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{${key}}`, 'g')
      prompt = prompt.replace(regex, value)
    })

    return prompt
  }

  async generateWithOpenAI(prompt, options = {}) {
    if (!this.openai) {
      throw new Error('OpenAI not initialized. Please check your API key.')
    }

    const {
      model = 'gpt-3.5-turbo',
      temperature = 0.7,
      maxTokens = 2000
    } = options

    try {
      const response = await this.openai.chat.completions.create({
        model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert educational content creator. Always respond with valid JSON only, no additional text or formatting.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature,
        max_tokens: maxTokens,
        response_format: { type: 'json_object' }
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('No content received from OpenAI')
      }

      return JSON.parse(content)
    } catch (error) {
      console.error('OpenAI generation error:', error)
      throw new Error(`OpenAI generation failed: ${error.message}`)
    }
  }

  async generateWithGemini(prompt, options = {}) {
    if (!this.gemini) {
      throw new Error('Gemini not initialized. Please check your API key.')
    }

    const {
      model = 'gemini-pro',
      temperature = 0.7
    } = options

    try {
      const genAI = this.gemini.getGenerativeModel({ 
        model,
        generationConfig: {
          temperature,
          candidateCount: 1
        }
      })

      const enhancedPrompt = `${prompt}\n\nIMPORTANT: Respond only with valid JSON, no additional text or formatting.`
      
      const result = await genAI.generateContent(enhancedPrompt)
      const response = await result.response
      const content = response.text()

      if (!content) {
        throw new Error('No content received from Gemini')
      }

      // Clean the response to extract JSON
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No valid JSON found in Gemini response')
      }

      return JSON.parse(jsonMatch[0])
    } catch (error) {
      console.error('Gemini generation error:', error)
      throw new Error(`Gemini generation failed: ${error.message}`)
    }
  }

  async generate(promptType, variables = {}, options = {}) {
    const prompt = this.getPrompt(promptType, variables)
    
    let lastError = null
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`AI Generation attempt ${attempt}/${this.maxRetries} for ${promptType}`)
        
        let result
        if (this.provider === 'openai' && this.openai) {
          result = await this.generateWithOpenAI(prompt, options)
        } else if (this.provider === 'gemini' && this.gemini) {
          result = await this.generateWithGemini(prompt, options)
        } else {
          // Fallback to available provider
          if (this.openai) {
            result = await this.generateWithOpenAI(prompt, options)
          } else if (this.gemini) {
            result = await this.generateWithGemini(prompt, options)
          } else {
            throw new Error('No AI providers available')
          }
        }

        console.log(`AI Generation successful for ${promptType}`)
        return result
      } catch (error) {
        lastError = error
        console.error(`AI Generation attempt ${attempt} failed:`, error.message)
        
        if (attempt < this.maxRetries) {
          // Wait before retry (exponential backoff)
          const delay = Math.pow(2, attempt) * 1000
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    throw new Error(`AI generation failed after ${this.maxRetries} attempts: ${lastError.message}`)
  }

  // Specific generation methods
  async generateChapters(domain, subject, count = 5, options = {}) {
    return this.generate('chapter_generation', { domain, subject, count }, options)
  }

  async generateChapterContent(title, subject, domain, options = {}) {
    return this.generate('chapter_content', { title, subject, domain }, options)
  }

  async generateMCQs(chapter, subject, domain, count = 10, options = {}) {
    return this.generate('mcq_generation', { chapter, subject, domain, count }, options)
  }

  async generateVivaQA(chapter, subject, domain, count = 5, options = {}) {
    return this.generate('viva_generation', { chapter, subject, domain, count }, options)
  }

  async generateBulkContent(subject, domain, chapterCount = 5, mcqCount = 10, vivaCount = 5, options = {}) {
    return this.generate('bulk_content_generation', {
      subject,
      domain,
      chapter_count: chapterCount,
      mcq_count: mcqCount,
      viva_count: vivaCount
    }, options)
  }

  // Utility methods
  getAvailablePrompts() {
    return Object.keys(this.prompts).map(key => ({
      key,
      name: this.prompts[key].name,
      description: this.prompts[key].description,
      variables: this.prompts[key].variables
    }))
  }

  getPromptTemplate(promptType) {
    return this.prompts[promptType] || null
  }

  updatePrompt(promptType, newPrompt) {
    if (this.prompts[promptType]) {
      this.prompts[promptType].prompt = newPrompt
      return true
    }
    return false
  }

  async savePrompts() {
    try {
      const promptsPath = path.join(__dirname, '../../config/ai-prompts.json')
      await fs.writeFile(promptsPath, JSON.stringify(this.prompts, null, 2))
      return true
    } catch (error) {
      console.error('Failed to save prompts:', error)
      return false
    }
  }
}

// Singleton instance
const aiService = new AIService()

export default aiService
