import { queueController } from '../controllers/queueController.js'
import { authMiddleware } from '../middleware/auth.js'

export default async function queueRoutes(fastify, options) {
  // Apply authentication middleware to all routes
  fastify.addHook('preHandler', authMiddleware)

  // Queue health and monitoring
  fastify.get('/health', queueController.getHealth)
  fastify.get('/info', queueController.getQueueInfo)
  fastify.get('/stats', queueController.getStats)

  // Job management
  fastify.get('/jobs/:jobId', queueController.getJobStatus)
  fastify.delete('/jobs/:jobId', queueController.cancelJob)
  fastify.post('/jobs/:jobId/retry', queueController.retryJob)
  fastify.get('/jobs/:jobId/logs', queueController.getJobLogs)

  // Bulk operations
  fastify.post('/retry-failed', queueController.retryFailedJobs)
  fastify.post('/clean-completed', queueController.cleanCompletedJobs)

  // Queue control
  fastify.post('/:queueName/pause', queueController.pauseQueue)
  fastify.post('/:queueName/resume', queueController.resumeQueue)
}
