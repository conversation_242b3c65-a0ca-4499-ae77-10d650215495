import admin from 'firebase-admin'

let firebaseApp = null

export const initializeFirebase = () => {
  if (firebaseApp) {
    return firebaseApp
  }

  try {
    // Initialize Firebase Admin SDK
    // In production, use service account key file or environment variables
    // For development, you can use the Firebase emulator or service account key
    
    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: "https://accounts.google.com/o/oauth2/auth",
      token_uri: "https://oauth2.googleapis.com/token",
      auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
      client_x509_cert_url: process.env.FIREBASE_CLIENT_CERT_URL
    }

    // Only initialize if we have the required environment variables
    if (process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL) {
      firebaseApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID
      })
      
      console.log('Firebase Admin SDK initialized successfully')
    } else {
      console.warn('Firebase environment variables not found. Firebase features will be disabled.')
      console.warn('Required variables: FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY, FIREBASE_CLIENT_EMAIL')
    }
    
    return firebaseApp
  } catch (error) {
    console.error('Error initializing Firebase Admin SDK:', error)
    return null
  }
}

export const getFirebaseAuth = () => {
  if (!firebaseApp) {
    initializeFirebase()
  }
  return firebaseApp ? admin.auth() : null
}

export const getFirebaseMessaging = () => {
  if (!firebaseApp) {
    initializeFirebase()
  }
  return firebaseApp ? admin.messaging() : null
}

// Verify Firebase ID token
export const verifyIdToken = async (idToken) => {
  try {
    const auth = getFirebaseAuth()
    if (!auth) {
      throw new Error('Firebase not initialized')
    }
    
    const decodedToken = await auth.verifyIdToken(idToken)
    return decodedToken
  } catch (error) {
    console.error('Error verifying ID token:', error)
    throw error
  }
}

// Send push notification
export const sendPushNotification = async (fcmToken, title, body, data = {}) => {
  try {
    const messaging = getFirebaseMessaging()
    if (!messaging) {
      throw new Error('Firebase Messaging not initialized')
    }

    const message = {
      token: fcmToken,
      notification: {
        title,
        body
      },
      data: {
        ...data,
        timestamp: Date.now().toString()
      }
    }

    const response = await messaging.send(message)
    console.log('Push notification sent successfully:', response)
    return response
  } catch (error) {
    console.error('Error sending push notification:', error)
    throw error
  }
}

// Send push notification to multiple tokens
export const sendPushNotificationToMultiple = async (fcmTokens, title, body, data = {}) => {
  try {
    const messaging = getFirebaseMessaging()
    if (!messaging) {
      throw new Error('Firebase Messaging not initialized')
    }

    const message = {
      tokens: fcmTokens,
      notification: {
        title,
        body
      },
      data: {
        ...data,
        timestamp: Date.now().toString()
      }
    }

    const response = await messaging.sendEachForMulticast(message)
    console.log('Push notifications sent:', response.successCount, 'successful,', response.failureCount, 'failed')
    return response
  } catch (error) {
    console.error('Error sending push notifications:', error)
    throw error
  }
}

export default {
  initializeFirebase,
  getFirebaseAuth,
  getFirebaseMessaging,
  verifyIdToken,
  sendPushNotification,
  sendPushNotificationToMultiple
}
