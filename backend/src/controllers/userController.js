import { PrismaClient } from '@prisma/client'
import { createUserSchema, updateUserSchema, paginationSchema, searchSchema } from '../types/schemas.js'
import { successResponse, errorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

const prisma = new PrismaClient()

export const userController = {
  // Get all users with pagination and filtering
  async getAll(request, reply) {
    try {
      const { page, limit } = paginationSchema.parse(request.query)
      const { search, isActive } = searchSchema.parse(request.query)
      const skip = (page - 1) * limit

      const where = {
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
            { phone: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(isActive !== undefined && { isActive: isActive === 'true' })
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          include: {
            subscriptions: {
              include: {
                plan: true
              },
              where: { isActive: true },
              orderBy: { createdAt: 'desc' }
            },
            _count: {
              select: {
                subscriptions: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.user.count({ where })
      ])

      const pagination = getPaginationMeta(total, page, limit)

      return successResponse(reply, {
        users,
        pagination
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get user by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          subscriptions: {
            include: {
              plan: true
            },
            orderBy: { createdAt: 'desc' }
          }
        }
      })

      if (!user) {
        return errorResponse(reply, 'User not found', 404)
      }

      return successResponse(reply, { user })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Create new user
  async create(request, reply) {
    try {
      const data = createUserSchema.parse(request.body)

      // Check if user with email already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: data.email }
      })

      if (existingUser) {
        return errorResponse(reply, 'User with this email already exists', 400)
      }

      const user = await prisma.user.create({
        data,
        include: {
          subscriptions: {
            include: {
              plan: true
            }
          }
        }
      })

      return successResponse(reply, { user }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Update user
  async update(request, reply) {
    try {
      const { id } = request.params
      const data = updateUserSchema.parse(request.body)

      // Check if user exists
      const existingUser = await prisma.user.findUnique({
        where: { id }
      })

      if (!existingUser) {
        return errorResponse(reply, 'User not found', 404)
      }

      // If email is being updated, check if it's unique
      if (data.email && data.email !== existingUser.email) {
        const userWithSameEmail = await prisma.user.findUnique({
          where: { email: data.email }
        })

        if (userWithSameEmail) {
          return errorResponse(reply, 'User with this email already exists', 400)
        }
      }

      const user = await prisma.user.update({
        where: { id },
        data,
        include: {
          subscriptions: {
            include: {
              plan: true
            }
          }
        }
      })

      return successResponse(reply, { user })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Delete user
  async delete(request, reply) {
    try {
      const { id } = request.params

      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              subscriptions: true
            }
          }
        }
      })

      if (!user) {
        return errorResponse(reply, 'User not found', 404)
      }

      // Check if user has active subscriptions
      const activeSubscriptions = await prisma.userSubscription.count({
        where: {
          userId: id,
          isActive: true,
          endDate: { gt: new Date() }
        }
      })

      if (activeSubscriptions > 0) {
        return errorResponse(reply, 'Cannot delete user with active subscriptions', 400)
      }

      await prisma.user.delete({
        where: { id }
      })

      return successResponse(reply, { message: 'User deleted successfully' })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get user statistics
  async getStats(request, reply) {
    try {
      const [totalUsers, activeUsers, totalSubscriptions, activeSubscriptions] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { isActive: true } }),
        prisma.userSubscription.count(),
        prisma.userSubscription.count({
          where: {
            isActive: true,
            endDate: { gt: new Date() }
          }
        })
      ])

      const stats = {
        totalUsers,
        activeUsers,
        inactiveUsers: totalUsers - activeUsers,
        totalSubscriptions,
        activeSubscriptions,
        expiredSubscriptions: totalSubscriptions - activeSubscriptions
      }

      return successResponse(reply, { stats })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  }
}
