import { PrismaClient } from '@prisma/client'
import { createMCQSchema, updateMCQSchema, paginationSchema, searchSchema } from '../types/schemas.js'
import { successResponse, errorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

const prisma = new PrismaClient()

export const mcqController = {
  // Get all MCQs with pagination and filtering
  async getAll(request, reply) {
    try {
      const { page, limit } = paginationSchema.parse(request.query)
      const { search } = searchSchema.parse(request.query)
      const { chapterId, subjectId, domainId } = request.query
      const skip = (page - 1) * limit

      const where = {
        ...(search && {
          OR: [
            { question: { contains: search, mode: 'insensitive' } },
            { explanation: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(chapterId && { chapterId }),
        ...(subjectId && {
          chapter: {
            subjectId
          }
        }),
        ...(domainId && {
          chapter: {
            subject: {
              domainId
            }
          }
        })
      }

      const [mcqs, total] = await Promise.all([
        prisma.mCQ.findMany({
          where,
          include: {
            chapter: {
              include: {
                subject: {
                  include: {
                    domain: true
                  }
                }
              }
            }
          },
          orderBy: [
            { order: 'asc' },
            { createdAt: 'desc' }
          ],
          skip,
          take: limit
        }),
        prisma.mCQ.count({ where })
      ])

      const pagination = getPaginationMeta(total, page, limit)

      return successResponse(reply, {
        mcqs,
        pagination
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get MCQ by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const mcq = await prisma.mCQ.findUnique({
        where: { id },
        include: {
          chapter: {
            include: {
              subject: {
                include: {
                  domain: true
                }
              }
            }
          }
        }
      })

      if (!mcq) {
        return errorResponse(reply, 'MCQ not found', 404)
      }

      return successResponse(reply, { mcq })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Create new MCQ
  async create(request, reply) {
    try {
      const data = createMCQSchema.parse(request.body)

      // Check if chapter exists
      const chapter = await prisma.chapter.findUnique({
        where: { id: data.chapterId }
      })

      if (!chapter) {
        return errorResponse(reply, 'Chapter not found', 404)
      }

      // Validate correct answer
      if (!['A', 'B', 'C', 'D'].includes(data.correctAnswer)) {
        return errorResponse(reply, 'Correct answer must be A, B, C, or D', 400)
      }

      // Get the next order number
      const lastMcq = await prisma.mCQ.findFirst({
        where: { chapterId: data.chapterId },
        orderBy: { order: 'desc' }
      })

      const mcq = await prisma.mCQ.create({
        data: {
          ...data,
          order: data.order ?? (lastMcq?.order ?? 0) + 1
        },
        include: {
          chapter: {
            include: {
              subject: {
                include: {
                  domain: true
                }
              }
            }
          }
        }
      })

      return successResponse(reply, { mcq }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Update MCQ
  async update(request, reply) {
    try {
      const { id } = request.params
      const data = updateMCQSchema.parse(request.body)

      // Check if MCQ exists
      const existingMcq = await prisma.mCQ.findUnique({
        where: { id }
      })

      if (!existingMcq) {
        return errorResponse(reply, 'MCQ not found', 404)
      }

      // If chapterId is being updated, check if the new chapter exists
      if (data.chapterId && data.chapterId !== existingMcq.chapterId) {
        const chapter = await prisma.chapter.findUnique({
          where: { id: data.chapterId }
        })

        if (!chapter) {
          return errorResponse(reply, 'Chapter not found', 404)
        }
      }

      // Validate correct answer if provided
      if (data.correctAnswer && !['A', 'B', 'C', 'D'].includes(data.correctAnswer)) {
        return errorResponse(reply, 'Correct answer must be A, B, C, or D', 400)
      }

      const mcq = await prisma.mCQ.update({
        where: { id },
        data,
        include: {
          chapter: {
            include: {
              subject: {
                include: {
                  domain: true
                }
              }
            }
          }
        }
      })

      return successResponse(reply, { mcq })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Delete MCQ
  async delete(request, reply) {
    try {
      const { id } = request.params

      const mcq = await prisma.mCQ.findUnique({
        where: { id }
      })

      if (!mcq) {
        return errorResponse(reply, 'MCQ not found', 404)
      }

      await prisma.mCQ.delete({
        where: { id }
      })

      return successResponse(reply, { message: 'MCQ deleted successfully' })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Bulk create MCQs
  async bulkCreate(request, reply) {
    try {
      const { mcqs } = request.body

      if (!Array.isArray(mcqs) || mcqs.length === 0) {
        return errorResponse(reply, 'MCQs array is required and cannot be empty', 400)
      }

      // Validate all MCQs
      const validatedMcqs = mcqs.map(mcq => createMCQSchema.parse(mcq))

      // Check if all chapters exist
      const chapterIds = [...new Set(validatedMcqs.map(mcq => mcq.chapterId))]
      const chapters = await prisma.chapter.findMany({
        where: { id: { in: chapterIds } }
      })

      if (chapters.length !== chapterIds.length) {
        return errorResponse(reply, 'One or more chapters not found', 404)
      }

      // Create MCQs in transaction
      const createdMcqs = await prisma.$transaction(
        validatedMcqs.map((mcq, index) => 
          prisma.mCQ.create({
            data: {
              ...mcq,
              order: mcq.order ?? index + 1
            }
          })
        )
      )

      return successResponse(reply, { 
        mcqs: createdMcqs,
        count: createdMcqs.length 
      }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  }
}
