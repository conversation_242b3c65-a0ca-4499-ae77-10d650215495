import prisma from '../../lib/prisma.js'
import { 
  createPointTransactionSchema, 
  redeemPointsSchema, 
  generateReferralCodeSchema,
  applyReferralCodeSchema,
  dailyLoginSchema,
  studyTimeRewardSchema
} from '../../types/schemas.js'

// ===== POINT EARNING FUNCTIONS =====

// Daily login reward
export const recordDailyLogin = async (request, reply) => {
  try {
    const userId = request.user.id
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { lastLoginDate: true, loginStreak: true, pointBalance: true }
    })

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'User not found'
      })
    }

    // Check if user already logged in today
    const lastLogin = user.lastLoginDate ? new Date(user.lastLoginDate) : null
    if (lastLogin && lastLogin.getTime() === today.getTime()) {
      return reply.send({
        success: true,
        message: 'Already logged in today',
        data: {
          pointsEarned: 0,
          currentStreak: user.loginStreak,
          totalPoints: user.pointBalance
        }
      })
    }

    // Calculate streak and points
    let newStreak = 1
    let pointsEarned = 5 // Base daily login points

    if (lastLogin) {
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      
      if (lastLogin.getTime() === yesterday.getTime()) {
        // Consecutive day
        newStreak = user.loginStreak + 1
        
        // Streak bonus: +1 point for every 7 days of streak
        const streakBonus = Math.floor(newStreak / 7)
        pointsEarned += streakBonus
      }
    }

    // Update user and create transaction
    const [updatedUser, transaction] = await prisma.$transaction([
      prisma.user.update({
        where: { id: userId },
        data: {
          lastLoginDate: today,
          loginStreak: newStreak,
          pointBalance: { increment: pointsEarned },
          totalPointsEarned: { increment: pointsEarned }
        }
      }),
      prisma.pointTransaction.create({
        data: {
          userId,
          type: 'earned',
          category: 'daily_login',
          amount: pointsEarned,
          description: `Daily login reward (${newStreak} day streak)`,
          metadata: JSON.stringify({ streak: newStreak, basePoints: 5, streakBonus: pointsEarned - 5 })
        }
      })
    ])

    reply.send({
      success: true,
      data: {
        pointsEarned,
        currentStreak: newStreak,
        totalPoints: updatedUser.pointBalance,
        transaction: {
          id: transaction.id,
          description: transaction.description
        }
      }
    })
  } catch (error) {
    console.error('Error recording daily login:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to record daily login'
    })
  }
}

// Study time reward
export const recordStudyTimeReward = async (request, reply) => {
  try {
    const userId = request.user.id
    
    const validation = studyTimeRewardSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid request data',
        errors: validation.error.errors
      })
    }

    const { studyTimeMinutes, activityType } = validation.data

    // Calculate points: 1 point per 10 minutes of study
    const pointsEarned = Math.floor(studyTimeMinutes / 10)

    if (pointsEarned === 0) {
      return reply.send({
        success: true,
        message: 'Study time recorded, but no points earned (minimum 10 minutes required)',
        data: { pointsEarned: 0 }
      })
    }

    // Update user points and create transaction
    const [updatedUser, transaction] = await prisma.$transaction([
      prisma.user.update({
        where: { id: userId },
        data: {
          pointBalance: { increment: pointsEarned },
          totalPointsEarned: { increment: pointsEarned }
        }
      }),
      prisma.pointTransaction.create({
        data: {
          userId,
          type: 'earned',
          category: 'study_time',
          amount: pointsEarned,
          description: `Study time reward: ${studyTimeMinutes} minutes of ${activityType}`,
          metadata: JSON.stringify({ studyTimeMinutes, activityType })
        }
      })
    ])

    reply.send({
      success: true,
      data: {
        pointsEarned,
        studyTimeMinutes,
        totalPoints: updatedUser.pointBalance,
        transaction: {
          id: transaction.id,
          description: transaction.description
        }
      }
    })
  } catch (error) {
    console.error('Error recording study time reward:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to record study time reward'
    })
  }
}

// Achievement reward
export const recordAchievementReward = async (userId, achievementData) => {
  try {
    const { name, description, pointsAwarded } = achievementData

    if (pointsAwarded <= 0) return null

    // Update user points and create achievement + transaction
    const [updatedUser, achievement, transaction] = await prisma.$transaction([
      prisma.user.update({
        where: { id: userId },
        data: {
          pointBalance: { increment: pointsAwarded },
          totalPointsEarned: { increment: pointsAwarded }
        }
      }),
      prisma.userAchievement.create({
        data: {
          userId,
          type: 'achievement',
          name,
          description,
          pointsAwarded
        }
      }),
      prisma.pointTransaction.create({
        data: {
          userId,
          type: 'earned',
          category: 'achievement',
          amount: pointsAwarded,
          description: `Achievement unlocked: ${name}`,
          metadata: JSON.stringify({ achievementType: 'achievement', name, description })
        }
      })
    ])

    return {
      pointsEarned: pointsAwarded,
      achievement,
      transaction,
      totalPoints: updatedUser.pointBalance
    }
  } catch (error) {
    console.error('Error recording achievement reward:', error)
    return null
  }
}

// ===== POINT REDEMPTION FUNCTIONS =====

// Calculate discount for purchase
export const calculatePointDiscount = async (request, reply) => {
  try {
    const userId = request.user.id
    const validation = redeemPointsSchema.safeParse(request.body)
    
    if (!validation.success) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid request data',
        errors: validation.error.errors
      })
    }

    const { pointsToUse, purchaseType, itemId } = validation.data

    // Get user's current point balance
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { pointBalance: true }
    })

    if (!user || user.pointBalance < pointsToUse) {
      return reply.status(400).send({
        success: false,
        message: 'Insufficient points'
      })
    }

    // Get item price
    let originalPrice = 0
    if (purchaseType === 'subscription') {
      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id: itemId },
        select: { price: true, name: true }
      })
      if (!plan) {
        return reply.status(404).send({
          success: false,
          message: 'Subscription plan not found'
        })
      }
      originalPrice = plan.price
    } else if (purchaseType === 'subject') {
      const pricing = await prisma.subjectPricing.findFirst({
        where: { subjectId: itemId },
        select: { price: true },
        include: {
          subject: {
            select: { name: true }
          }
        }
      })
      if (!pricing) {
        return reply.status(404).send({
          success: false,
          message: 'Subject pricing not found'
        })
      }
      originalPrice = pricing.price
    }

    // Calculate discount (1 point = 1 BDT discount)
    const discountAmount = Math.min(pointsToUse, originalPrice)
    const finalPrice = Math.max(0, originalPrice - discountAmount)

    reply.send({
      success: true,
      data: {
        originalPrice,
        pointsToUse: discountAmount,
        discountAmount,
        finalPrice,
        remainingPoints: user.pointBalance - discountAmount
      }
    })
  } catch (error) {
    console.error('Error calculating point discount:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to calculate point discount'
    })
  }
}

// Get user's point balance and transaction history
export const getPointsHistory = async (request, reply) => {
  try {
    const userId = request.user.id
    const { page = 1, limit = 20, type, category } = request.query

    const where = { userId }
    if (type) where.type = type
    if (category) where.category = category

    const [user, transactions, total] = await Promise.all([
      prisma.user.findUnique({
        where: { id: userId },
        select: { 
          pointBalance: true, 
          totalPointsEarned: true,
          loginStreak: true,
          lastLoginDate: true
        }
      }),
      prisma.pointTransaction.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: parseInt(limit)
      }),
      prisma.pointTransaction.count({ where })
    ])

    reply.send({
      success: true,
      data: {
        user: {
          pointBalance: user.pointBalance,
          totalPointsEarned: user.totalPointsEarned,
          loginStreak: user.loginStreak,
          lastLoginDate: user.lastLoginDate
        },
        transactions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('Error getting points history:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to get points history'
    })
  }
}
