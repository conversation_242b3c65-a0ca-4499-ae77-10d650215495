import prisma from '../../lib/prisma.js'
import { createMCQExamSchema, createMCQAttemptSchema, updateMCQExamSchema } from '../../types/schemas.js'

// Start a new MCQ exam
export const startMCQExam = async (request, reply) => {
  try {
    const userId = request.user.id
    
    // Validate request body
    const validation = createMCQExamSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid request data',
        errors: validation.error.errors
      })
    }

    const { chapterId, subjectId, examType, totalQuestions } = validation.data

    let mcqs = []

    // Get MCQs based on exam type
    if (examType === 'chapter' && chapterId) {
      // Verify user has access to chapter
      const hasAccess = await verifyChapterAccess(userId, chapterId)
      if (!hasAccess) {
        return reply.status(403).send({
          success: false,
          message: 'Access denied to this chapter'
        })
      }

      mcqs = await prisma.mCQ.findMany({
        where: {
          chapterId,
          isActive: true
        },
        select: {
          id: true,
          question: true,
          optionA: true,
          optionB: true,
          optionC: true,
          optionD: true,
          correctAnswer: true,
          explanation: true,
          order: true
        },
        orderBy: {
          order: 'asc'
        },
        take: totalQuestions
      })
    } else if (examType === 'subject' && subjectId) {
      // Verify user has access to subject
      const hasAccess = await verifySubjectAccess(userId, subjectId)
      if (!hasAccess) {
        return reply.status(403).send({
          success: false,
          message: 'Access denied to this subject'
        })
      }

      mcqs = await prisma.mCQ.findMany({
        where: {
          chapter: {
            subjectId,
            isActive: true
          },
          isActive: true
        },
        select: {
          id: true,
          question: true,
          optionA: true,
          optionB: true,
          optionC: true,
          optionD: true,
          correctAnswer: true,
          explanation: true,
          chapterId: true,
          chapter: {
            select: {
              title: true
            }
          }
        },
        orderBy: {
          order: 'asc'
        }
      })

      // Shuffle and limit for random exam
      if (examType === 'random') {
        mcqs = shuffleArray(mcqs).slice(0, totalQuestions)
      } else {
        mcqs = mcqs.slice(0, totalQuestions)
      }
    } else if (examType === 'random') {
      // Get random MCQs from all accessible subjects
      const accessibleSubjects = await getUserAccessibleSubjects(userId)
      
      mcqs = await prisma.mCQ.findMany({
        where: {
          chapter: {
            subjectId: {
              in: accessibleSubjects
            },
            isActive: true
          },
          isActive: true
        },
        select: {
          id: true,
          question: true,
          optionA: true,
          optionB: true,
          optionC: true,
          optionD: true,
          correctAnswer: true,
          explanation: true,
          chapterId: true,
          chapter: {
            select: {
              title: true,
              subject: {
                select: {
                  name: true
                }
              }
            }
          }
        }
      })

      mcqs = shuffleArray(mcqs).slice(0, totalQuestions)
    }

    if (mcqs.length === 0) {
      return reply.status(404).send({
        success: false,
        message: 'No MCQs found for the specified criteria'
      })
    }

    // Create exam record
    const exam = await prisma.mCQExam.create({
      data: {
        userId,
        chapterId,
        subjectId,
        examType,
        totalQuestions: mcqs.length
      }
    })

    // Remove correct answers from response (will be checked on submission)
    const examQuestions = mcqs.map(mcq => ({
      id: mcq.id,
      question: mcq.question,
      optionA: mcq.optionA,
      optionB: mcq.optionB,
      optionC: mcq.optionC,
      optionD: mcq.optionD,
      chapterTitle: mcq.chapter?.title,
      subjectName: mcq.chapter?.subject?.name
    }))

    reply.send({
      success: true,
      data: {
        examId: exam.id,
        examType: exam.examType,
        totalQuestions: exam.totalQuestions,
        questions: examQuestions,
        timeLimit: 30 * mcqs.length, // 30 seconds per question
        startTime: exam.createdAt
      }
    })
  } catch (error) {
    console.error('Error starting MCQ exam:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to start MCQ exam'
    })
  }
}

// Submit MCQ answer
export const submitMCQAnswer = async (request, reply) => {
  try {
    const { examId } = request.params
    const userId = request.user.id
    
    // Validate request body
    const validation = createMCQAttemptSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid request data',
        errors: validation.error.errors
      })
    }

    const { mcqId, selectedAnswer, timeSpent } = validation.data

    // Verify exam belongs to user and is not completed
    const exam = await prisma.mCQExam.findFirst({
      where: {
        id: examId,
        userId,
        isCompleted: false
      }
    })

    if (!exam) {
      return reply.status(404).send({
        success: false,
        message: 'Exam not found or already completed'
      })
    }

    // Get MCQ with correct answer
    const mcq = await prisma.mCQ.findUnique({
      where: { id: mcqId },
      select: {
        id: true,
        correctAnswer: true,
        explanation: true
      }
    })

    if (!mcq) {
      return reply.status(404).send({
        success: false,
        message: 'MCQ not found'
      })
    }

    const isCorrect = selectedAnswer === mcq.correctAnswer

    // Check if user already answered this MCQ in this exam
    const existingAttempt = await prisma.mCQAttempt.findFirst({
      where: {
        userId,
        mcqId,
        examId
      }
    })

    if (existingAttempt) {
      return reply.status(400).send({
        success: false,
        message: 'MCQ already answered in this exam'
      })
    }

    // Create MCQ attempt
    const attempt = await prisma.mCQAttempt.create({
      data: {
        userId,
        mcqId,
        examId,
        selectedAnswer,
        isCorrect,
        timeSpent
      }
    })

    reply.send({
      success: true,
      data: {
        attemptId: attempt.id,
        isCorrect,
        correctAnswer: mcq.correctAnswer,
        explanation: mcq.explanation,
        timeSpent
      }
    })
  } catch (error) {
    console.error('Error submitting MCQ answer:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to submit MCQ answer'
    })
  }
}

// Complete MCQ exam
export const completeMCQExam = async (request, reply) => {
  try {
    const { examId } = request.params
    const userId = request.user.id

    // Get exam with attempts
    const exam = await prisma.mCQExam.findFirst({
      where: {
        id: examId,
        userId,
        isCompleted: false
      },
      include: {
        attempts: true
      }
    })

    if (!exam) {
      return reply.status(404).send({
        success: false,
        message: 'Exam not found or already completed'
      })
    }

    // Calculate results
    const totalQuestions = exam.totalQuestions
    const answeredQuestions = exam.attempts.length
    const correctAnswers = exam.attempts.filter(attempt => attempt.isCorrect).length
    const score = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0
    const totalTimeSpent = exam.attempts.reduce((total, attempt) => total + attempt.timeSpent, 0)

    // Update exam as completed
    const completedExam = await prisma.mCQExam.update({
      where: { id: examId },
      data: {
        correctAnswers,
        score,
        timeSpent: totalTimeSpent,
        isCompleted: true,
        completedAt: new Date()
      }
    })

    // Update leaderboard (implement later)
    // await updateLeaderboard(userId, exam.subjectId, score)

    // Award achievements based on performance (implement later)
    // await checkExamAchievements(userId, score, correctAnswers, totalQuestions)

    reply.send({
      success: true,
      data: {
        examId: completedExam.id,
        totalQuestions,
        answeredQuestions,
        correctAnswers,
        score,
        timeSpent: totalTimeSpent,
        completedAt: completedExam.completedAt,
        performance: getPerformanceLevel(score)
      }
    })
  } catch (error) {
    console.error('Error completing MCQ exam:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to complete MCQ exam'
    })
  }
}

// Get exam history for user
export const getExamHistory = async (request, reply) => {
  try {
    const userId = request.user.id
    const { page = 1, limit = 10, examType, subjectId } = request.query

    const where = {
      userId,
      isCompleted: true
    }

    if (examType) {
      where.examType = examType
    }

    if (subjectId) {
      where.subjectId = subjectId
    }

    const exams = await prisma.mCQExam.findMany({
      where,
      include: {
        attempts: {
          select: {
            isCorrect: true,
            timeSpent: true
          }
        }
      },
      orderBy: {
        completedAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: parseInt(limit)
    })

    const total = await prisma.mCQExam.count({ where })

    reply.send({
      success: true,
      data: {
        exams: exams.map(exam => ({
          id: exam.id,
          examType: exam.examType,
          totalQuestions: exam.totalQuestions,
          correctAnswers: exam.correctAnswers,
          score: exam.score,
          timeSpent: exam.timeSpent,
          completedAt: exam.completedAt,
          performance: getPerformanceLevel(exam.score)
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('Error getting exam history:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to get exam history'
    })
  }
}

// Helper functions
const shuffleArray = (array) => {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

const getPerformanceLevel = (score) => {
  if (score >= 90) return 'Excellent'
  if (score >= 80) return 'Very Good'
  if (score >= 70) return 'Good'
  if (score >= 60) return 'Average'
  return 'Needs Improvement'
}

const verifyChapterAccess = async (userId, chapterId) => {
  const chapter = await prisma.chapter.findFirst({
    where: {
      id: chapterId,
      isActive: true
    },
    include: {
      subject: {
        include: {
          userSubjectAccess: {
            where: {
              userId,
              isActive: true
            }
          },
          planSubjects: {
            include: {
              plan: {
                include: {
                  userSubscriptions: {
                    where: {
                      userId,
                      isActive: true,
                      endDate: {
                        gte: new Date()
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  })

  if (!chapter) return false

  const hasDirectAccess = chapter.subject.userSubjectAccess.length > 0
  const hasSubscriptionAccess = chapter.subject.planSubjects.some(
    ps => ps.plan.userSubscriptions.length > 0
  )

  return hasDirectAccess || hasSubscriptionAccess
}

const verifySubjectAccess = async (userId, subjectId) => {
  const subject = await prisma.subject.findFirst({
    where: {
      id: subjectId,
      isActive: true
    },
    include: {
      userSubjectAccess: {
        where: {
          userId,
          isActive: true
        }
      },
      planSubjects: {
        include: {
          plan: {
            include: {
              userSubscriptions: {
                where: {
                  userId,
                  isActive: true,
                  endDate: {
                    gte: new Date()
                  }
                }
              }
            }
          }
        }
      }
    }
  })

  if (!subject) return false

  const hasDirectAccess = subject.userSubjectAccess.length > 0
  const hasSubscriptionAccess = subject.planSubjects.some(
    ps => ps.plan.userSubscriptions.length > 0
  )

  return hasDirectAccess || hasSubscriptionAccess
}

const getUserAccessibleSubjects = async (userId) => {
  // Get subjects with direct access
  const directAccess = await prisma.userSubjectAccess.findMany({
    where: {
      userId,
      isActive: true
    },
    select: {
      subjectId: true
    }
  })

  // Get subjects through subscriptions
  const subscriptionAccess = await prisma.userSubscription.findMany({
    where: {
      userId,
      isActive: true,
      endDate: {
        gte: new Date()
      }
    },
    include: {
      plan: {
        include: {
          planSubjects: {
            select: {
              subjectId: true
            }
          }
        }
      }
    }
  })

  const subjectIds = new Set()
  
  // Add direct access subjects
  directAccess.forEach(access => subjectIds.add(access.subjectId))
  
  // Add subscription subjects
  subscriptionAccess.forEach(subscription => {
    subscription.plan.planSubjects.forEach(ps => {
      subjectIds.add(ps.subjectId)
    })
  })

  return Array.from(subjectIds)
}
