import prisma from '../../lib/prisma.js'
import { updateChapterProgressSchema } from '../../types/schemas.js'

// Get user's progress for a specific chapter
export const getChapterProgress = async (request, reply) => {
  try {
    const { id: chapterId } = request.params
    const userId = request.user.id

    // Get or create chapter progress
    let progress = await prisma.chapterProgress.findUnique({
      where: {
        userId_chapterId: {
          userId,
          chapterId
        }
      },
      include: {
        chapter: {
          select: {
            id: true,
            title: true,
            order: true
          }
        }
      }
    })

    if (!progress) {
      // Create initial progress record
      progress = await prisma.chapterProgress.create({
        data: {
          userId,
          chapterId,
          readingProgress: 0,
          timeSpent: 0,
          lastPosition: 0,
          isCompleted: false
        },
        include: {
          chapter: {
            select: {
              id: true,
              title: true,
              order: true
            }
          }
        }
      })
    }

    // Get reading sessions for this chapter
    const sessions = await prisma.readingSession.findMany({
      where: {
        userId,
        chapterId
      },
      orderBy: {
        startTime: 'desc'
      },
      take: 5
    })

    reply.send({
      success: true,
      data: {
        progress,
        recentSessions: sessions
      }
    })
  } catch (error) {
    console.error('Error getting chapter progress:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to get chapter progress'
    })
  }
}

// Start a new reading session
export const startReadingSession = async (request, reply) => {
  try {
    const { id: chapterId } = request.params
    const userId = request.user.id

    // Verify chapter exists and user has access
    const chapter = await prisma.chapter.findFirst({
      where: {
        id: chapterId,
        isActive: true
      },
      include: {
        subject: {
          include: {
            userSubjectAccess: {
              where: {
                userId,
                isActive: true
              }
            },
            planSubjects: {
              include: {
                plan: {
                  include: {
                    userSubscriptions: {
                      where: {
                        userId,
                        isActive: true,
                        endDate: {
                          gte: new Date()
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!chapter) {
      return reply.status(404).send({
        success: false,
        message: 'Chapter not found'
      })
    }

    // Check access permissions
    const hasDirectAccess = chapter.subject.userSubjectAccess.length > 0
    const hasSubscriptionAccess = chapter.subject.planSubjects.some(
      ps => ps.plan.userSubscriptions.length > 0
    )

    if (!hasDirectAccess && !hasSubscriptionAccess) {
      return reply.status(403).send({
        success: false,
        message: 'Access denied. Please purchase this subject or subscribe to a plan.'
      })
    }

    // Create reading session
    const session = await prisma.readingSession.create({
      data: {
        userId,
        chapterId,
        startTime: new Date()
      }
    })

    reply.send({
      success: true,
      data: {
        sessionId: session.id,
        startTime: session.startTime
      }
    })
  } catch (error) {
    console.error('Error starting reading session:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to start reading session'
    })
  }
}

// Update chapter reading progress
export const updateChapterProgress = async (request, reply) => {
  try {
    const { id: chapterId } = request.params
    const userId = request.user.id
    
    // Validate request body
    const validation = updateChapterProgressSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid request data',
        errors: validation.error.errors
      })
    }

    const { readingProgress, timeSpent, lastPosition, isCompleted } = validation.data

    // Update or create progress
    const progress = await prisma.chapterProgress.upsert({
      where: {
        userId_chapterId: {
          userId,
          chapterId
        }
      },
      update: {
        readingProgress,
        timeSpent,
        lastPosition,
        isCompleted: isCompleted || readingProgress >= 100,
        completedAt: (isCompleted || readingProgress >= 100) ? new Date() : null,
        updatedAt: new Date()
      },
      create: {
        userId,
        chapterId,
        readingProgress,
        timeSpent,
        lastPosition,
        isCompleted: isCompleted || readingProgress >= 100,
        completedAt: (isCompleted || readingProgress >= 100) ? new Date() : null
      }
    })

    // If chapter is completed, check for achievements
    if (progress.isCompleted) {
      // Award completion achievement (implement later)
      // await awardChapterCompletionAchievement(userId, chapterId)
    }

    reply.send({
      success: true,
      data: progress
    })
  } catch (error) {
    console.error('Error updating chapter progress:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to update chapter progress'
    })
  }
}

// Complete chapter and end reading session
export const completeChapter = async (request, reply) => {
  try {
    const { id: chapterId } = request.params
    const { sessionId } = request.body
    const userId = request.user.id

    // End reading session if provided
    if (sessionId) {
      const session = await prisma.readingSession.findFirst({
        where: {
          id: sessionId,
          userId,
          chapterId,
          endTime: null
        }
      })

      if (session) {
        const endTime = new Date()
        const duration = Math.floor((endTime - session.startTime) / 1000)

        await prisma.readingSession.update({
          where: { id: sessionId },
          data: {
            endTime,
            duration
          }
        })
      }
    }

    // Mark chapter as completed
    const progress = await prisma.chapterProgress.upsert({
      where: {
        userId_chapterId: {
          userId,
          chapterId
        }
      },
      update: {
        isCompleted: true,
        readingProgress: 100,
        completedAt: new Date(),
        updatedAt: new Date()
      },
      create: {
        userId,
        chapterId,
        isCompleted: true,
        readingProgress: 100,
        completedAt: new Date()
      }
    })

    // Get next chapter in sequence
    const currentChapter = await prisma.chapter.findUnique({
      where: { id: chapterId },
      select: { order: true, subjectId: true }
    })

    const nextChapter = await prisma.chapter.findFirst({
      where: {
        subjectId: currentChapter.subjectId,
        order: {
          gt: currentChapter.order
        },
        isActive: true
      },
      orderBy: {
        order: 'asc'
      },
      select: {
        id: true,
        title: true,
        order: true
      }
    })

    reply.send({
      success: true,
      data: {
        progress,
        nextChapter,
        message: 'Chapter completed successfully!'
      }
    })
  } catch (error) {
    console.error('Error completing chapter:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to complete chapter'
    })
  }
}

// Get user's overall progress for a subject
export const getSubjectProgress = async (request, reply) => {
  try {
    const { subjectId } = request.params
    const userId = request.user.id

    // Get all chapters in subject
    const chapters = await prisma.chapter.findMany({
      where: {
        subjectId,
        isActive: true
      },
      include: {
        chapterProgress: {
          where: {
            userId
          }
        }
      },
      orderBy: {
        order: 'asc'
      }
    })

    const totalChapters = chapters.length
    const completedChapters = chapters.filter(
      chapter => chapter.chapterProgress[0]?.isCompleted
    ).length

    const overallProgress = totalChapters > 0 
      ? Math.round((completedChapters / totalChapters) * 100)
      : 0

    const totalTimeSpent = chapters.reduce((total, chapter) => {
      return total + (chapter.chapterProgress[0]?.timeSpent || 0)
    }, 0)

    reply.send({
      success: true,
      data: {
        subjectId,
        totalChapters,
        completedChapters,
        overallProgress,
        totalTimeSpent,
        chapters: chapters.map(chapter => ({
          id: chapter.id,
          title: chapter.title,
          order: chapter.order,
          isCompleted: chapter.chapterProgress[0]?.isCompleted || false,
          readingProgress: chapter.chapterProgress[0]?.readingProgress || 0,
          timeSpent: chapter.chapterProgress[0]?.timeSpent || 0
        }))
      }
    })
  } catch (error) {
    console.error('Error getting subject progress:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to get subject progress'
    })
  }
}
