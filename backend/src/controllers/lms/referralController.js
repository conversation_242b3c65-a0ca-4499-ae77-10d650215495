import prisma from '../../lib/prisma.js'
import { 
  generateReferralCodeSchema,
  applyReferralCodeSchema
} from '../../types/schemas.js'

// Generate unique referral code
const generateUniqueCode = async (customCode = null) => {
  if (customCode) {
    // Check if custom code is available
    const existing = await prisma.user.findFirst({
      where: { referralCode: customCode }
    })
    if (existing) {
      throw new Error('Referral code already exists')
    }
    return customCode
  }

  // Generate random code
  let attempts = 0
  while (attempts < 10) {
    const code = Math.random().toString(36).substring(2, 8).toUpperCase()
    const existing = await prisma.user.findFirst({
      where: { referralCode: code }
    })
    if (!existing) {
      return code
    }
    attempts++
  }
  
  // Fallback with timestamp
  return `REF${Date.now().toString().slice(-6)}`
}

// Generate referral code for user
export const generateReferralCode = async (request, reply) => {
  try {
    const userId = request.user.id
    
    const validation = generateReferralCodeSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid request data',
        errors: validation.error.errors
      })
    }

    const { customCode } = validation.data

    // Check if user already has a referral code
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { referralCode: true, name: true }
    })

    if (user.referralCode) {
      return reply.status(400).send({
        success: false,
        message: 'User already has a referral code',
        data: { referralCode: user.referralCode }
      })
    }

    try {
      const referralCode = await generateUniqueCode(customCode)
      
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { referralCode }
      })

      reply.send({
        success: true,
        data: {
          referralCode,
          shareMessage: `Join LearnLegend with my referral code ${referralCode} and get 10 bonus points! 🎓`,
          shareUrl: `${process.env.FRONTEND_URL}/signup?ref=${referralCode}`
        }
      })
    } catch (error) {
      if (error.message === 'Referral code already exists') {
        return reply.status(400).send({
          success: false,
          message: 'Referral code already exists. Please try a different code.'
        })
      }
      throw error
    }
  } catch (error) {
    console.error('Error generating referral code:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to generate referral code'
    })
  }
}

// Apply referral code during signup
export const applyReferralCode = async (request, reply) => {
  try {
    const userId = request.user.id
    
    const validation = applyReferralCodeSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid request data',
        errors: validation.error.errors
      })
    }

    const { referralCode } = validation.data

    // Check if user already used a referral code
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { referredBy: true, pointBalance: true }
    })

    if (currentUser.referredBy) {
      return reply.status(400).send({
        success: false,
        message: 'User has already used a referral code'
      })
    }

    // Find referrer by code
    const referrer = await prisma.user.findFirst({
      where: { referralCode },
      select: { id: true, name: true, pointBalance: true }
    })

    if (!referrer) {
      return reply.status(404).send({
        success: false,
        message: 'Invalid referral code'
      })
    }

    if (referrer.id === userId) {
      return reply.status(400).send({
        success: false,
        message: 'Cannot use your own referral code'
      })
    }

    // Check if referral already exists
    const existingReferral = await prisma.referral.findFirst({
      where: {
        referrerUserId: referrer.id,
        referredUserId: userId
      }
    })

    if (existingReferral) {
      return reply.status(400).send({
        success: false,
        message: 'Referral already exists'
      })
    }

    const signupReward = 10 // Points for signup

    // Create referral and update both users
    const [referral, updatedReferrer, updatedReferred, referrerTransaction, referredTransaction] = await prisma.$transaction([
      // Create referral record
      prisma.referral.create({
        data: {
          referrerUserId: referrer.id,
          referredUserId: userId,
          referralCode,
          status: 'completed',
          signupReward,
          rewardedAt: new Date()
        }
      }),
      // Update referrer points
      prisma.user.update({
        where: { id: referrer.id },
        data: {
          pointBalance: { increment: signupReward },
          totalPointsEarned: { increment: signupReward }
        }
      }),
      // Update referred user
      prisma.user.update({
        where: { id: userId },
        data: {
          referredBy: referralCode,
          pointBalance: { increment: signupReward },
          totalPointsEarned: { increment: signupReward }
        }
      }),
      // Create transaction for referrer
      prisma.pointTransaction.create({
        data: {
          userId: referrer.id,
          type: 'earned',
          category: 'referral',
          amount: signupReward,
          description: `Referral signup bonus from ${request.user.name}`,
          metadata: JSON.stringify({ 
            referredUserId: userId, 
            referredUserName: request.user.name,
            type: 'signup_bonus'
          })
        }
      }),
      // Create transaction for referred user
      prisma.pointTransaction.create({
        data: {
          userId,
          type: 'earned',
          category: 'referral',
          amount: signupReward,
          description: `Welcome bonus for using referral code ${referralCode}`,
          metadata: JSON.stringify({ 
            referrerUserId: referrer.id, 
            referrerName: referrer.name,
            referralCode,
            type: 'welcome_bonus'
          })
        }
      })
    ])

    reply.send({
      success: true,
      data: {
        referral: {
          id: referral.id,
          referrerName: referrer.name,
          signupReward,
          status: 'completed'
        },
        pointsEarned: signupReward,
        totalPoints: updatedReferred.pointBalance,
        message: `Welcome! You've earned ${signupReward} points for using ${referrer.name}'s referral code.`
      }
    })
  } catch (error) {
    console.error('Error applying referral code:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to apply referral code'
    })
  }
}

// Process referral purchase reward
export const processReferralPurchaseReward = async (userId, purchaseAmount, purchaseType) => {
  try {
    // Find if user was referred and hasn't received purchase reward yet
    const referral = await prisma.referral.findFirst({
      where: {
        referredUserId: userId,
        isFirstPurchase: false
      },
      include: {
        referrer: {
          select: { id: true, name: true, pointBalance: true }
        }
      }
    })

    if (!referral) {
      return null // User wasn't referred or already received purchase reward
    }

    // Calculate referrer reward (50% of purchase amount as points)
    const referrerReward = Math.floor(purchaseAmount * 0.5)

    // Update referral and referrer points
    const [updatedReferral, updatedReferrer, transaction] = await prisma.$transaction([
      // Mark referral as first purchase completed
      prisma.referral.update({
        where: { id: referral.id },
        data: {
          isFirstPurchase: true,
          purchaseReward: 0.5 // 50%
        }
      }),
      // Update referrer points
      prisma.user.update({
        where: { id: referral.referrer.id },
        data: {
          pointBalance: { increment: referrerReward },
          totalPointsEarned: { increment: referrerReward }
        }
      }),
      // Create transaction for referrer
      prisma.pointTransaction.create({
        data: {
          userId: referral.referrer.id,
          type: 'earned',
          category: 'referral',
          amount: referrerReward,
          description: `Referral purchase bonus: 50% of ${purchaseType} purchase`,
          metadata: JSON.stringify({ 
            referredUserId: userId,
            purchaseAmount,
            purchaseType,
            rewardPercentage: 0.5,
            type: 'purchase_bonus'
          })
        }
      })
    ])

    return {
      referrerReward,
      referrerName: referral.referrer.name,
      transaction
    }
  } catch (error) {
    console.error('Error processing referral purchase reward:', error)
    return null
  }
}

// Get user's referral stats
export const getReferralStats = async (request, reply) => {
  try {
    const userId = request.user.id

    const [user, referrals, referredBy] = await Promise.all([
      prisma.user.findUnique({
        where: { id: userId },
        select: { 
          referralCode: true, 
          referredBy: true,
          pointBalance: true,
          totalPointsEarned: true
        }
      }),
      prisma.referral.findMany({
        where: { referrerUserId: userId },
        include: {
          referred: {
            select: { name: true, createdAt: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.referral.findFirst({
        where: { referredUserId: userId },
        include: {
          referrer: {
            select: { name: true, referralCode: true }
          }
        }
      })
    ])

    // Calculate referral earnings
    const referralEarnings = await prisma.pointTransaction.aggregate({
      where: {
        userId,
        category: 'referral'
      },
      _sum: {
        amount: true
      }
    })

    reply.send({
      success: true,
      data: {
        user: {
          referralCode: user.referralCode,
          referredBy: user.referredBy,
          pointBalance: user.pointBalance,
          totalPointsEarned: user.totalPointsEarned
        },
        referralStats: {
          totalReferrals: referrals.length,
          totalEarnings: referralEarnings._sum.amount || 0,
          shareUrl: user.referralCode ? `${process.env.FRONTEND_URL}/signup?ref=${user.referralCode}` : null
        },
        referrals: referrals.map(ref => ({
          id: ref.id,
          referredUserName: ref.referred.name,
          status: ref.status,
          signupReward: ref.signupReward,
          isFirstPurchase: ref.isFirstPurchase,
          createdAt: ref.createdAt
        })),
        referredBy: referredBy ? {
          referrerName: referredBy.referrer.name,
          referralCode: referredBy.referrer.referralCode,
          signupReward: referredBy.signupReward,
          createdAt: referredBy.createdAt
        } : null
      }
    })
  } catch (error) {
    console.error('Error getting referral stats:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to get referral stats'
    })
  }
}
