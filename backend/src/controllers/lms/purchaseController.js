import prisma from '../../lib/prisma.js'
import { redeemPointsSchema } from '../../types/schemas.js'
import { processReferralPurchaseReward } from './referralController.js'

// Purchase subscription with points redemption
export const purchaseSubscription = async (request, reply) => {
  try {
    const userId = request.user.id
    const { planId, pointsToUse = 0 } = request.body

    // Validate points usage if provided
    if (pointsToUse > 0) {
      const validation = redeemPointsSchema.safeParse({
        pointsToUse,
        purchaseType: 'subscription',
        itemId: planId
      })
      
      if (!validation.success) {
        return reply.status(400).send({
          success: false,
          message: 'Invalid points redemption data',
          errors: validation.error.errors
        })
      }
    }

    // Get user and plan details
    const [user, plan] = await Promise.all([
      prisma.user.findUnique({
        where: { id: userId },
        select: { pointBalance: true, name: true }
      }),
      prisma.subscriptionPlan.findUnique({
        where: { id: planId },
        select: { price: true, duration: true, name: true, isActive: true }
      })
    ])

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'User not found'
      })
    }

    if (!plan || !plan.isActive) {
      return reply.status(404).send({
        success: false,
        message: 'Subscription plan not found or inactive'
      })
    }

    // Check if user already has active subscription for this plan
    const existingSubscription = await prisma.userSubscription.findFirst({
      where: {
        userId,
        planId,
        isActive: true,
        endDate: { gt: new Date() }
      }
    })

    if (existingSubscription) {
      return reply.status(400).send({
        success: false,
        message: 'You already have an active subscription for this plan'
      })
    }

    // Validate points if being used
    if (pointsToUse > 0) {
      if (user.pointBalance < pointsToUse) {
        return reply.status(400).send({
          success: false,
          message: 'Insufficient points',
          data: {
            availablePoints: user.pointBalance,
            requestedPoints: pointsToUse
          }
        })
      }
    }

    // Calculate final price
    const discountAmount = Math.min(pointsToUse, plan.price)
    const finalPrice = Math.max(0, plan.price - discountAmount)

    // Calculate subscription dates
    const startDate = new Date()
    const endDate = new Date(startDate)
    endDate.setDate(endDate.getDate() + plan.duration)

    // Create subscription and handle points/referrals in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create subscription
      const subscription = await tx.userSubscription.create({
        data: {
          userId,
          planId,
          startDate,
          endDate,
          pointsUsed: discountAmount,
          finalPrice,
          isActive: true
        },
        include: {
          plan: {
            select: { name: true, duration: true }
          }
        }
      })

      // Deduct points if used
      let pointTransaction = null
      if (discountAmount > 0) {
        await tx.user.update({
          where: { id: userId },
          data: {
            pointBalance: { decrement: discountAmount }
          }
        })

        // Create point transaction
        pointTransaction = await tx.pointTransaction.create({
          data: {
            userId,
            type: 'spent',
            category: 'subscription_discount',
            amount: -discountAmount,
            description: `Points used for ${plan.name} subscription`,
            metadata: JSON.stringify({
              subscriptionId: subscription.id,
              planName: plan.name,
              originalPrice: plan.price,
              finalPrice
            })
          }
        })
      }

      return { subscription, pointTransaction }
    })

    // Process referral reward (outside transaction to avoid conflicts)
    let referralReward = null
    if (finalPrice > 0) {
      referralReward = await processReferralPurchaseReward(userId, finalPrice, 'subscription')
    }

    reply.send({
      success: true,
      data: {
        subscription: result.subscription,
        purchase: {
          originalPrice: plan.price,
          pointsUsed: discountAmount,
          finalPrice,
          savings: plan.price - finalPrice
        },
        pointTransaction: result.pointTransaction,
        referralReward: referralReward ? {
          referrerName: referralReward.referrerName,
          referrerReward: referralReward.referrerReward
        } : null,
        message: `Successfully purchased ${plan.name} subscription!`
      }
    })
  } catch (error) {
    console.error('Error purchasing subscription:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to purchase subscription'
    })
  }
}

// Purchase individual subject with points redemption
export const purchaseSubject = async (request, reply) => {
  try {
    const userId = request.user.id
    const { subjectId, pointsToUse = 0 } = request.body

    // Validate points usage if provided
    if (pointsToUse > 0) {
      const validation = redeemPointsSchema.safeParse({
        pointsToUse,
        purchaseType: 'subject',
        itemId: subjectId
      })
      
      if (!validation.success) {
        return reply.status(400).send({
          success: false,
          message: 'Invalid points redemption data',
          errors: validation.error.errors
        })
      }
    }

    // Get user, subject, and pricing details
    const [user, subject, pricing] = await Promise.all([
      prisma.user.findUnique({
        where: { id: userId },
        select: { pointBalance: true, name: true }
      }),
      prisma.subject.findUnique({
        where: { id: subjectId },
        select: { name: true, isActive: true }
      }),
      prisma.subjectPricing.findFirst({
        where: { subjectId, isActive: true },
        select: { price: true }
      })
    ])

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'User not found'
      })
    }

    if (!subject || !subject.isActive) {
      return reply.status(404).send({
        success: false,
        message: 'Subject not found or inactive'
      })
    }

    if (!pricing) {
      return reply.status(404).send({
        success: false,
        message: 'Subject pricing not found'
      })
    }

    // Check if user already has access to this subject
    const existingAccess = await prisma.userSubjectAccess.findFirst({
      where: {
        userId,
        subjectId,
        isActive: true
      }
    })

    if (existingAccess) {
      return reply.status(400).send({
        success: false,
        message: 'You already have access to this subject'
      })
    }

    // Validate points if being used
    if (pointsToUse > 0) {
      if (user.pointBalance < pointsToUse) {
        return reply.status(400).send({
          success: false,
          message: 'Insufficient points',
          data: {
            availablePoints: user.pointBalance,
            requestedPoints: pointsToUse
          }
        })
      }
    }

    // Calculate final price
    const discountAmount = Math.min(pointsToUse, pricing.price)
    const finalPrice = Math.max(0, pricing.price - discountAmount)

    // Create subject access and handle points/referrals in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create subject access
      const subjectAccess = await tx.userSubjectAccess.create({
        data: {
          userId,
          subjectId,
          pointsUsed: discountAmount,
          finalPrice,
          isActive: true
        },
        include: {
          subject: {
            select: { name: true }
          }
        }
      })

      // Deduct points if used
      let pointTransaction = null
      if (discountAmount > 0) {
        await tx.user.update({
          where: { id: userId },
          data: {
            pointBalance: { decrement: discountAmount }
          }
        })

        // Create point transaction
        pointTransaction = await tx.pointTransaction.create({
          data: {
            userId,
            type: 'spent',
            category: 'subject_discount',
            amount: -discountAmount,
            description: `Points used for ${subject.name} subject`,
            metadata: JSON.stringify({
              subjectAccessId: subjectAccess.id,
              subjectName: subject.name,
              originalPrice: pricing.price,
              finalPrice
            })
          }
        })
      }

      return { subjectAccess, pointTransaction }
    })

    // Process referral reward (outside transaction to avoid conflicts)
    let referralReward = null
    if (finalPrice > 0) {
      referralReward = await processReferralPurchaseReward(userId, finalPrice, 'subject')
    }

    reply.send({
      success: true,
      data: {
        subjectAccess: result.subjectAccess,
        purchase: {
          originalPrice: pricing.price,
          pointsUsed: discountAmount,
          finalPrice,
          savings: pricing.price - finalPrice
        },
        pointTransaction: result.pointTransaction,
        referralReward: referralReward ? {
          referrerName: referralReward.referrerName,
          referrerReward: referralReward.referrerReward
        } : null,
        message: `Successfully purchased access to ${subject.name}!`
      }
    })
  } catch (error) {
    console.error('Error purchasing subject:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to purchase subject'
    })
  }
}

// Get purchase history for user
export const getPurchaseHistory = async (request, reply) => {
  try {
    const userId = request.user.id
    const { page = 1, limit = 20, type } = request.query

    const [subscriptions, subjectAccess] = await Promise.all([
      type === 'subject' ? [] : prisma.userSubscription.findMany({
        where: { userId },
        include: {
          plan: {
            select: { name: true, duration: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: type ? 0 : (page - 1) * Math.floor(limit / 2),
        take: type ? 0 : Math.floor(limit / 2)
      }),
      type === 'subscription' ? [] : prisma.userSubjectAccess.findMany({
        where: { userId },
        include: {
          subject: {
            select: { name: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: type ? (page - 1) * limit : 0,
        take: type ? limit : Math.ceil(limit / 2)
      })
    ])

    // Combine and sort by date
    const purchases = [
      ...subscriptions.map(sub => ({
        id: sub.id,
        type: 'subscription',
        name: sub.plan.name,
        originalPrice: sub.plan.price || 0,
        pointsUsed: sub.pointsUsed,
        finalPrice: sub.finalPrice,
        purchaseDate: sub.createdAt,
        status: sub.isActive && sub.endDate > new Date() ? 'active' : 'expired',
        endDate: sub.endDate
      })),
      ...subjectAccess.map(access => ({
        id: access.id,
        type: 'subject',
        name: access.subject.name,
        originalPrice: 0, // We'd need to join with pricing
        pointsUsed: access.pointsUsed,
        finalPrice: access.finalPrice,
        purchaseDate: access.createdAt,
        status: access.isActive ? 'active' : 'inactive'
      }))
    ].sort((a, b) => new Date(b.purchaseDate) - new Date(a.purchaseDate))

    reply.send({
      success: true,
      data: {
        purchases,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: purchases.length
        }
      }
    })
  } catch (error) {
    console.error('Error getting purchase history:', error)
    reply.status(500).send({
      success: false,
      message: 'Failed to get purchase history'
    })
  }
}
