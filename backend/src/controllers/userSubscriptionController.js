import { PrismaClient } from '@prisma/client'
import { createUserSubscriptionSchema, updateUserSubscriptionSchema, paginationSchema, searchSchema } from '../types/schemas.js'
import { successResponse, errorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

const prisma = new PrismaClient()

export const userSubscriptionController = {
  // Get all user subscriptions with pagination and filtering
  async getAll(request, reply) {
    try {
      const { page, limit } = paginationSchema.parse(request.query)
      const { search } = searchSchema.parse(request.query)
      const { userId, planId, isActive, status } = request.query
      const skip = (page - 1) * limit

      const where = {
        ...(search && {
          OR: [
            { user: { name: { contains: search, mode: 'insensitive' } } },
            { user: { email: { contains: search, mode: 'insensitive' } } },
            { plan: { name: { contains: search, mode: 'insensitive' } } }
          ]
        }),
        ...(userId && { userId }),
        ...(planId && { planId }),
        ...(isActive !== undefined && { isActive: isActive === 'true' }),
        ...(status === 'active' && {
          isActive: true,
          endDate: { gt: new Date() }
        }),
        ...(status === 'expired' && {
          OR: [
            { isActive: false },
            { endDate: { lte: new Date() } }
          ]
        })
      }

      const [subscriptions, total] = await Promise.all([
        prisma.userSubscription.findMany({
          where,
          include: {
            user: true,
            plan: true
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.userSubscription.count({ where })
      ])

      const pagination = getPaginationMeta(total, page, limit)

      return successResponse(reply, {
        subscriptions,
        pagination
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get user subscription by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const subscription = await prisma.userSubscription.findUnique({
        where: { id },
        include: {
          user: true,
          plan: true
        }
      })

      if (!subscription) {
        return errorResponse(reply, 'User subscription not found', 404)
      }

      // Add computed status
      const now = new Date()
      const status = subscription.isActive && subscription.endDate > now ? 'active' : 'expired'

      return successResponse(reply, { 
        subscription: {
          ...subscription,
          status
        }
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Create new user subscription
  async create(request, reply) {
    try {
      const data = createUserSubscriptionSchema.parse(request.body)

      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { id: data.userId }
      })

      if (!user) {
        return errorResponse(reply, 'User not found', 404)
      }

      // Check if plan exists
      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id: data.planId }
      })

      if (!plan) {
        return errorResponse(reply, 'Subscription plan not found', 404)
      }

      // Check if user already has an active subscription for this plan
      const existingSubscription = await prisma.userSubscription.findFirst({
        where: {
          userId: data.userId,
          planId: data.planId,
          isActive: true,
          endDate: { gt: new Date() }
        }
      })

      if (existingSubscription) {
        return errorResponse(reply, 'User already has an active subscription for this plan', 400)
      }

      // Calculate end date based on plan duration
      const startDate = data.startDate ? new Date(data.startDate) : new Date()
      const endDate = new Date(startDate)
      endDate.setDate(endDate.getDate() + plan.duration)

      const subscription = await prisma.userSubscription.create({
        data: {
          ...data,
          startDate,
          endDate
        },
        include: {
          user: true,
          plan: true
        }
      })

      return successResponse(reply, { subscription }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Update user subscription
  async update(request, reply) {
    try {
      const { id } = request.params
      const data = updateUserSubscriptionSchema.parse(request.body)

      // Check if subscription exists
      const existingSubscription = await prisma.userSubscription.findUnique({
        where: { id },
        include: { plan: true }
      })

      if (!existingSubscription) {
        return errorResponse(reply, 'User subscription not found', 404)
      }

      // If userId is being updated, check if the new user exists
      if (data.userId && data.userId !== existingSubscription.userId) {
        const user = await prisma.user.findUnique({
          where: { id: data.userId }
        })

        if (!user) {
          return errorResponse(reply, 'User not found', 404)
        }
      }

      // If planId is being updated, check if the new plan exists
      if (data.planId && data.planId !== existingSubscription.planId) {
        const plan = await prisma.subscriptionPlan.findUnique({
          where: { id: data.planId }
        })

        if (!plan) {
          return errorResponse(reply, 'Subscription plan not found', 404)
        }

        // Recalculate end date if plan changed
        if (data.startDate || !data.endDate) {
          const startDate = data.startDate ? new Date(data.startDate) : existingSubscription.startDate
          const endDate = new Date(startDate)
          endDate.setDate(endDate.getDate() + plan.duration)
          data.endDate = endDate
        }
      }

      const subscription = await prisma.userSubscription.update({
        where: { id },
        data,
        include: {
          user: true,
          plan: true
        }
      })

      return successResponse(reply, { subscription })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Delete user subscription
  async delete(request, reply) {
    try {
      const { id } = request.params

      const subscription = await prisma.userSubscription.findUnique({
        where: { id }
      })

      if (!subscription) {
        return errorResponse(reply, 'User subscription not found', 404)
      }

      await prisma.userSubscription.delete({
        where: { id }
      })

      return successResponse(reply, { message: 'User subscription deleted successfully' })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Extend subscription
  async extend(request, reply) {
    try {
      const { id } = request.params
      const { days } = request.body

      if (!days || days <= 0) {
        return errorResponse(reply, 'Days must be a positive number', 400)
      }

      const subscription = await prisma.userSubscription.findUnique({
        where: { id }
      })

      if (!subscription) {
        return errorResponse(reply, 'User subscription not found', 404)
      }

      const newEndDate = new Date(subscription.endDate)
      newEndDate.setDate(newEndDate.getDate() + days)

      const updatedSubscription = await prisma.userSubscription.update({
        where: { id },
        data: {
          endDate: newEndDate,
          isActive: true
        },
        include: {
          user: true,
          plan: true
        }
      })

      return successResponse(reply, { subscription: updatedSubscription })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Cancel subscription
  async cancel(request, reply) {
    try {
      const { id } = request.params

      const subscription = await prisma.userSubscription.findUnique({
        where: { id }
      })

      if (!subscription) {
        return errorResponse(reply, 'User subscription not found', 404)
      }

      const updatedSubscription = await prisma.userSubscription.update({
        where: { id },
        data: { isActive: false },
        include: {
          user: true,
          plan: true
        }
      })

      return successResponse(reply, { subscription: updatedSubscription })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get subscription statistics
  async getStats(request, reply) {
    try {
      const now = new Date()

      const [total, active, expired, revenue] = await Promise.all([
        prisma.userSubscription.count(),
        prisma.userSubscription.count({
          where: {
            isActive: true,
            endDate: { gt: now }
          }
        }),
        prisma.userSubscription.count({
          where: {
            OR: [
              { isActive: false },
              { endDate: { lte: now } }
            ]
          }
        }),
        prisma.userSubscription.aggregate({
          where: {
            isActive: true,
            endDate: { gt: now }
          },
          _sum: {
            plan: {
              price: true
            }
          }
        })
      ])

      const stats = {
        total,
        active,
        expired,
        revenue: revenue._sum?.price || 0
      }

      return successResponse(reply, { stats })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  }
}
