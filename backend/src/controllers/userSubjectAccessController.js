import { prisma } from '../lib/prisma.js'
import { 
  createUserSubjectAccessSchema, 
  updateUserSubjectAccessSchema,
  paginationSchema,
  searchSchema
} from '../types/schemas.js'
import { successResponse, errorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

export const userSubjectAccessController = {
  // Get all user subject access with pagination and filtering
  async getAll(request, reply) {
    try {
      const { page, limit } = paginationSchema.parse(request.query)
      const { search, isActive } = searchSchema.parse(request.query)
      const { userId, subjectId, accessType } = request.query
      const skip = (page - 1) * limit

      const where = {
        ...(isActive !== undefined && { isActive: isActive === 'true' }),
        ...(userId && { userId }),
        ...(subjectId && { subjectId }),
        ...(accessType && { accessType }),
        ...(search && {
          OR: [
            { user: { name: { contains: search, mode: 'insensitive' } } },
            { user: { email: { contains: search, mode: 'insensitive' } } },
            { subject: { name: { contains: search, mode: 'insensitive' } } },
            { subject: { domain: { name: { contains: search, mode: 'insensitive' } } } }
          ]
        })
      }

      const [accesses, total] = await Promise.all([
        prisma.userSubjectAccess.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true
              }
            },
            subject: {
              include: {
                domain: true,
                _count: {
                  select: {
                    chapters: true
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.userSubjectAccess.count({ where })
      ])

      const pagination = getPaginationMeta(total, page, limit)

      return successResponse(reply, {
        accesses,
        pagination
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get user subject access by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const access = await prisma.userSubjectAccess.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          },
          subject: {
            include: {
              domain: true,
              chapters: {
                select: {
                  id: true,
                  title: true,
                  description: true
                }
              }
            }
          }
        }
      })

      if (!access) {
        return errorResponse(reply, 'User subject access not found', 404)
      }

      return successResponse(reply, { access })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get access by user ID
  async getByUserId(request, reply) {
    try {
      const { userId } = request.params
      const { isActive, accessType } = request.query

      const where = {
        userId,
        ...(isActive !== undefined && { isActive: isActive === 'true' }),
        ...(accessType && { accessType })
      }

      const accesses = await prisma.userSubjectAccess.findMany({
        where,
        include: {
          subject: {
            include: {
              domain: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      return successResponse(reply, { accesses })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Check if user has access to subject
  async checkAccess(request, reply) {
    try {
      const { userId, subjectId } = request.params

      // Check direct subject access
      const directAccess = await prisma.userSubjectAccess.findFirst({
        where: {
          userId,
          subjectId,
          isActive: true,
          OR: [
            { endDate: null }, // Permanent access
            { endDate: { gt: new Date() } } // Not expired
          ]
        }
      })

      if (directAccess) {
        return successResponse(reply, { 
          hasAccess: true, 
          accessType: directAccess.accessType,
          source: 'direct'
        })
      }

      // Check subscription-based access
      const subscriptionAccess = await prisma.userSubscription.findFirst({
        where: {
          userId,
          isActive: true,
          endDate: { gt: new Date() },
          plan: {
            planSubjects: {
              some: {
                subjectId
              }
            }
          }
        },
        include: {
          plan: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      if (subscriptionAccess) {
        return successResponse(reply, { 
          hasAccess: true, 
          accessType: 'SUBSCRIPTION',
          source: 'subscription',
          plan: subscriptionAccess.plan
        })
      }

      return successResponse(reply, { 
        hasAccess: false,
        accessType: null,
        source: null
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Create user subject access
  async create(request, reply) {
    try {
      const data = createUserSubjectAccessSchema.parse(request.body)
      const adminId = request.admin?.adminId

      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { id: data.userId }
      })

      if (!user) {
        return errorResponse(reply, 'User not found', 404)
      }

      // Check if subject exists
      const subject = await prisma.subject.findUnique({
        where: { id: data.subjectId }
      })

      if (!subject) {
        return errorResponse(reply, 'Subject not found', 404)
      }

      // Check if access already exists
      const existingAccess = await prisma.userSubjectAccess.findUnique({
        where: {
          userId_subjectId: {
            userId: data.userId,
            subjectId: data.subjectId
          }
        }
      })

      if (existingAccess) {
        return errorResponse(reply, 'User already has access to this subject', 400)
      }

      const access = await prisma.userSubjectAccess.create({
        data: {
          ...data,
          createdBy: adminId
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          subject: {
            include: {
              domain: true
            }
          }
        }
      })

      return successResponse(reply, { access }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Update user subject access
  async update(request, reply) {
    try {
      const { id } = request.params
      const data = updateUserSubjectAccessSchema.parse(request.body)

      // Check if access exists
      const existingAccess = await prisma.userSubjectAccess.findUnique({
        where: { id }
      })

      if (!existingAccess) {
        return errorResponse(reply, 'User subject access not found', 404)
      }

      const access = await prisma.userSubjectAccess.update({
        where: { id },
        data,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          subject: {
            include: {
              domain: true
            }
          }
        }
      })

      return successResponse(reply, { access })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Delete user subject access
  async delete(request, reply) {
    try {
      const { id } = request.params

      const access = await prisma.userSubjectAccess.findUnique({
        where: { id }
      })

      if (!access) {
        return errorResponse(reply, 'User subject access not found', 404)
      }

      await prisma.userSubjectAccess.delete({
        where: { id }
      })

      return successResponse(reply, { message: 'User subject access deleted successfully' })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Bulk grant access to multiple users for a subject
  async bulkGrant(request, reply) {
    try {
      const { userIds, subjectId, accessType = 'GRANTED', endDate } = request.body
      const adminId = request.admin?.adminId

      if (!Array.isArray(userIds) || userIds.length === 0) {
        return errorResponse(reply, 'User IDs array is required and cannot be empty', 400)
      }

      // Check if subject exists
      const subject = await prisma.subject.findUnique({
        where: { id: subjectId }
      })

      if (!subject) {
        return errorResponse(reply, 'Subject not found', 404)
      }

      // Check if users exist
      const users = await prisma.user.findMany({
        where: { id: { in: userIds } }
      })

      if (users.length !== userIds.length) {
        return errorResponse(reply, 'One or more users not found', 400)
      }

      // Check for existing access
      const existingAccesses = await prisma.userSubjectAccess.findMany({
        where: {
          userId: { in: userIds },
          subjectId
        }
      })

      const existingUserIds = existingAccesses.map(a => a.userId)
      const newUserIds = userIds.filter(id => !existingUserIds.includes(id))

      if (newUserIds.length === 0) {
        return errorResponse(reply, 'All users already have access to this subject', 400)
      }

      // Create access for new users
      const accessData = newUserIds.map(userId => ({
        userId,
        subjectId,
        accessType,
        endDate: endDate ? new Date(endDate) : null,
        createdBy: adminId
      }))

      const createdAccesses = await prisma.userSubjectAccess.createMany({
        data: accessData
      })

      return successResponse(reply, { 
        message: `Access granted to ${createdAccesses.count} users`,
        count: createdAccesses.count,
        skipped: existingUserIds.length
      }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  }
}
