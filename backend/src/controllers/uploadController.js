import path from 'path';
import fs from 'fs/promises';
import sharp from 'sharp';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Allowed image types
const ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

// Image dimensions for different types
const IMAGE_CONFIGS = {
  subject: {
    width: 400,
    height: 300,
    quality: 85
  },
  slider: {
    width: 1200,
    height: 600,
    quality: 90
  }
};

/**
 * Upload and process subject image
 */
export const uploadSubjectImage = async (request, reply) => {
  try {
    const data = await request.file();
    
    if (!data) {
      return reply.status(400).send({
        success: false,
        message: 'No file uploaded'
      });
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(data.mimetype)) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
      });
    }

    // Get file buffer
    const buffer = await data.toBuffer();
    
    // Validate file size
    if (buffer.length > MAX_FILE_SIZE) {
      return reply.status(400).send({
        success: false,
        message: 'File size too large. Maximum size is 5MB.'
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = path.extname(data.filename) || '.jpg';
    const filename = `subject_${timestamp}_${randomString}${extension}`;
    
    // Create upload directory if it doesn't exist
    const uploadDir = path.join(__dirname, '../../uploads/subjects');
    await fs.mkdir(uploadDir, { recursive: true });
    
    const filePath = path.join(uploadDir, filename);
    
    // Process and save image
    const config = IMAGE_CONFIGS.subject;
    await sharp(buffer)
      .resize(config.width, config.height, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: config.quality })
      .toFile(filePath);

    // Return the relative URL path
    const imageUrl = `/uploads/subjects/${filename}`;

    return reply.send({
      success: true,
      message: 'Subject image uploaded successfully',
      data: {
        imageUrl,
        filename,
        originalName: data.filename,
        size: buffer.length,
        dimensions: {
          width: config.width,
          height: config.height
        }
      }
    });

  } catch (error) {
    console.error('Subject image upload error:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to upload subject image',
      error: error.message
    });
  }
};

/**
 * Upload and process slider image
 */
export const uploadSliderImage = async (request, reply) => {
  try {
    const data = await request.file();
    
    if (!data) {
      return reply.status(400).send({
        success: false,
        message: 'No file uploaded'
      });
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(data.mimetype)) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
      });
    }

    // Get file buffer
    const buffer = await data.toBuffer();
    
    // Validate file size
    if (buffer.length > MAX_FILE_SIZE) {
      return reply.status(400).send({
        success: false,
        message: 'File size too large. Maximum size is 5MB.'
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = path.extname(data.filename) || '.jpg';
    const filename = `slider_${timestamp}_${randomString}${extension}`;
    
    // Create upload directory if it doesn't exist
    const uploadDir = path.join(__dirname, '../../uploads/slider');
    await fs.mkdir(uploadDir, { recursive: true });
    
    const filePath = path.join(uploadDir, filename);
    
    // Process and save image
    const config = IMAGE_CONFIGS.slider;
    await sharp(buffer)
      .resize(config.width, config.height, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: config.quality })
      .toFile(filePath);

    // Return the relative URL path
    const imageUrl = `/uploads/slider/${filename}`;

    return reply.send({
      success: true,
      message: 'Slider image uploaded successfully',
      data: {
        imageUrl,
        filename,
        originalName: data.filename,
        size: buffer.length,
        dimensions: {
          width: config.width,
          height: config.height
        }
      }
    });

  } catch (error) {
    console.error('Slider image upload error:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to upload slider image',
      error: error.message
    });
  }
};

/**
 * Delete uploaded image
 */
export const deleteImage = async (request, reply) => {
  try {
    const { filename, type } = request.params;
    
    if (!filename || !type) {
      return reply.status(400).send({
        success: false,
        message: 'Filename and type are required'
      });
    }

    if (!['subject', 'slider'].includes(type)) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid image type. Must be "subject" or "slider"'
      });
    }

    const filePath = path.join(__dirname, `../../uploads/${type}s`, filename);
    
    try {
      await fs.access(filePath);
      await fs.unlink(filePath);
      
      return reply.send({
        success: true,
        message: 'Image deleted successfully'
      });
    } catch (error) {
      if (error.code === 'ENOENT') {
        return reply.status(404).send({
          success: false,
          message: 'Image not found'
        });
      }
      throw error;
    }

  } catch (error) {
    console.error('Image deletion error:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to delete image',
      error: error.message
    });
  }
};
