import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
const createSliderSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().optional(),
  imageUrl: z.string().url('Invalid image URL'),
  linkUrl: z.string().url('Invalid link URL').optional(),
  isActive: z.boolean().default(true),
  order: z.number().int().min(0).default(0)
});

const updateSliderSchema = createSliderSchema.partial();

/**
 * Get all slider images
 */
export const getAllSliders = async (request, reply) => {
  try {
    const { page = 1, limit = 10, isActive } = request.query;
    const skip = (page - 1) * limit;

    const where = {};
    if (isActive !== undefined) {
      where.isActive = isActive === 'true';
    }

    const [sliders, total] = await Promise.all([
      prisma.sliderImage.findMany({
        where,
        orderBy: [
          { order: 'asc' },
          { createdAt: 'desc' }
        ],
        skip: parseInt(skip),
        take: parseInt(limit)
      }),
      prisma.sliderImage.count({ where })
    ]);

    return reply.send({
      success: true,
      message: 'Slider images retrieved successfully',
      data: sliders,
      meta: {
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get sliders error:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve slider images',
      error: error.message
    });
  }
};

/**
 * Get active slider images for public display
 */
export const getActiveSliders = async (request, reply) => {
  try {
    const sliders = await prisma.sliderImage.findMany({
      where: { isActive: true },
      orderBy: [
        { order: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    return reply.send({
      success: true,
      message: 'Active slider images retrieved successfully',
      data: sliders
    });

  } catch (error) {
    console.error('Get active sliders error:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve active slider images',
      error: error.message
    });
  }
};

/**
 * Get slider by ID
 */
export const getSliderById = async (request, reply) => {
  try {
    const { id } = request.params;

    const slider = await prisma.sliderImage.findUnique({
      where: { id }
    });

    if (!slider) {
      return reply.status(404).send({
        success: false,
        message: 'Slider image not found'
      });
    }

    return reply.send({
      success: true,
      message: 'Slider image retrieved successfully',
      data: slider
    });

  } catch (error) {
    console.error('Get slider by ID error:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve slider image',
      error: error.message
    });
  }
};

/**
 * Create new slider image
 */
export const createSlider = async (request, reply) => {
  try {
    const validation = createSliderSchema.safeParse(request.body);
    
    if (!validation.success) {
      return reply.status(400).send({
        success: false,
        message: 'Validation failed',
        errors: validation.error.errors
      });
    }

    const slider = await prisma.sliderImage.create({
      data: validation.data
    });

    return reply.status(201).send({
      success: true,
      message: 'Slider image created successfully',
      data: slider
    });

  } catch (error) {
    console.error('Create slider error:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to create slider image',
      error: error.message
    });
  }
};

/**
 * Update slider image
 */
export const updateSlider = async (request, reply) => {
  try {
    const { id } = request.params;
    
    const validation = updateSliderSchema.safeParse(request.body);
    
    if (!validation.success) {
      return reply.status(400).send({
        success: false,
        message: 'Validation failed',
        errors: validation.error.errors
      });
    }

    // Check if slider exists
    const existingSlider = await prisma.sliderImage.findUnique({
      where: { id }
    });

    if (!existingSlider) {
      return reply.status(404).send({
        success: false,
        message: 'Slider image not found'
      });
    }

    const slider = await prisma.sliderImage.update({
      where: { id },
      data: validation.data
    });

    return reply.send({
      success: true,
      message: 'Slider image updated successfully',
      data: slider
    });

  } catch (error) {
    console.error('Update slider error:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to update slider image',
      error: error.message
    });
  }
};

/**
 * Delete slider image
 */
export const deleteSlider = async (request, reply) => {
  try {
    const { id } = request.params;

    // Check if slider exists
    const existingSlider = await prisma.sliderImage.findUnique({
      where: { id }
    });

    if (!existingSlider) {
      return reply.status(404).send({
        success: false,
        message: 'Slider image not found'
      });
    }

    await prisma.sliderImage.delete({
      where: { id }
    });

    return reply.send({
      success: true,
      message: 'Slider image deleted successfully'
    });

  } catch (error) {
    console.error('Delete slider error:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to delete slider image',
      error: error.message
    });
  }
};

/**
 * Update slider order
 */
export const updateSliderOrder = async (request, reply) => {
  try {
    const { sliders } = request.body;

    if (!Array.isArray(sliders)) {
      return reply.status(400).send({
        success: false,
        message: 'Sliders must be an array'
      });
    }

    // Update order for each slider
    const updatePromises = sliders.map((slider, index) => 
      prisma.sliderImage.update({
        where: { id: slider.id },
        data: { order: index }
      })
    );

    await Promise.all(updatePromises);

    return reply.send({
      success: true,
      message: 'Slider order updated successfully'
    });

  } catch (error) {
    console.error('Update slider order error:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to update slider order',
      error: error.message
    });
  }
};
