import { prisma } from '../lib/prisma.js'
import { createSubscriptionPlanSchema, updateSubscriptionPlanSchema, paginationSchema, searchSchema } from '../types/schemas.js'
import { successResponse, errorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

export const subscriptionPlanController = {
  // Get all subscription plans with pagination and filtering
  async getAll(request, reply) {
    try {
      const { page, limit } = paginationSchema.parse(request.query)
      const { search, isActive } = searchSchema.parse(request.query)
      const skip = (page - 1) * limit

      const where = {
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(isActive !== undefined && { isActive: isActive === 'true' })
      }

      const [plans, total] = await Promise.all([
        prisma.subscriptionPlan.findMany({
          where,
          include: {
            planSubjects: {
              include: {
                subject: {
                  include: {
                    domain: true
                  }
                }
              }
            },
            _count: {
              select: {
                userSubscriptions: true
              }
            }
          },
          orderBy: [
            { price: 'asc' },
            { createdAt: 'desc' }
          ],
          skip,
          take: limit
        }),
        prisma.subscriptionPlan.count({ where })
      ])

      const pagination = getPaginationMeta(total, page, limit)

      return successResponse(reply, {
        plans,
        pagination
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get subscription plan by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id },
        include: {
          planSubjects: {
            include: {
              subject: {
                include: {
                  domain: true,
                  chapters: {
                    select: {
                      id: true,
                      title: true
                    }
                  }
                }
              }
            }
          },
          userSubscriptions: {
            include: {
              user: true
            },
            orderBy: { createdAt: 'desc' }
          },
          _count: {
            select: {
              userSubscriptions: true
            }
          }
        }
      })

      if (!plan) {
        return errorResponse(reply, 'Subscription plan not found', 404)
      }

      return successResponse(reply, { plan })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Create new subscription plan
  async create(request, reply) {
    try {
      const data = createSubscriptionPlanSchema.parse(request.body)
      const { subjectIds, ...planData } = data

      // Check if plan name already exists
      const existingPlan = await prisma.subscriptionPlan.findUnique({
        where: { name: planData.name }
      })

      if (existingPlan) {
        return errorResponse(reply, 'Subscription plan with this name already exists', 400)
      }

      // Check if subjects exist
      const subjects = await prisma.subject.findMany({
        where: {
          id: { in: subjectIds },
          isActive: true
        }
      })

      if (subjects.length !== subjectIds.length) {
        return errorResponse(reply, 'One or more subjects not found or inactive', 400)
      }

      // Create plan with subjects in a transaction
      const plan = await prisma.$transaction(async (tx) => {
        const newPlan = await tx.subscriptionPlan.create({
          data: planData
        })

        // Create plan-subject relationships
        await tx.planSubject.createMany({
          data: subjectIds.map(subjectId => ({
            planId: newPlan.id,
            subjectId
          }))
        })

        return tx.subscriptionPlan.findUnique({
          where: { id: newPlan.id },
          include: {
            planSubjects: {
              include: {
                subject: {
                  include: {
                    domain: true
                  }
                }
              }
            }
          }
        })
      })

      return successResponse(reply, { plan }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Update subscription plan
  async update(request, reply) {
    try {
      const { id } = request.params
      const data = updateSubscriptionPlanSchema.parse(request.body)
      const { subjectIds, ...planData } = data

      // Check if plan exists
      const existingPlan = await prisma.subscriptionPlan.findUnique({
        where: { id }
      })

      if (!existingPlan) {
        return errorResponse(reply, 'Subscription plan not found', 404)
      }

      // If name is being updated, check if it's unique
      if (planData.name && planData.name !== existingPlan.name) {
        const planWithSameName = await prisma.subscriptionPlan.findUnique({
          where: { name: planData.name }
        })

        if (planWithSameName) {
          return errorResponse(reply, 'Subscription plan with this name already exists', 400)
        }
      }

      // Update plan with subjects in a transaction
      const plan = await prisma.$transaction(async (tx) => {
        const updatedPlan = await tx.subscriptionPlan.update({
          where: { id },
          data: planData
        })

        // Update plan-subject relationships if subjectIds provided
        if (subjectIds) {
          // Check if subjects exist
          const subjects = await tx.subject.findMany({
            where: {
              id: { in: subjectIds },
              isActive: true
            }
          })

          if (subjects.length !== subjectIds.length) {
            throw new Error('One or more subjects not found or inactive')
          }

          // Delete existing relationships
          await tx.planSubject.deleteMany({
            where: { planId: id }
          })

          // Create new relationships
          await tx.planSubject.createMany({
            data: subjectIds.map(subjectId => ({
              planId: id,
              subjectId
            }))
          })
        }

        return tx.subscriptionPlan.findUnique({
          where: { id },
          include: {
            planSubjects: {
              include: {
                subject: {
                  include: {
                    domain: true
                  }
                }
              }
            }
          }
        })
      })

      return successResponse(reply, { plan })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Delete subscription plan
  async delete(request, reply) {
    try {
      const { id } = request.params

      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              userSubscriptions: true
            }
          }
        }
      })

      if (!plan) {
        return errorResponse(reply, 'Subscription plan not found', 404)
      }

      // Check if plan has active subscriptions
      if (plan._count.userSubscriptions > 0) {
        return errorResponse(reply, 'Cannot delete subscription plan with active subscriptions', 400)
      }

      await prisma.subscriptionPlan.delete({
        where: { id }
      })

      return successResponse(reply, { message: 'Subscription plan deleted successfully' })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get active plans (public endpoint)
  async getActivePlans(request, reply) {
    try {
      const plans = await prisma.subscriptionPlan.findMany({
        where: { isActive: true },
        orderBy: { price: 'asc' },
        select: {
          id: true,
          name: true,
          description: true,
          price: true,
          duration: true,
          discountPercentage: true
        }
      })

      return successResponse(reply, { plans })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  }
}
