import { PrismaClient } from '@prisma/client'
import { createSubscriptionPlanSchema, updateSubscriptionPlanSchema, paginationSchema, searchSchema } from '../types/schemas.js'
import { successResponse, errorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

const prisma = new PrismaClient()

export const subscriptionPlanController = {
  // Get all subscription plans with pagination and filtering
  async getAll(request, reply) {
    try {
      const { page, limit } = paginationSchema.parse(request.query)
      const { search, isActive } = searchSchema.parse(request.query)
      const skip = (page - 1) * limit

      const where = {
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(isActive !== undefined && { isActive: isActive === 'true' })
      }

      const [plans, total] = await Promise.all([
        prisma.subscriptionPlan.findMany({
          where,
          include: {
            _count: {
              select: {
                userSubscriptions: true
              }
            }
          },
          orderBy: [
            { price: 'asc' },
            { createdAt: 'desc' }
          ],
          skip,
          take: limit
        }),
        prisma.subscriptionPlan.count({ where })
      ])

      const pagination = getPaginationMeta(total, page, limit)

      return successResponse(reply, {
        plans,
        pagination
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get subscription plan by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id },
        include: {
          userSubscriptions: {
            include: {
              user: true
            },
            orderBy: { createdAt: 'desc' }
          },
          _count: {
            select: {
              userSubscriptions: true
            }
          }
        }
      })

      if (!plan) {
        return errorResponse(reply, 'Subscription plan not found', 404)
      }

      return successResponse(reply, { plan })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Create new subscription plan
  async create(request, reply) {
    try {
      const data = createSubscriptionPlanSchema.parse(request.body)

      // Check if plan name already exists
      const existingPlan = await prisma.subscriptionPlan.findUnique({
        where: { name: data.name }
      })

      if (existingPlan) {
        return errorResponse(reply, 'Subscription plan with this name already exists', 400)
      }

      const plan = await prisma.subscriptionPlan.create({
        data
      })

      return successResponse(reply, { plan }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Update subscription plan
  async update(request, reply) {
    try {
      const { id } = request.params
      const data = updateSubscriptionPlanSchema.parse(request.body)

      // Check if plan exists
      const existingPlan = await prisma.subscriptionPlan.findUnique({
        where: { id }
      })

      if (!existingPlan) {
        return errorResponse(reply, 'Subscription plan not found', 404)
      }

      // If name is being updated, check if it's unique
      if (data.name && data.name !== existingPlan.name) {
        const planWithSameName = await prisma.subscriptionPlan.findUnique({
          where: { name: data.name }
        })

        if (planWithSameName) {
          return errorResponse(reply, 'Subscription plan with this name already exists', 400)
        }
      }

      const plan = await prisma.subscriptionPlan.update({
        where: { id },
        data
      })

      return successResponse(reply, { plan })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Delete subscription plan
  async delete(request, reply) {
    try {
      const { id } = request.params

      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              userSubscriptions: true
            }
          }
        }
      })

      if (!plan) {
        return errorResponse(reply, 'Subscription plan not found', 404)
      }

      // Check if plan has active subscriptions
      if (plan._count.userSubscriptions > 0) {
        return errorResponse(reply, 'Cannot delete subscription plan with active subscriptions', 400)
      }

      await prisma.subscriptionPlan.delete({
        where: { id }
      })

      return successResponse(reply, { message: 'Subscription plan deleted successfully' })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get active plans (public endpoint)
  async getActivePlans(request, reply) {
    try {
      const plans = await prisma.subscriptionPlan.findMany({
        where: { isActive: true },
        orderBy: { price: 'asc' },
        select: {
          id: true,
          name: true,
          description: true,
          price: true,
          duration: true,
          features: true
        }
      })

      return successResponse(reply, { plans })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  }
}
