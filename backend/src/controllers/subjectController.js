import { successResponse, errorResponse, validationErrorResponse, handlePrismaError, paginationMeta } from '../utils/response.js'
import { createSubjectSchema, updateSubjectSchema, paginationSchema, searchSchema } from '../types/schemas.js'
import { z } from 'zod'

// Schema for updating subject image
const updateSubjectImageSchema = z.object({
  imageUrl: z.string().url('Invalid image URL')
})

/**
 * Get all subjects with pagination and search
 */
export const getSubjects = async (request, reply) => {
  try {
    const paginationValidation = paginationSchema.safeParse(request.query)
    const searchValidation = searchSchema.safeParse(request.query)
    
    if (!paginationValidation.success || !searchValidation.success) {
      return reply.status(400).send(errorResponse('Invalid query parameters'))
    }

    const { page, limit } = paginationValidation.data
    const { search, isActive } = searchValidation.data
    const { domainId } = request.query

    // Build where clause
    const where = {}
    if (domainId) where.domainId = domainId
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } }
      ]
    }
    if (isActive !== undefined) where.isActive = isActive

    const total = await request.server.prisma.subject.count({ where })

    const subjects = await request.server.prisma.subject.findMany({
      where,
      orderBy: [{ order: 'asc' }, { name: 'asc' }],
      skip: (page - 1) * limit,
      take: limit,
      include: {
        domain: {
          select: { id: true, name: true }
        },
        _count: {
          select: { chapters: true }
        }
      }
    })

    return reply.send(successResponse(
      subjects,
      'Subjects retrieved successfully',
      paginationMeta(page, limit, total)
    ))

  } catch (error) {
    request.log.error('Get subjects error:', error)
    return reply.status(500).send(errorResponse('Internal server error'))
  }
}

/**
 * Get subject by ID
 */
export const getSubjectById = async (request, reply) => {
  try {
    const { id } = request.params

    const subject = await request.server.prisma.subject.findUnique({
      where: { id },
      include: {
        domain: {
          select: { id: true, name: true }
        },
        chapters: {
          orderBy: [{ order: 'asc' }, { title: 'asc' }],
          include: {
            _count: {
              select: { mcqs: true, vivaQAs: true }
            }
          }
        },
        _count: {
          select: { chapters: true }
        }
      }
    })

    if (!subject) {
      return reply.status(404).send(errorResponse('Subject not found'))
    }

    return reply.send(successResponse(subject, 'Subject retrieved successfully'))

  } catch (error) {
    request.log.error('Get subject by ID error:', error)
    return reply.status(500).send(errorResponse('Internal server error'))
  }
}

/**
 * Create new subject
 */
export const createSubject = async (request, reply) => {
  try {
    const validation = createSubjectSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send(validationErrorResponse(validation.error.errors))
    }

    const subjectData = validation.data

    const subject = await request.server.prisma.subject.create({
      data: subjectData,
      include: {
        domain: {
          select: { id: true, name: true }
        },
        _count: {
          select: { chapters: true }
        }
      }
    })

    return reply.status(201).send(successResponse(
      subject,
      'Subject created successfully'
    ))

  } catch (error) {
    request.log.error('Create subject error:', error)
    
    if (error.code) {
      return reply.status(400).send(handlePrismaError(error))
    }
    
    return reply.status(500).send(errorResponse('Internal server error'))
  }
}

/**
 * Update subject
 */
export const updateSubject = async (request, reply) => {
  try {
    const { id } = request.params

    const validation = updateSubjectSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send(validationErrorResponse(validation.error.errors))
    }

    const updateData = validation.data

    const subject = await request.server.prisma.subject.update({
      where: { id },
      data: updateData,
      include: {
        domain: {
          select: { id: true, name: true }
        },
        _count: {
          select: { chapters: true }
        }
      }
    })

    return reply.send(successResponse(
      subject,
      'Subject updated successfully'
    ))

  } catch (error) {
    request.log.error('Update subject error:', error)
    
    if (error.code) {
      return reply.status(400).send(handlePrismaError(error))
    }
    
    return reply.status(500).send(errorResponse('Internal server error'))
  }
}

/**
 * Delete subject
 */
export const deleteSubject = async (request, reply) => {
  try {
    const { id } = request.params

    await request.server.prisma.subject.delete({
      where: { id }
    })

    return reply.send(successResponse(
      null,
      'Subject deleted successfully'
    ))

  } catch (error) {
    request.log.error('Delete subject error:', error)

    if (error.code) {
      return reply.status(400).send(handlePrismaError(error))
    }

    return reply.status(500).send(errorResponse('Internal server error'))
  }
}

/**
 * Update subject image
 */
export const updateSubjectImage = async (request, reply) => {
  try {
    const { id } = request.params

    const validation = updateSubjectImageSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send(validationErrorResponse(validation.error.errors))
    }

    const { imageUrl } = validation.data

    // Check if subject exists
    const existingSubject = await request.server.prisma.subject.findUnique({
      where: { id }
    })

    if (!existingSubject) {
      return reply.status(404).send(errorResponse('Subject not found'))
    }

    // Update subject image
    const subject = await request.server.prisma.subject.update({
      where: { id },
      data: { imageUrl },
      include: {
        domain: {
          select: { id: true, name: true }
        },
        _count: {
          select: { chapters: true }
        }
      }
    })

    return reply.send(successResponse(
      subject,
      'Subject image updated successfully'
    ))

  } catch (error) {
    request.log.error('Update subject image error:', error)

    if (error.code) {
      return reply.status(400).send(handlePrismaError(error))
    }

    return reply.status(500).send(errorResponse('Internal server error'))
  }
}
