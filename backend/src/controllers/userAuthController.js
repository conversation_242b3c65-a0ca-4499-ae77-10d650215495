import jwt from 'jsonwebtoken'
import { verifyIdToken } from '../config/firebase.js'
import { successResponse, errorResponse } from '../utils/response.js'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// Google OAuth login/signup
export const googleAuth = async (request, reply) => {
  try {
    const { idToken, fcmToken } = request.body

    if (!idToken) {
      return reply.status(400).send(errorResponse(
        'ID token is required',
        'Validation Error'
      ))
    }

    // Verify the Firebase ID token
    const decodedToken = await verifyIdToken(idToken)
    
    const { uid, email, name, picture } = decodedToken

    if (!email) {
      return reply.status(400).send(errorResponse(
        'Email is required for authentication',
        'Validation Error'
      ))
    }

    // Check if user exists
    let user = await request.server.prisma.user.findUnique({
      where: { email }
    })

    if (user) {
      // Update existing user
      user = await request.server.prisma.user.update({
        where: { id: user.id },
        data: {
          googleId: uid,
          name: name || user.name,
          profilePicture: picture || user.profilePicture,
          fcmToken: fcmToken || user.fcmToken,
          lastLoginAt: new Date(),
          isActive: true
        }
      })
    } else {
      // Create new user
      user = await request.server.prisma.user.create({
        data: {
          googleId: uid,
          email,
          name: name || 'User',
          profilePicture: picture,
          fcmToken,
          lastLoginAt: new Date(),
          isActive: true
        }
      })
    }

    // Generate JWT token for the user
    const token = jwt.sign(
      { 
        userId: user.id, 
        email: user.email,
        type: 'user' // Distinguish from admin tokens
      },
      JWT_SECRET,
      { expiresIn: '30d' }
    )

    // Remove sensitive data from response
    const { fcmToken: _, ...userResponse } = user

    return reply.send(successResponse({
      user: userResponse,
      token
    }, 'Authentication successful'))

  } catch (error) {
    console.error('Google auth error:', error)
    return reply.status(500).send(errorResponse(
      'Authentication failed',
      'Server Error'
    ))
  }
}

// Update user profile
export const updateProfile = async (request, reply) => {
  try {
    const userId = request.user.userId
    const { name, theme, language, fcmToken } = request.body

    const updateData = {}
    if (name !== undefined) updateData.name = name
    if (theme !== undefined && ['light', 'dark'].includes(theme)) updateData.theme = theme
    if (language !== undefined && ['en', 'bn'].includes(language)) updateData.language = language
    if (fcmToken !== undefined) updateData.fcmToken = fcmToken

    const user = await request.server.prisma.user.update({
      where: { id: userId },
      data: updateData
    })

    // Remove sensitive data from response
    const { fcmToken: _, ...userResponse } = user

    return reply.send(successResponse(
      userResponse,
      'Profile updated successfully'
    ))

  } catch (error) {
    console.error('Update profile error:', error)
    return reply.status(500).send(errorResponse(
      'Failed to update profile',
      'Server Error'
    ))
  }
}

// Get user profile
export const getProfile = async (request, reply) => {
  try {
    const userId = request.user.userId

    const user = await request.server.prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          where: { isActive: true },
          include: {
            plan: {
              include: {
                planSubjects: {
                  include: {
                    subject: true
                  }
                }
              }
            }
          }
        },
        subjectAccess: {
          where: { isActive: true },
          include: {
            subject: true
          }
        }
      }
    })

    if (!user) {
      return reply.status(404).send(errorResponse(
        'User not found',
        'Not Found'
      ))
    }

    // Remove sensitive data from response
    const { fcmToken, ...userResponse } = user

    return reply.send(successResponse(
      userResponse,
      'Profile retrieved successfully'
    ))

  } catch (error) {
    console.error('Get profile error:', error)
    return reply.status(500).send(errorResponse(
      'Failed to retrieve profile',
      'Server Error'
    ))
  }
}

// Logout (mainly for FCM token cleanup)
export const logout = async (request, reply) => {
  try {
    const userId = request.user.userId

    // Clear FCM token
    await request.server.prisma.user.update({
      where: { id: userId },
      data: {
        fcmToken: null
      }
    })

    return reply.send(successResponse(
      null,
      'Logged out successfully'
    ))

  } catch (error) {
    console.error('Logout error:', error)
    return reply.status(500).send(errorResponse(
      'Logout failed',
      'Server Error'
    ))
  }
}

// Delete user account
export const deleteAccount = async (request, reply) => {
  try {
    const userId = request.user.userId

    // Get user first to access email
    const user = await request.server.prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return reply.status(404).send(errorResponse(
        'User not found',
        'Not Found'
      ))
    }

    // Soft delete - mark as inactive
    await request.server.prisma.user.update({
      where: { id: userId },
      data: {
        isActive: false,
        fcmToken: null,
        email: `deleted_${Date.now()}_${user.email}` // Prevent email conflicts
      }
    })

    return reply.send(successResponse(
      null,
      'Account deleted successfully'
    ))

  } catch (error) {
    console.error('Delete account error:', error)
    return reply.status(500).send(errorResponse(
      'Failed to delete account',
      'Server Error'
    ))
  }
}

// Verify user token (middleware function)
export const verifyUserToken = async (request, reply) => {
  try {
    const token = request.headers.authorization?.replace('Bearer ', '')

    if (!token) {
      return reply.status(401).send(errorResponse(
        'Access token required',
        'Unauthorized'
      ))
    }

    const decoded = jwt.verify(token, JWT_SECRET)
    
    if (decoded.type !== 'user') {
      return reply.status(401).send(errorResponse(
        'Invalid token type',
        'Unauthorized'
      ))
    }

    // Check if user still exists and is active
    const user = await request.server.prisma.user.findUnique({
      where: { id: decoded.userId }
    })

    if (!user || !user.isActive) {
      return reply.status(401).send(errorResponse(
        'User not found or inactive',
        'Unauthorized'
      ))
    }

    request.user = decoded
    
  } catch (error) {
    console.error('Token verification error:', error)
    return reply.status(401).send(errorResponse(
      'Invalid or expired token',
      'Unauthorized'
    ))
  }
}

// Verify token endpoint (for frontend to check if token is still valid)
export const verifyToken = async (request, reply) => {
  try {
    // If we reach here, the token is valid (auth middleware passed)
    return reply.send(successResponse(
      { valid: true, user: request.user },
      'Token is valid'
    ))
  } catch (error) {
    console.error('Verify token error:', error)
    return reply.status(500).send(errorResponse(
      'Internal server error',
      'Server Error'
    ))
  }
}
