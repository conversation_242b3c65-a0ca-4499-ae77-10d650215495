import { prisma } from '../lib/prisma.js'
import { 
  createSubjectPricingSchema, 
  updateSubjectPricingSchema,
  paginationSchema,
  searchSchema
} from '../types/schemas.js'
import { successResponse, errorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

export const subjectPricingController = {
  // Get all subject pricing with pagination and filtering
  async getAll(request, reply) {
    try {
      const { page, limit } = paginationSchema.parse(request.query)
      const { search, isActive } = searchSchema.parse(request.query)
      const skip = (page - 1) * limit

      const where = {
        ...(isActive !== undefined && { isActive: isActive === 'true' }),
        ...(search && {
          subject: {
            OR: [
              { name: { contains: search, mode: 'insensitive' } },
              { domain: { name: { contains: search, mode: 'insensitive' } } }
            ]
          }
        })
      }

      const [pricings, total] = await Promise.all([
        prisma.subjectPricing.findMany({
          where,
          include: {
            subject: {
              include: {
                domain: true,
                _count: {
                  select: {
                    chapters: true
                  }
                }
              }
            }
          },
          orderBy: [
            { subject: { domain: { name: 'asc' } } },
            { subject: { name: 'asc' } }
          ],
          skip,
          take: limit
        }),
        prisma.subjectPricing.count({ where })
      ])

      const pagination = getPaginationMeta(total, page, limit)

      return successResponse(reply, {
        pricings,
        pagination
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get subject pricing by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const pricing = await prisma.subjectPricing.findUnique({
        where: { id },
        include: {
          subject: {
            include: {
              domain: true,
              chapters: {
                select: {
                  id: true,
                  title: true,
                  description: true
                }
              },
              userSubjectAccess: {
                include: {
                  user: {
                    select: {
                      id: true,
                      name: true,
                      email: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!pricing) {
        return errorResponse(reply, 'Subject pricing not found', 404)
      }

      return successResponse(reply, { pricing })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get pricing by subject ID
  async getBySubjectId(request, reply) {
    try {
      const { subjectId } = request.params

      const pricing = await prisma.subjectPricing.findUnique({
        where: { subjectId },
        include: {
          subject: {
            include: {
              domain: true
            }
          }
        }
      })

      if (!pricing) {
        return errorResponse(reply, 'Subject pricing not found', 404)
      }

      return successResponse(reply, { pricing })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Create subject pricing
  async create(request, reply) {
    try {
      const data = createSubjectPricingSchema.parse(request.body)

      // Check if subject exists
      const subject = await prisma.subject.findUnique({
        where: { id: data.subjectId }
      })

      if (!subject) {
        return errorResponse(reply, 'Subject not found', 404)
      }

      // Check if pricing already exists for this subject
      const existingPricing = await prisma.subjectPricing.findUnique({
        where: { subjectId: data.subjectId }
      })

      if (existingPricing) {
        return errorResponse(reply, 'Pricing already exists for this subject', 400)
      }

      const pricing = await prisma.subjectPricing.create({
        data,
        include: {
          subject: {
            include: {
              domain: true
            }
          }
        }
      })

      return successResponse(reply, { pricing }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Update subject pricing
  async update(request, reply) {
    try {
      const { id } = request.params
      const data = updateSubjectPricingSchema.parse(request.body)

      // Check if pricing exists
      const existingPricing = await prisma.subjectPricing.findUnique({
        where: { id }
      })

      if (!existingPricing) {
        return errorResponse(reply, 'Subject pricing not found', 404)
      }

      const pricing = await prisma.subjectPricing.update({
        where: { id },
        data,
        include: {
          subject: {
            include: {
              domain: true
            }
          }
        }
      })

      return successResponse(reply, { pricing })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Delete subject pricing
  async delete(request, reply) {
    try {
      const { id } = request.params

      const pricing = await prisma.subjectPricing.findUnique({
        where: { id },
        include: {
          subject: {
            include: {
              _count: {
                select: {
                  userSubjectAccess: true
                }
              }
            }
          }
        }
      })

      if (!pricing) {
        return errorResponse(reply, 'Subject pricing not found', 404)
      }

      // Check if subject has users with access
      if (pricing.subject._count.userSubjectAccess > 0) {
        return errorResponse(reply, 'Cannot delete pricing for subject with active user access. Deactivate the pricing instead.', 400)
      }

      await prisma.subjectPricing.delete({
        where: { id }
      })

      return successResponse(reply, { message: 'Subject pricing deleted successfully' })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get subjects without pricing
  async getSubjectsWithoutPricing(request, reply) {
    try {
      const subjects = await prisma.subject.findMany({
        where: {
          isActive: true,
          subjectPricing: null
        },
        include: {
          domain: true,
          _count: {
            select: {
              chapters: true
            }
          }
        },
        orderBy: [
          { domain: { name: 'asc' } },
          { name: 'asc' }
        ]
      })

      return successResponse(reply, { subjects })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Bulk create pricing for multiple subjects
  async bulkCreate(request, reply) {
    try {
      const { pricings } = request.body

      if (!Array.isArray(pricings) || pricings.length === 0) {
        return errorResponse(reply, 'Pricings array is required and cannot be empty', 400)
      }

      // Validate each pricing entry
      const validatedPricings = pricings.map(pricing => 
        createSubjectPricingSchema.parse(pricing)
      )

      // Check if subjects exist
      const subjectIds = validatedPricings.map(p => p.subjectId)
      const subjects = await prisma.subject.findMany({
        where: { id: { in: subjectIds } }
      })

      if (subjects.length !== subjectIds.length) {
        return errorResponse(reply, 'One or more subjects not found', 400)
      }

      // Check for existing pricing
      const existingPricings = await prisma.subjectPricing.findMany({
        where: { subjectId: { in: subjectIds } }
      })

      if (existingPricings.length > 0) {
        const existingSubjectIds = existingPricings.map(p => p.subjectId)
        return errorResponse(reply, `Pricing already exists for subjects: ${existingSubjectIds.join(', ')}`, 400)
      }

      // Create all pricings
      const createdPricings = await prisma.subjectPricing.createMany({
        data: validatedPricings
      })

      return successResponse(reply, { 
        message: `${createdPricings.count} subject pricings created successfully`,
        count: createdPricings.count
      }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  }
}
