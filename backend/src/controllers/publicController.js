import { successResponse, errorResponse } from '../utils/response.js'

/**
 * Get featured subjects for public display
 */
export const getFeaturedSubjects = async (request, reply) => {
  try {
    const subjects = await request.server.prisma.subject.findMany({
      where: {
        isActive: true,
        isFeatured: true
      },
      orderBy: [{ order: 'asc' }, { name: 'asc' }],
      include: {
        domain: {
          select: { id: true, name: true }
        },
        _count: {
          select: { chapters: true }
        }
      }
    })

    // Transform data for public consumption
    const publicSubjects = subjects.map(subject => ({
      id: subject.id,
      name: subject.name,
      slug: subject.slug,
      description: subject.description,
      imageUrl: subject.imageUrl,
      domain: subject.domain,
      chaptersCount: subject._count.chapters,
      order: subject.order
    }))

    return reply.send(successResponse(
      publicSubjects,
      'Featured subjects retrieved successfully'
    ))

  } catch (error) {
    request.log.error('Get featured subjects error:', error)
    return reply.status(500).send(errorResponse('Internal server error'))
  }
}

/**
 * Get all active subjects for public display
 */
export const getPublicSubjects = async (request, reply) => {
  try {
    const { domainId } = request.query

    const where = {
      isActive: true
    }

    if (domainId) {
      where.domainId = domainId
    }

    const subjects = await request.server.prisma.subject.findMany({
      where,
      orderBy: [{ order: 'asc' }, { name: 'asc' }],
      include: {
        domain: {
          select: { id: true, name: true }
        },
        _count: {
          select: { chapters: true }
        }
      }
    })

    // Transform data for public consumption
    const publicSubjects = subjects.map(subject => ({
      id: subject.id,
      name: subject.name,
      slug: subject.slug,
      description: subject.description,
      imageUrl: subject.imageUrl,
      domain: subject.domain,
      chaptersCount: subject._count.chapters,
      isFeatured: subject.isFeatured,
      order: subject.order
    }))

    return reply.send(successResponse(
      publicSubjects,
      'Public subjects retrieved successfully'
    ))

  } catch (error) {
    request.log.error('Get public subjects error:', error)
    return reply.status(500).send(errorResponse('Internal server error'))
  }
}

/**
 * Get subject details by slug for public display
 */
export const getSubjectBySlug = async (request, reply) => {
  try {
    const { slug } = request.params

    const subject = await request.server.prisma.subject.findFirst({
      where: {
        slug,
        isActive: true
      },
      include: {
        domain: {
          select: { id: true, name: true }
        },
        chapters: {
          where: { isActive: true },
          orderBy: [{ order: 'asc' }, { title: 'asc' }],
          select: {
            id: true,
            title: true,
            description: true,
            order: true,
            _count: {
              select: {
                mcqs: true,
                vivaQAs: true
              }
            }
          }
        },
        _count: {
          select: { chapters: true }
        }
      }
    })

    if (!subject) {
      return reply.status(404).send(errorResponse('Subject not found'))
    }

    // Transform data for public consumption
    const publicSubject = {
      id: subject.id,
      name: subject.name,
      slug: subject.slug,
      description: subject.description,
      imageUrl: subject.imageUrl,
      domain: subject.domain,
      chapters: subject.chapters.map(chapter => ({
        id: chapter.id,
        title: chapter.title,
        description: chapter.description,
        order: chapter.order,
        mcqsCount: chapter._count.mcqs,
        vivaQAsCount: chapter._count.vivaQAs
      })),
      chaptersCount: subject._count.chapters,
      isFeatured: subject.isFeatured,
      order: subject.order
    }

    return reply.send(successResponse(
      publicSubject,
      'Subject details retrieved successfully'
    ))

  } catch (error) {
    request.log.error('Get subject by slug error:', error)
    return reply.status(500).send(errorResponse('Internal server error'))
  }
}

/**
 * Get all active domains for public display
 */
export const getPublicDomains = async (request, reply) => {
  try {
    const domains = await request.server.prisma.domain.findMany({
      where: { isActive: true },
      orderBy: [{ order: 'asc' }, { name: 'asc' }],
      include: {
        _count: {
          select: {
            subjects: {
              where: { isActive: true }
            }
          }
        }
      }
    })

    // Transform data for public consumption
    const publicDomains = domains.map(domain => ({
      id: domain.id,
      name: domain.name,
      description: domain.description,
      subjectsCount: domain._count.subjects,
      order: domain.order
    }))

    return reply.send(successResponse(
      publicDomains,
      'Public domains retrieved successfully'
    ))

  } catch (error) {
    request.log.error('Get public domains error:', error)
    return reply.status(500).send(errorResponse('Internal server error'))
  }
}
