import { PrismaClient } from '@prisma/client'
import { createChapterSchema, updateChapterSchema, paginationSchema, searchSchema } from '../types/schemas.js'
import { successResponse, errorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

const prisma = new PrismaClient()

export const chapterController = {
  // Get all chapters with pagination and filtering
  async getAll(request, reply) {
    try {
      const { page, limit } = paginationSchema.parse(request.query)
      const { search } = searchSchema.parse(request.query)
      const { subjectId } = request.query
      const skip = (page - 1) * limit

      const where = {
        ...(search && {
          OR: [
            { title: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(subjectId && { subjectId })
      }

      const [chapters, total] = await Promise.all([
        prisma.chapter.findMany({
          where,
          include: {
            subject: {
              include: {
                domain: true
              }
            },
            _count: {
              select: {
                mcqs: true,
                vivaQAs: true
              }
            }
          },
          orderBy: [
            { order: 'asc' },
            { createdAt: 'desc' }
          ],
          skip,
          take: limit
        }),
        prisma.chapter.count({ where })
      ])

      const pagination = getPaginationMeta(page, limit, total)

      return reply.send({
        success: true,
        message: 'Chapters retrieved successfully',
        data: chapters,
        meta: pagination
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get chapter by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const chapter = await prisma.chapter.findUnique({
        where: { id },
        include: {
          subject: {
            include: {
              domain: true
            }
          },
          mcqs: {
            where: { isActive: true },
            orderBy: { order: 'asc' }
          },
          vivaQAs: {
            where: { isActive: true },
            orderBy: { order: 'asc' }
          }
        }
      })

      if (!chapter) {
        return reply.status(404).send({
          success: false,
          message: 'Chapter not found'
        })
      }

      return reply.send({
        success: true,
        message: 'Chapter retrieved successfully',
        data: chapter
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Create new chapter
  async create(request, reply) {
    try {
      const data = createChapterSchema.parse(request.body)

      // Check if subject exists
      const subject = await prisma.subject.findUnique({
        where: { id: data.subjectId }
      })

      if (!subject) {
        return reply.status(404).send({
          success: false,
          message: 'Subject not found'
        })
      }

      // Get the next order number
      const lastChapter = await prisma.chapter.findFirst({
        where: { subjectId: data.subjectId },
        orderBy: { order: 'desc' }
      })

      const chapter = await prisma.chapter.create({
        data: {
          ...data,
          order: data.order ?? (lastChapter?.order ?? 0) + 1
        },
        include: {
          subject: {
            include: {
              domain: true
            }
          }
        }
      })

      return reply.status(201).send({
        success: true,
        message: 'Chapter created successfully',
        data: chapter
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Update chapter
  async update(request, reply) {
    try {
      const { id } = request.params
      const data = updateChapterSchema.parse(request.body)

      // Check if chapter exists
      const existingChapter = await prisma.chapter.findUnique({
        where: { id }
      })

      if (!existingChapter) {
        return reply.status(404).send({
          success: false,
          message: 'Chapter not found'
        })
      }

      // If subjectId is being updated, check if the new subject exists
      if (data.subjectId && data.subjectId !== existingChapter.subjectId) {
        const subject = await prisma.subject.findUnique({
          where: { id: data.subjectId }
        })

        if (!subject) {
          return reply.status(404).send({
            success: false,
            message: 'Subject not found'
          })
        }
      }

      const chapter = await prisma.chapter.update({
        where: { id },
        data,
        include: {
          subject: {
            include: {
              domain: true
            }
          }
        }
      })

      return reply.send({
        success: true,
        message: 'Chapter updated successfully',
        data: chapter
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Delete chapter
  async delete(request, reply) {
    try {
      const { id } = request.params

      const chapter = await prisma.chapter.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              mcqs: true,
              vivaQAs: true
            }
          }
        }
      })

      if (!chapter) {
        return reply.status(404).send({
          success: false,
          message: 'Chapter not found'
        })
      }

      // Check if chapter has associated content
      if (chapter._count.mcqs > 0 || chapter._count.vivaQAs > 0) {
        return reply.status(400).send({
          success: false,
          message: 'Cannot delete chapter with associated MCQs or Viva Q&As'
        })
      }

      await prisma.chapter.delete({
        where: { id }
      })

      return reply.send({
        success: true,
        message: 'Chapter deleted successfully'
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  }
}
