import { PrismaClient } from '@prisma/client'
import { createChapterSchema, updateChapterSchema, paginationSchema, searchSchema } from '../types/schemas.js'
import { successResponse, errorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

const prisma = new PrismaClient()

export const chapterController = {
  // Get all chapters with pagination and filtering
  async getAll(request, reply) {
    try {
      const { page, limit } = paginationSchema.parse(request.query)
      const { search } = searchSchema.parse(request.query)
      const { subjectId } = request.query
      const skip = (page - 1) * limit

      const where = {
        ...(search && {
          OR: [
            { title: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(subjectId && { subjectId })
      }

      const [chapters, total] = await Promise.all([
        prisma.chapter.findMany({
          where,
          include: {
            subject: {
              include: {
                domain: true
              }
            },
            _count: {
              select: {
                mcqs: true,
                vivaQAs: true
              }
            }
          },
          orderBy: [
            { order: 'asc' },
            { createdAt: 'desc' }
          ],
          skip,
          take: limit
        }),
        prisma.chapter.count({ where })
      ])

      const pagination = getPaginationMeta(total, page, limit)

      return successResponse(reply, {
        chapters,
        pagination
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get chapter by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const chapter = await prisma.chapter.findUnique({
        where: { id },
        include: {
          subject: {
            include: {
              domain: true
            }
          },
          mcqs: {
            where: { isActive: true },
            orderBy: { order: 'asc' }
          },
          vivaQAs: {
            where: { isActive: true },
            orderBy: { order: 'asc' }
          }
        }
      })

      if (!chapter) {
        return errorResponse(reply, 'Chapter not found', 404)
      }

      return successResponse(reply, { chapter })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Create new chapter
  async create(request, reply) {
    try {
      const data = createChapterSchema.parse(request.body)

      // Check if subject exists
      const subject = await prisma.subject.findUnique({
        where: { id: data.subjectId }
      })

      if (!subject) {
        return errorResponse(reply, 'Subject not found', 404)
      }

      // Get the next order number
      const lastChapter = await prisma.chapter.findFirst({
        where: { subjectId: data.subjectId },
        orderBy: { order: 'desc' }
      })

      const chapter = await prisma.chapter.create({
        data: {
          ...data,
          order: data.order ?? (lastChapter?.order ?? 0) + 1
        },
        include: {
          subject: {
            include: {
              domain: true
            }
          }
        }
      })

      return successResponse(reply, { chapter }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Update chapter
  async update(request, reply) {
    try {
      const { id } = request.params
      const data = updateChapterSchema.parse(request.body)

      // Check if chapter exists
      const existingChapter = await prisma.chapter.findUnique({
        where: { id }
      })

      if (!existingChapter) {
        return errorResponse(reply, 'Chapter not found', 404)
      }

      // If subjectId is being updated, check if the new subject exists
      if (data.subjectId && data.subjectId !== existingChapter.subjectId) {
        const subject = await prisma.subject.findUnique({
          where: { id: data.subjectId }
        })

        if (!subject) {
          return errorResponse(reply, 'Subject not found', 404)
        }
      }

      const chapter = await prisma.chapter.update({
        where: { id },
        data,
        include: {
          subject: {
            include: {
              domain: true
            }
          }
        }
      })

      return successResponse(reply, { chapter })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Delete chapter
  async delete(request, reply) {
    try {
      const { id } = request.params

      const chapter = await prisma.chapter.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              mcqs: true,
              vivaQAs: true
            }
          }
        }
      })

      if (!chapter) {
        return errorResponse(reply, 'Chapter not found', 404)
      }

      // Check if chapter has associated content
      if (chapter._count.mcqs > 0 || chapter._count.vivaQAs > 0) {
        return errorResponse(reply, 'Cannot delete chapter with associated MCQs or Viva Q&As', 400)
      }

      await prisma.chapter.delete({
        where: { id }
      })

      return successResponse(reply, { message: 'Chapter deleted successfully' })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  }
}
