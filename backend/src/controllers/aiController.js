import { z } from 'zod'
import aiService from '../services/aiService.js'
import { 
  addGenerationJob, 
  getJobStatus, 
  cancelJob, 
  getQueueStats,
  JOB_TYPES 
} from '../services/jobQueue.js'
import { handlePrismaError } from '../utils/errorHandler.js'

// Validation schemas
const generateChaptersSchema = z.object({
  domainId: z.string().min(1),
  subjectId: z.string().min(1),
  count: z.number().int().min(1).max(20).default(5),
  options: z.object({
    model: z.string().optional(),
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().int().min(100).max(4000).optional()
  }).optional().default({})
})

const generateContentSchema = z.object({
  chapterId: z.string().min(1),
  options: z.object({
    model: z.string().optional(),
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().int().min(100).max(4000).optional()
  }).optional().default({})
})

const generateMCQsSchema = z.object({
  chapterId: z.string().min(1),
  count: z.number().int().min(1).max(50).default(10),
  customPrompt: z.string().optional(),
  options: z.object({
    model: z.string().optional(),
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().int().min(100).max(4000).optional()
  }).optional().default({})
})

const generateVivaSchema = z.object({
  chapterId: z.string().min(1),
  count: z.number().int().min(1).max(30).default(5),
  options: z.object({
    model: z.string().optional(),
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().int().min(100).max(4000).optional()
  }).optional().default({})
})

const generateBulkContentSchema = z.object({
  domainId: z.string().min(1),
  subjectId: z.string().min(1),
  chapterCount: z.number().int().min(1).max(10).default(5),
  mcqCount: z.number().int().min(1).max(20).default(10),
  vivaCount: z.number().int().min(1).max(15).default(5),
  options: z.object({
    model: z.string().optional(),
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().int().min(100).max(8000).optional()
  }).optional().default({})
})

const updatePromptSchema = z.object({
  promptType: z.string(),
  prompt: z.string().min(10)
})

export const aiController = {
  // Generate chapters
  generateChapters: async (request, reply) => {
    try {
      const data = generateChaptersSchema.parse(request.body)
      const adminId = request.admin.id

      // Add job to queue
      const job = await addGenerationJob(JOB_TYPES.GENERATE_CHAPTERS, {
        ...data,
        adminId
      })

      reply.status(202).send({
        success: true,
        message: 'Chapter generation job started',
        data: {
          jobId: job.id,
          status: 'pending'
        }
      })
    } catch (error) {
      console.error('Generate chapters error:', error)
      const prismaError = handlePrismaError(error)
      reply.status(prismaError.status).send(prismaError)
    }
  },

  // Generate chapter content
  generateChapterContent: async (request, reply) => {
    try {
      const data = generateContentSchema.parse(request.body)
      const adminId = request.admin.id

      // Add job to queue
      const job = await addGenerationJob(JOB_TYPES.GENERATE_CHAPTER_CONTENT, {
        ...data,
        adminId
      })

      reply.status(202).send({
        success: true,
        message: 'Chapter content generation job started',
        data: {
          jobId: job.id,
          status: 'pending'
        }
      })
    } catch (error) {
      console.error('Generate chapter content error:', error)
      const prismaError = handlePrismaError(error)
      reply.status(prismaError.status).send(prismaError)
    }
  },

  // Generate MCQs
  generateMCQs: async (request, reply) => {
    try {
      const data = generateMCQsSchema.parse(request.body)
      const adminId = request.admin.id

      // Add job to queue
      const job = await addGenerationJob(JOB_TYPES.GENERATE_MCQS, {
        ...data,
        adminId
      })

      reply.status(202).send({
        success: true,
        message: 'MCQ generation job started',
        data: {
          jobId: job.id,
          status: 'pending'
        }
      })
    } catch (error) {
      console.error('Generate MCQs error:', error)
      const prismaError = handlePrismaError(error)
      reply.status(prismaError.status).send(prismaError)
    }
  },

  // Generate Viva Q&A
  generateViva: async (request, reply) => {
    try {
      const data = generateVivaSchema.parse(request.body)
      const adminId = request.admin.id

      // Add job to queue
      const job = await addGenerationJob(JOB_TYPES.GENERATE_VIVA, {
        ...data,
        adminId
      })

      reply.status(202).send({
        success: true,
        message: 'Viva Q&A generation job started',
        data: {
          jobId: job.id,
          status: 'pending'
        }
      })
    } catch (error) {
      console.error('Generate Viva Q&A error:', error)
      const prismaError = handlePrismaError(error)
      reply.status(prismaError.status).send(prismaError)
    }
  },

  // Generate bulk content
  generateBulkContent: async (request, reply) => {
    try {
      const data = generateBulkContentSchema.parse(request.body)
      const adminId = request.admin.id

      // Add job to queue
      const job = await addGenerationJob(JOB_TYPES.GENERATE_BULK_CONTENT, {
        ...data,
        adminId
      })

      reply.status(202).send({
        success: true,
        message: 'Bulk content generation job started',
        data: {
          jobId: job.id,
          status: 'pending'
        }
      })
    } catch (error) {
      console.error('Generate bulk content error:', error)
      const prismaError = handlePrismaError(error)
      reply.status(prismaError.status).send(prismaError)
    }
  },

  // Get job status
  getJobStatus: async (request, reply) => {
    try {
      const { jobId } = request.params

      const jobStatus = await getJobStatus(jobId)

      if (!jobStatus) {
        return reply.status(404).send({
          success: false,
          message: 'Job not found'
        })
      }

      reply.send({
        success: true,
        data: jobStatus
      })
    } catch (error) {
      console.error('Get job status error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get job status',
        error: error.message
      })
    }
  },

  // Get job history
  getJobHistory: async (request, reply) => {
    try {
      const { limit = 10, offset = 0 } = request.query

      // For now, return empty array since we don't have job history storage
      // In a real implementation, you'd query your job storage
      reply.send({
        success: true,
        data: [],
        pagination: {
          total: 0,
          limit: parseInt(limit),
          offset: parseInt(offset),
          totalPages: 0
        }
      })
    } catch (error) {
      console.error('Get job history error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get job history'
      })
    }
  },

  // Cancel job
  cancelJob: async (request, reply) => {
    try {
      const { jobId } = request.params
      
      const cancelled = await cancelJob(jobId)
      
      if (!cancelled) {
        return reply.status(404).send({
          success: false,
          message: 'Job not found or cannot be cancelled'
        })
      }

      reply.send({
        success: true,
        message: 'Job cancelled successfully'
      })
    } catch (error) {
      console.error('Cancel job error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to cancel job',
        error: error.message
      })
    }
  },

  // Get queue statistics
  getQueueStats: async (request, reply) => {
    try {
      const stats = await getQueueStats()
      
      reply.send({
        success: true,
        data: stats
      })
    } catch (error) {
      console.error('Get queue stats error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get queue statistics',
        error: error.message
      })
    }
  },

  // Get available prompts
  getPrompts: async (request, reply) => {
    try {
      const prompts = aiService.getAvailablePrompts()
      
      reply.send({
        success: true,
        data: prompts
      })
    } catch (error) {
      console.error('Get prompts error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get prompts',
        error: error.message
      })
    }
  },

  // Get specific prompt template
  getPromptTemplate: async (request, reply) => {
    try {
      const { promptType } = request.params
      
      const template = aiService.getPromptTemplate(promptType)
      
      if (!template) {
        return reply.status(404).send({
          success: false,
          message: 'Prompt template not found'
        })
      }

      reply.send({
        success: true,
        data: template
      })
    } catch (error) {
      console.error('Get prompt template error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get prompt template',
        error: error.message
      })
    }
  },

  // Update prompt template
  updatePrompt: async (request, reply) => {
    try {
      const { promptType } = request.params
      const { prompt } = updatePromptSchema.parse(request.body)
      
      const updated = aiService.updatePrompt(promptType, prompt)
      
      if (!updated) {
        return reply.status(404).send({
          success: false,
          message: 'Prompt template not found'
        })
      }

      // Save prompts to file
      await aiService.savePrompts()

      reply.send({
        success: true,
        message: 'Prompt updated successfully'
      })
    } catch (error) {
      console.error('Update prompt error:', error)
      const prismaError = handlePrismaError(error)
      reply.status(prismaError.status).send(prismaError)
    }
  }
}
