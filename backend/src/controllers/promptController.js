import { z } from 'zod'
import aiService from '../services/aiService.js'
import { handlePrismaError } from '../utils/errorHandler.js'

// Validation schemas
const updatePromptSchema = z.object({
  prompt: z.string().min(10, 'Prompt must be at least 10 characters long'),
  name: z.string().min(1, 'Prompt name is required').optional(),
  description: z.string().optional(),
  variables: z.array(z.string()).optional()
})

const createPromptSchema = z.object({
  key: z.string().min(1, 'Prompt key is required'),
  name: z.string().min(1, 'Prompt name is required'),
  description: z.string().optional(),
  prompt: z.string().min(10, 'Prompt must be at least 10 characters long'),
  variables: z.array(z.string()).default([])
})

const testPromptSchema = z.object({
  promptType: z.string().min(1, 'Prompt type is required'),
  variables: z.record(z.string(), z.any()).default({}),
  options: z.object({
    model: z.string().optional(),
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().int().min(100).max(4000).optional()
  }).optional().default({})
})

export const promptController = {
  // Get all available prompts
  getAllPrompts: async (request, reply) => {
    try {
      const prompts = aiService.getAvailablePrompts()
      
      reply.send({
        success: true,
        data: prompts
      })
    } catch (error) {
      console.error('Get all prompts error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get prompts',
        error: error.message
      })
    }
  },

  // Get specific prompt template
  getPromptTemplate: async (request, reply) => {
    try {
      const { promptType } = request.params
      
      const template = aiService.getPromptTemplate(promptType)
      
      if (!template) {
        return reply.status(404).send({
          success: false,
          message: 'Prompt template not found'
        })
      }

      reply.send({
        success: true,
        data: template
      })
    } catch (error) {
      console.error('Get prompt template error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get prompt template',
        error: error.message
      })
    }
  },

  // Update existing prompt template
  updatePrompt: async (request, reply) => {
    try {
      const { promptType } = request.params
      const data = updatePromptSchema.parse(request.body)
      
      const currentTemplate = aiService.getPromptTemplate(promptType)
      if (!currentTemplate) {
        return reply.status(404).send({
          success: false,
          message: 'Prompt template not found'
        })
      }

      // Update the prompt
      const updated = aiService.updatePrompt(promptType, data.prompt)
      
      if (!updated) {
        return reply.status(400).send({
          success: false,
          message: 'Failed to update prompt'
        })
      }

      // Update other fields if provided
      if (data.name) currentTemplate.name = data.name
      if (data.description) currentTemplate.description = data.description
      if (data.variables) currentTemplate.variables = data.variables

      // Save prompts to file
      const saved = await aiService.savePrompts()
      
      if (!saved) {
        return reply.status(500).send({
          success: false,
          message: 'Failed to save prompt changes'
        })
      }

      reply.send({
        success: true,
        message: 'Prompt updated successfully',
        data: aiService.getPromptTemplate(promptType)
      })
    } catch (error) {
      console.error('Update prompt error:', error)
      const prismaError = handlePrismaError(error)
      reply.status(prismaError.status).send(prismaError)
    }
  },

  // Create new prompt template
  createPrompt: async (request, reply) => {
    try {
      const data = createPromptSchema.parse(request.body)
      
      // Check if prompt key already exists
      const existingTemplate = aiService.getPromptTemplate(data.key)
      if (existingTemplate) {
        return reply.status(409).send({
          success: false,
          message: 'Prompt template with this key already exists'
        })
      }

      // Add new prompt to the service
      const newPrompt = {
        name: data.name,
        description: data.description || '',
        prompt: data.prompt,
        variables: data.variables
      }

      // Manually add to prompts (since aiService doesn't have addPrompt method)
      await aiService.loadPrompts() // Reload current prompts
      const prompts = aiService.prompts || {}
      prompts[data.key] = newPrompt
      aiService.prompts = prompts

      // Save prompts to file
      const saved = await aiService.savePrompts()
      
      if (!saved) {
        return reply.status(500).send({
          success: false,
          message: 'Failed to save new prompt'
        })
      }

      reply.status(201).send({
        success: true,
        message: 'Prompt created successfully',
        data: { key: data.key, ...newPrompt }
      })
    } catch (error) {
      console.error('Create prompt error:', error)
      const prismaError = handlePrismaError(error)
      reply.status(prismaError.status).send(prismaError)
    }
  },

  // Delete prompt template
  deletePrompt: async (request, reply) => {
    try {
      const { promptType } = request.params
      
      const template = aiService.getPromptTemplate(promptType)
      if (!template) {
        return reply.status(404).send({
          success: false,
          message: 'Prompt template not found'
        })
      }

      // Remove prompt from service
      await aiService.loadPrompts()
      const prompts = aiService.prompts || {}
      delete prompts[promptType]
      aiService.prompts = prompts

      // Save prompts to file
      const saved = await aiService.savePrompts()
      
      if (!saved) {
        return reply.status(500).send({
          success: false,
          message: 'Failed to save prompt changes'
        })
      }

      reply.send({
        success: true,
        message: 'Prompt deleted successfully'
      })
    } catch (error) {
      console.error('Delete prompt error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to delete prompt',
        error: error.message
      })
    }
  },

  // Test prompt with sample data
  testPrompt: async (request, reply) => {
    try {
      const data = testPromptSchema.parse(request.body)
      
      const template = aiService.getPromptTemplate(data.promptType)
      if (!template) {
        return reply.status(404).send({
          success: false,
          message: 'Prompt template not found'
        })
      }

      // Generate content using the prompt
      const result = await aiService.generate(data.promptType, data.variables, data.options)
      
      reply.send({
        success: true,
        message: 'Prompt test completed successfully',
        data: {
          prompt: aiService.getPrompt(data.promptType, data.variables),
          result: result,
          variables: data.variables,
          options: data.options
        }
      })
    } catch (error) {
      console.error('Test prompt error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to test prompt',
        error: error.message
      })
    }
  },

  // Get prompt usage statistics
  getPromptStats: async (request, reply) => {
    try {
      // This would typically come from a database or analytics service
      // For now, return basic information about available prompts
      const prompts = aiService.getAvailablePrompts()
      
      const stats = {
        totalPrompts: prompts.length,
        promptTypes: prompts.map(p => ({
          key: p.key,
          name: p.name,
          variableCount: p.variables ? p.variables.length : 0
        })),
        lastUpdated: new Date().toISOString()
      }
      
      reply.send({
        success: true,
        data: stats
      })
    } catch (error) {
      console.error('Get prompt stats error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get prompt statistics',
        error: error.message
      })
    }
  },

  // Reset prompts to default
  resetToDefaults: async (request, reply) => {
    try {
      // Load default prompts from the original config file
      const fs = await import('fs/promises')
      const path = await import('path')
      const { fileURLToPath } = await import('url')
      
      const __filename = fileURLToPath(import.meta.url)
      const __dirname = path.dirname(__filename)
      const defaultPromptsPath = path.join(__dirname, '../../config/ai-prompts.json')
      
      const defaultPromptsData = await fs.readFile(defaultPromptsPath, 'utf8')
      const defaultPrompts = JSON.parse(defaultPromptsData)
      
      // Reset prompts in service
      aiService.prompts = defaultPrompts
      
      // Save to file
      const saved = await aiService.savePrompts()
      
      if (!saved) {
        return reply.status(500).send({
          success: false,
          message: 'Failed to reset prompts'
        })
      }

      reply.send({
        success: true,
        message: 'Prompts reset to defaults successfully',
        data: aiService.getAvailablePrompts()
      })
    } catch (error) {
      console.error('Reset prompts error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to reset prompts to defaults',
        error: error.message
      })
    }
  }
}
