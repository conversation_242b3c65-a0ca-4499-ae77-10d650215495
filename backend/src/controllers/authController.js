import { hashPassword, comparePassword, generateToken } from '../utils/auth.js'
import { successResponse, errorResponse, validationErrorResponse, handlePrismaError } from '../utils/response.js'
import { loginSchema, createAdminSchema } from '../types/schemas.js'

/**
 * Admin login
 */
export const login = async (request, reply) => {
  try {
    // Validate request body
    const validation = loginSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send(validationErrorResponse(validation.error.errors))
    }

    const { email, password } = validation.data

    // Find admin by email
    const admin = await request.server.prisma.admin.findUnique({
      where: { email }
    })

    if (!admin) {
      return reply.status(401).send(errorResponse(
        'Invalid email or password',
        'Authentication Failed'
      ))
    }

    // Verify password
    const isValidPassword = await comparePassword(password, admin.password)
    if (!isValidPassword) {
      return reply.status(401).send(errorResponse(
        'Invalid email or password',
        'Authentication Failed'
      ))
    }

    // Generate JWT token
    const token = generateToken(request.server, admin)

    // Return success response with token and admin info
    return reply.send(successResponse({
      token,
      admin: {
        id: admin.id,
        email: admin.email,
        name: admin.name
      }
    }, 'Login successful'))

  } catch (error) {
    request.log.error('Login error:', error)
    console.error('Login error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    })
    return reply.status(500).send(errorResponse(
      'Internal server error',
      'Server Error'
    ))
  }
}

/**
 * Create new admin (for initial setup or admin management)
 */
export const createAdmin = async (request, reply) => {
  try {
    // Validate request body
    const validation = createAdminSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send(validationErrorResponse(validation.error.errors))
    }

    const { email, password, name } = validation.data

    // Hash password
    const hashedPassword = await hashPassword(password)

    // Create admin
    const admin = await request.server.prisma.admin.create({
      data: {
        email,
        password: hashedPassword,
        name
      },
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true
      }
    })

    return reply.status(201).send(successResponse(
      admin,
      'Admin created successfully'
    ))

  } catch (error) {
    request.log.error('Create admin error:', error)
    
    if (error.code) {
      return reply.status(400).send(handlePrismaError(error))
    }
    
    return reply.status(500).send(errorResponse(
      'Internal server error',
      'Server Error'
    ))
  }
}

/**
 * Get current admin profile
 */
export const getProfile = async (request, reply) => {
  try {
    // Admin info is already available from auth middleware
    return reply.send(successResponse(
      request.admin,
      'Profile retrieved successfully'
    ))
  } catch (error) {
    request.log.error('Get profile error:', error)
    return reply.status(500).send(errorResponse(
      'Internal server error',
      'Server Error'
    ))
  }
}

/**
 * Verify token (for frontend to check if token is still valid)
 */
export const verifyToken = async (request, reply) => {
  try {
    // If we reach here, the token is valid (auth middleware passed)
    return reply.send(successResponse(
      { valid: true, admin: request.admin },
      'Token is valid'
    ))
  } catch (error) {
    request.log.error('Verify token error:', error)
    return reply.status(500).send(errorResponse(
      'Internal server error',
      'Server Error'
    ))
  }
}
