import { createVivaQASchema, updateVivaQASchema, paginationSchema, searchSchema } from '../types/schemas.js'
import { successResponse, errorResponse, validationErrorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

export const vivaController = {
  // Get all Viva Q&As with pagination and filtering
  async getAll(request, reply) {
    try {
      // Validate query parameters
      const paginationValidation = paginationSchema.safeParse(request.query)
      const searchValidation = searchSchema.safeParse(request.query)

      if (!paginationValidation.success || !searchValidation.success) {
        return reply.status(400).send(errorResponse('Invalid query parameters'))
      }

      const { page, limit } = paginationValidation.data
      const { search } = searchValidation.data
      const { chapterId, subjectId, domainId } = request.query
      const skip = (page - 1) * limit

      const where = {
        ...(search && {
          OR: [
            { question: { contains: search, mode: 'insensitive' } },
            { answer: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(chapterId && { chapterId }),
        ...(subjectId && {
          chapter: {
            subjectId
          }
        }),
        ...(domainId && {
          chapter: {
            subject: {
              domainId
            }
          }
        })
      }

      const [vivaQAs, total] = await Promise.all([
        request.server.prisma.vivaQA.findMany({
          where,
          include: {
            chapter: {
              include: {
                subject: {
                  include: {
                    domain: true
                  }
                }
              }
            }
          },
          orderBy: [
            { order: 'asc' },
            { createdAt: 'desc' }
          ],
          skip,
          take: limit
        }),
        request.server.prisma.vivaQA.count({ where })
      ])

      const pagination = getPaginationMeta(total, page, limit)

      return reply.send(successResponse(
        { vivaQAs },
        'Viva Q&As retrieved successfully',
        pagination
      ))
    } catch (error) {
      if (error.code) {
        return reply.status(400).send(handlePrismaError(error))
      }
      return reply.status(500).send(errorResponse('Internal server error'))
    }
  },

  // Get Viva Q&A by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const vivaQA = await request.server.prisma.vivaQA.findUnique({
        where: { id },
        include: {
          chapter: {
            include: {
              subject: {
                include: {
                  domain: true
                }
              }
            }
          }
        }
      })

      if (!vivaQA) {
        return reply.status(404).send(errorResponse('Viva Q&A not found'))
      }

      return reply.send(successResponse({ vivaQA }, 'Viva Q&A retrieved successfully'))
    } catch (error) {
      if (error.code) {
        return reply.status(400).send(handlePrismaError(error))
      }
      return reply.status(500).send(errorResponse('Internal server error'))
    }
  },

  // Create new Viva Q&A
  async create(request, reply) {
    try {
      // Validate request body
      const validation = createVivaQASchema.safeParse(request.body)
      if (!validation.success) {
        return reply.status(400).send(validationErrorResponse(validation.error.errors))
      }

      const data = validation.data

      // Check if chapter exists
      const chapter = await request.server.prisma.chapter.findUnique({
        where: { id: data.chapterId }
      })

      if (!chapter) {
        return reply.status(404).send(errorResponse('Chapter not found'))
      }

      // Get the next order number
      const lastVivaQA = await request.server.prisma.vivaQA.findFirst({
        where: { chapterId: data.chapterId },
        orderBy: { order: 'desc' }
      })

      const vivaQA = await request.server.prisma.vivaQA.create({
        data: {
          ...data,
          order: data.order ?? (lastVivaQA?.order ?? 0) + 1
        },
        include: {
          chapter: {
            include: {
              subject: {
                include: {
                  domain: true
                }
              }
            }
          }
        }
      })

      return reply.status(201).send(successResponse({ vivaQA }, 'Viva Q&A created successfully'))
    } catch (error) {
      if (error.code) {
        return reply.status(400).send(handlePrismaError(error))
      }
      return reply.status(500).send(errorResponse('Internal server error'))
    }
  },

  // Update Viva Q&A
  async update(request, reply) {
    try {
      const { id } = request.params

      // Validate request body
      const validation = updateVivaQASchema.safeParse(request.body)
      if (!validation.success) {
        return reply.status(400).send(validationErrorResponse(validation.error.errors))
      }

      const data = validation.data

      // Check if Viva Q&A exists
      const existingVivaQA = await request.server.prisma.vivaQA.findUnique({
        where: { id }
      })

      if (!existingVivaQA) {
        return reply.status(404).send(errorResponse('Viva Q&A not found'))
      }

      // If chapterId is being updated, check if the new chapter exists
      if (data.chapterId && data.chapterId !== existingVivaQA.chapterId) {
        const chapter = await request.server.prisma.chapter.findUnique({
          where: { id: data.chapterId }
        })

        if (!chapter) {
          return reply.status(404).send(errorResponse('Chapter not found'))
        }
      }

      const vivaQA = await request.server.prisma.vivaQA.update({
        where: { id },
        data,
        include: {
          chapter: {
            include: {
              subject: {
                include: {
                  domain: true
                }
              }
            }
          }
        }
      })

      return reply.send(successResponse({ vivaQA }, 'Viva Q&A updated successfully'))
    } catch (error) {
      if (error.code) {
        return reply.status(400).send(handlePrismaError(error))
      }
      return reply.status(500).send(errorResponse('Internal server error'))
    }
  },

  // Delete Viva Q&A
  async delete(request, reply) {
    try {
      const { id } = request.params

      const vivaQA = await request.server.prisma.vivaQA.findUnique({
        where: { id }
      })

      if (!vivaQA) {
        return reply.status(404).send(errorResponse('Viva Q&A not found'))
      }

      await request.server.prisma.vivaQA.delete({
        where: { id }
      })

      return reply.send(successResponse(null, 'Viva Q&A deleted successfully'))
    } catch (error) {
      if (error.code) {
        return reply.status(400).send(handlePrismaError(error))
      }
      return reply.status(500).send(errorResponse('Internal server error'))
    }
  },

  // Bulk create Viva Q&As
  async bulkCreate(request, reply) {
    try {
      const { vivaQAs } = request.body

      if (!Array.isArray(vivaQAs) || vivaQAs.length === 0) {
        return reply.status(400).send(errorResponse('Viva Q&As array is required and cannot be empty'))
      }

      // Validate all Viva Q&As
      const validatedVivaQAs = []
      for (const vivaQA of vivaQAs) {
        const validation = createVivaQASchema.safeParse(vivaQA)
        if (!validation.success) {
          return reply.status(400).send(validationErrorResponse(validation.error.errors))
        }
        validatedVivaQAs.push(validation.data)
      }

      // Check if all chapters exist
      const chapterIds = [...new Set(validatedVivaQAs.map(vivaQA => vivaQA.chapterId))]
      const chapters = await request.server.prisma.chapter.findMany({
        where: { id: { in: chapterIds } }
      })

      if (chapters.length !== chapterIds.length) {
        return reply.status(404).send(errorResponse('One or more chapters not found'))
      }

      // Create Viva Q&As in transaction
      const createdVivaQAs = await request.server.prisma.$transaction(
        validatedVivaQAs.map((vivaQA, index) =>
          request.server.prisma.vivaQA.create({
            data: {
              ...vivaQA,
              order: vivaQA.order ?? index + 1
            }
          })
        )
      )

      return reply.status(201).send(successResponse({
        vivaQAs: createdVivaQAs,
        count: createdVivaQAs.length
      }, 'Viva Q&As created successfully'))
    } catch (error) {
      if (error.code) {
        return reply.status(400).send(handlePrismaError(error))
      }
      return reply.status(500).send(errorResponse('Internal server error'))
    }
  }
}
