import { PrismaClient } from '@prisma/client'
import { createVivaQASchema, updateVivaQASchema, paginationSchema, searchSchema } from '../types/schemas.js'
import { successResponse, errorResponse, handlePrismaError, getPaginationMeta } from '../utils/response.js'

const prisma = new PrismaClient()

export const vivaController = {
  // Get all Viva Q&As with pagination and filtering
  async getAll(request, reply) {
    try {
      const { page, limit } = paginationSchema.parse(request.query)
      const { search } = searchSchema.parse(request.query)
      const { chapterId, subjectId, domainId } = request.query
      const skip = (page - 1) * limit

      const where = {
        ...(search && {
          OR: [
            { question: { contains: search, mode: 'insensitive' } },
            { answer: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(chapterId && { chapterId }),
        ...(subjectId && {
          chapter: {
            subjectId
          }
        }),
        ...(domainId && {
          chapter: {
            subject: {
              domainId
            }
          }
        })
      }

      const [vivaQAs, total] = await Promise.all([
        prisma.vivaQA.findMany({
          where,
          include: {
            chapter: {
              include: {
                subject: {
                  include: {
                    domain: true
                  }
                }
              }
            }
          },
          orderBy: [
            { order: 'asc' },
            { createdAt: 'desc' }
          ],
          skip,
          take: limit
        }),
        prisma.vivaQA.count({ where })
      ])

      const pagination = getPaginationMeta(total, page, limit)

      return successResponse(reply, {
        vivaQAs,
        pagination
      })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Get Viva Q&A by ID
  async getById(request, reply) {
    try {
      const { id } = request.params

      const vivaQA = await prisma.vivaQA.findUnique({
        where: { id },
        include: {
          chapter: {
            include: {
              subject: {
                include: {
                  domain: true
                }
              }
            }
          }
        }
      })

      if (!vivaQA) {
        return errorResponse(reply, 'Viva Q&A not found', 404)
      }

      return successResponse(reply, { vivaQA })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Create new Viva Q&A
  async create(request, reply) {
    try {
      const data = createVivaQASchema.parse(request.body)

      // Check if chapter exists
      const chapter = await prisma.chapter.findUnique({
        where: { id: data.chapterId }
      })

      if (!chapter) {
        return errorResponse(reply, 'Chapter not found', 404)
      }

      // Get the next order number
      const lastVivaQA = await prisma.vivaQA.findFirst({
        where: { chapterId: data.chapterId },
        orderBy: { order: 'desc' }
      })

      const vivaQA = await prisma.vivaQA.create({
        data: {
          ...data,
          order: data.order ?? (lastVivaQA?.order ?? 0) + 1
        },
        include: {
          chapter: {
            include: {
              subject: {
                include: {
                  domain: true
                }
              }
            }
          }
        }
      })

      return successResponse(reply, { vivaQA }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Update Viva Q&A
  async update(request, reply) {
    try {
      const { id } = request.params
      const data = updateVivaQASchema.parse(request.body)

      // Check if Viva Q&A exists
      const existingVivaQA = await prisma.vivaQA.findUnique({
        where: { id }
      })

      if (!existingVivaQA) {
        return errorResponse(reply, 'Viva Q&A not found', 404)
      }

      // If chapterId is being updated, check if the new chapter exists
      if (data.chapterId && data.chapterId !== existingVivaQA.chapterId) {
        const chapter = await prisma.chapter.findUnique({
          where: { id: data.chapterId }
        })

        if (!chapter) {
          return errorResponse(reply, 'Chapter not found', 404)
        }
      }

      const vivaQA = await prisma.vivaQA.update({
        where: { id },
        data,
        include: {
          chapter: {
            include: {
              subject: {
                include: {
                  domain: true
                }
              }
            }
          }
        }
      })

      return successResponse(reply, { vivaQA })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Delete Viva Q&A
  async delete(request, reply) {
    try {
      const { id } = request.params

      const vivaQA = await prisma.vivaQA.findUnique({
        where: { id }
      })

      if (!vivaQA) {
        return errorResponse(reply, 'Viva Q&A not found', 404)
      }

      await prisma.vivaQA.delete({
        where: { id }
      })

      return successResponse(reply, { message: 'Viva Q&A deleted successfully' })
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  },

  // Bulk create Viva Q&As
  async bulkCreate(request, reply) {
    try {
      const { vivaQAs } = request.body

      if (!Array.isArray(vivaQAs) || vivaQAs.length === 0) {
        return errorResponse(reply, 'Viva Q&As array is required and cannot be empty', 400)
      }

      // Validate all Viva Q&As
      const validatedVivaQAs = vivaQAs.map(vivaQA => createVivaQASchema.parse(vivaQA))

      // Check if all chapters exist
      const chapterIds = [...new Set(validatedVivaQAs.map(vivaQA => vivaQA.chapterId))]
      const chapters = await prisma.chapter.findMany({
        where: { id: { in: chapterIds } }
      })

      if (chapters.length !== chapterIds.length) {
        return errorResponse(reply, 'One or more chapters not found', 404)
      }

      // Create Viva Q&As in transaction
      const createdVivaQAs = await prisma.$transaction(
        validatedVivaQAs.map((vivaQA, index) => 
          prisma.vivaQA.create({
            data: {
              ...vivaQA,
              order: vivaQA.order ?? index + 1
            }
          })
        )
      )

      return successResponse(reply, { 
        vivaQAs: createdVivaQAs,
        count: createdVivaQAs.length 
      }, 201)
    } catch (error) {
      return handlePrismaError(error, reply)
    }
  }
}
