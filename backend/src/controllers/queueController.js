import { z } from 'zod'
import queueManager from '../services/queueManager.js'
import { 
  getJobStatus, 
  cancelJob, 
  getQueueStats,
  retryJob,
  cleanOldJobs 
} from '../services/jobQueue.js'
import { handlePrismaError } from '../utils/errorHandler.js'

// Validation schemas
const retryJobsSchema = z.object({
  queueName: z.string().min(1, 'Queue name is required'),
  limit: z.number().int().min(1).max(100).default(10)
})

const cleanJobsSchema = z.object({
  queueName: z.string().min(1, 'Queue name is required'),
  olderThanHours: z.number().int().min(1).max(168).default(24) // Max 1 week
})

export const queueController = {
  // Get queue health status
  getHealth: async (request, reply) => {
    try {
      const health = await queueManager.healthCheck()
      
      const statusCode = health.status === 'healthy' ? 200 : 
                        health.status === 'degraded' ? 206 : 503

      reply.status(statusCode).send({
        success: health.status !== 'unhealthy',
        data: health
      })
    } catch (error) {
      console.error('Queue health check error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to check queue health',
        error: error.message
      })
    }
  },

  // Get detailed queue information
  getQueueInfo: async (request, reply) => {
    try {
      const info = await queueManager.getQueueInfo()
      
      reply.send({
        success: true,
        data: info
      })
    } catch (error) {
      console.error('Get queue info error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get queue information',
        error: error.message
      })
    }
  },

  // Get queue statistics
  getStats: async (request, reply) => {
    try {
      const stats = await getQueueStats()
      
      reply.send({
        success: true,
        data: stats
      })
    } catch (error) {
      console.error('Get queue stats error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get queue statistics',
        error: error.message
      })
    }
  },

  // Get specific job status
  getJobStatus: async (request, reply) => {
    try {
      const { jobId } = request.params
      
      const jobStatus = await getJobStatus(jobId)
      
      if (!jobStatus) {
        return reply.status(404).send({
          success: false,
          message: 'Job not found'
        })
      }

      reply.send({
        success: true,
        data: jobStatus
      })
    } catch (error) {
      console.error('Get job status error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get job status',
        error: error.message
      })
    }
  },

  // Cancel a specific job
  cancelJob: async (request, reply) => {
    try {
      const { jobId } = request.params
      
      const cancelled = await cancelJob(jobId)
      
      if (!cancelled) {
        return reply.status(404).send({
          success: false,
          message: 'Job not found or cannot be cancelled'
        })
      }

      reply.send({
        success: true,
        message: 'Job cancelled successfully'
      })
    } catch (error) {
      console.error('Cancel job error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to cancel job',
        error: error.message
      })
    }
  },

  // Retry a specific job
  retryJob: async (request, reply) => {
    try {
      const { jobId } = request.params
      
      const retried = await retryJob(jobId)
      
      if (!retried) {
        return reply.status(404).send({
          success: false,
          message: 'Job not found or cannot be retried'
        })
      }

      reply.send({
        success: true,
        message: 'Job retried successfully'
      })
    } catch (error) {
      console.error('Retry job error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to retry job',
        error: error.message
      })
    }
  },

  // Retry multiple failed jobs
  retryFailedJobs: async (request, reply) => {
    try {
      const data = retryJobsSchema.parse(request.body)
      
      const result = await queueManager.retryFailedJobs(data.queueName, data.limit)
      
      reply.send({
        success: true,
        message: `Retried ${result.retriedCount} out of ${result.totalFailed} failed jobs`,
        data: result
      })
    } catch (error) {
      console.error('Retry failed jobs error:', error)
      const prismaError = handlePrismaError(error)
      reply.status(prismaError.status).send(prismaError)
    }
  },

  // Clean old completed jobs
  cleanCompletedJobs: async (request, reply) => {
    try {
      const data = cleanJobsSchema.parse(request.body)
      const olderThanMs = data.olderThanHours * 60 * 60 * 1000
      
      const result = await queueManager.cleanCompletedJobs(data.queueName, olderThanMs)
      
      reply.send({
        success: true,
        message: `Cleaned ${result.cleanedCount} completed jobs`,
        data: result
      })
    } catch (error) {
      console.error('Clean completed jobs error:', error)
      const prismaError = handlePrismaError(error)
      reply.status(prismaError.status).send(prismaError)
    }
  },

  // Get job logs (if available)
  getJobLogs: async (request, reply) => {
    try {
      const { jobId } = request.params
      
      // This would typically fetch logs from a logging service
      // For now, return basic job information
      const jobStatus = await getJobStatus(jobId)
      
      if (!jobStatus) {
        return reply.status(404).send({
          success: false,
          message: 'Job not found'
        })
      }

      const logs = {
        jobId: jobId,
        logs: jobStatus.logs || [],
        progress: jobStatus.progress || 0,
        status: jobStatus.status,
        createdAt: jobStatus.createdAt,
        processedAt: jobStatus.processedAt,
        finishedAt: jobStatus.finishedAt,
        error: jobStatus.error
      }

      reply.send({
        success: true,
        data: logs
      })
    } catch (error) {
      console.error('Get job logs error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to get job logs',
        error: error.message
      })
    }
  },

  // Pause queue
  pauseQueue: async (request, reply) => {
    try {
      const { queueName } = request.params
      
      // This would pause the specific queue
      // Implementation depends on the queue manager setup
      
      reply.send({
        success: true,
        message: `Queue ${queueName} paused successfully`
      })
    } catch (error) {
      console.error('Pause queue error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to pause queue',
        error: error.message
      })
    }
  },

  // Resume queue
  resumeQueue: async (request, reply) => {
    try {
      const { queueName } = request.params
      
      // This would resume the specific queue
      // Implementation depends on the queue manager setup
      
      reply.send({
        success: true,
        message: `Queue ${queueName} resumed successfully`
      })
    } catch (error) {
      console.error('Resume queue error:', error)
      reply.status(500).send({
        success: false,
        message: 'Failed to resume queue',
        error: error.message
      })
    }
  }
}
