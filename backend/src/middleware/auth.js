/**
 * Authentication middleware for protecting admin routes
 */

export const authenticateAdmin = async (request, reply) => {
  try {
    // Check for Authorization header
    const authHeader = request.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Missing or invalid authorization header'
      })
    }

    // Extract token
    const token = authHeader.substring(7)
    
    // Verify JWT token
    const decoded = await request.jwtVerify(token)
    
    // Check if admin exists in database
    const admin = await request.server.prisma.admin.findUnique({
      where: { id: decoded.adminId },
      select: { id: true, email: true, name: true }
    })

    if (!admin) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Admin not found'
      })
    }

    // Add admin info to request
    request.admin = admin
    
  } catch (error) {
    request.log.error('Authentication error:', error)
    return reply.status(401).send({
      error: 'Unauthorized',
      message: 'Invalid or expired token'
    })
  }
}

/**
 * Optional authentication middleware - doesn't fail if no token
 */
export const optionalAuth = async (request, reply) => {
  try {
    const authHeader = request.headers.authorization
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const decoded = await request.jwtVerify(token)

      const admin = await request.server.prisma.admin.findUnique({
        where: { id: decoded.adminId },
        select: { id: true, email: true, name: true }
      })

      if (admin) {
        request.admin = admin
      }
    }
  } catch (error) {
    // Silently fail for optional auth
    request.log.debug('Optional auth failed:', error.message)
  }
}

// Aliases for compatibility
export const authMiddleware = authenticateAdmin
export const optionalAuthMiddleware = optionalAuth
