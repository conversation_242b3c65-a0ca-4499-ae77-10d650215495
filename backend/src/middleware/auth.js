/**
 * Authentication middleware for protecting admin routes
 */

export const authenticateAdmin = async (request, reply) => {
  try {
    // Check for Authorization header
    const authHeader = request.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Missing or invalid authorization header'
      })
    }

    // Extract token
    const token = authHeader.substring(7)
    
    // Verify JWT token
    const decoded = await request.jwtVerify(token)
    
    // Check if admin exists in database
    const admin = await request.server.prisma.admin.findUnique({
      where: { id: decoded.adminId },
      select: { id: true, email: true, name: true }
    })

    if (!admin) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Admin not found'
      })
    }

    // Add admin info to request
    request.admin = admin
    
  } catch (error) {
    request.log.error('Authentication error:', error)
    return reply.status(401).send({
      error: 'Unauthorized',
      message: 'Invalid or expired token'
    })
  }
}

/**
 * Optional authentication middleware - doesn't fail if no token
 */
export const optionalAuth = async (request, reply) => {
  try {
    const authHeader = request.headers.authorization
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const decoded = await request.jwtVerify(token)

      const admin = await request.server.prisma.admin.findUnique({
        where: { id: decoded.adminId },
        select: { id: true, email: true, name: true }
      })

      if (admin) {
        request.admin = admin
      }
    }
  } catch (error) {
    // Silently fail for optional auth
    request.log.debug('Optional auth failed:', error.message)
  }
}

/**
 * Authentication middleware for protecting user routes (Firebase users)
 */
export const authenticateUser = async (request, reply) => {
  try {
    // Check for Authorization header
    const authHeader = request.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Missing or invalid authorization header'
      })
    }

    // Extract Firebase ID token
    const idToken = authHeader.substring(7)

    // Verify Firebase ID token
    const decodedToken = await request.server.firebaseAdmin.auth().verifyIdToken(idToken)

    // Get user from database
    const user = await request.server.prisma.user.findUnique({
      where: { googleId: decodedToken.uid },
      select: {
        id: true,
        email: true,
        name: true,
        nickname: true,
        profilePicture: true,
        theme: true,
        language: true,
        isActive: true
      }
    })

    if (!user) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'User not found'
      })
    }

    if (!user.isActive) {
      return reply.status(403).send({
        error: 'Forbidden',
        message: 'User account is deactivated'
      })
    }

    // Add user info to request
    request.user = user
    request.firebaseUser = decodedToken

  } catch (error) {
    request.log.error('User authentication error:', error)
    return reply.status(401).send({
      error: 'Unauthorized',
      message: 'Invalid or expired Firebase token'
    })
  }
}

// Aliases for compatibility
export const authMiddleware = authenticateAdmin
export const optionalAuthMiddleware = optionalAuth
