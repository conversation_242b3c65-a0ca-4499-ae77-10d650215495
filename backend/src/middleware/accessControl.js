import { AccessControlService } from '../services/accessControlService.js'
import { errorResponse } from '../utils/response.js'

/**
 * Middleware to check subject access
 * @param {string} subjectIdParam - Parameter name for subject ID (default: 'subjectId')
 */
export function requireSubjectAccess(subjectIdParam = 'subjectId') {
  return async (request, reply) => {
    try {
      const userId = request.user?.userId || request.admin?.adminId
      const subjectId = request.params[subjectIdParam] || request.body[subjectIdParam] || request.query[subjectIdParam]

      if (!subjectId) {
        return errorResponse(reply, 'Subject ID is required', 400)
      }

      // Admins have access to everything
      if (request.admin?.adminId) {
        return
      }

      const access = await AccessControlService.checkSubjectAccess(userId, subjectId)

      if (!access.hasAccess) {
        return errorResponse(reply, 'Access denied. You need a subscription or purchase to access this subject.', 403)
      }

      // Add access info to request for use in controllers
      request.subjectAccess = access
    } catch (error) {
      console.error('Access control error:', error)
      return errorResponse(reply, 'Access control check failed', 500)
    }
  }
}

/**
 * Middleware to check chapter access
 * @param {string} chapterIdParam - Parameter name for chapter ID (default: 'chapterId')
 */
export function requireChapterAccess(chapterIdParam = 'chapterId') {
  return async (request, reply) => {
    try {
      const userId = request.user?.userId || request.admin?.adminId
      const chapterId = request.params[chapterIdParam] || request.body[chapterIdParam] || request.query[chapterIdParam]

      if (!chapterId) {
        return errorResponse(reply, 'Chapter ID is required', 400)
      }

      // Admins have access to everything
      if (request.admin?.adminId) {
        return
      }

      const access = await AccessControlService.checkChapterAccess(userId, chapterId)

      if (!access.hasAccess) {
        return errorResponse(reply, 'Access denied. You need a subscription or purchase to access this chapter.', 403)
      }

      // Add access info to request for use in controllers
      request.chapterAccess = access
    } catch (error) {
      console.error('Access control error:', error)
      return errorResponse(reply, 'Access control check failed', 500)
    }
  }
}

/**
 * Middleware to check MCQ access (requires full access, not preview)
 * @param {string} chapterIdParam - Parameter name for chapter ID (default: 'chapterId')
 */
export function requireMCQAccess(chapterIdParam = 'chapterId') {
  return async (request, reply) => {
    try {
      const userId = request.user?.userId || request.admin?.adminId
      const chapterId = request.params[chapterIdParam] || request.body[chapterIdParam] || request.query[chapterIdParam]

      if (!chapterId) {
        return errorResponse(reply, 'Chapter ID is required', 400)
      }

      // Admins have access to everything
      if (request.admin?.adminId) {
        return
      }

      const access = await AccessControlService.checkMCQAccess(userId, chapterId)

      if (!access.hasAccess) {
        return errorResponse(reply, 'Access denied. MCQs require a subscription or purchase. Preview access is not sufficient.', 403)
      }

      // Add access info to request for use in controllers
      request.mcqAccess = access
    } catch (error) {
      console.error('Access control error:', error)
      return errorResponse(reply, 'Access control check failed', 500)
    }
  }
}

/**
 * Middleware to check Viva access (requires full access, not preview)
 * @param {string} chapterIdParam - Parameter name for chapter ID (default: 'chapterId')
 */
export function requireVivaAccess(chapterIdParam = 'chapterId') {
  return async (request, reply) => {
    try {
      const userId = request.user?.userId || request.admin?.adminId
      const chapterId = request.params[chapterIdParam] || request.body[chapterIdParam] || request.query[chapterIdParam]

      if (!chapterId) {
        return errorResponse(reply, 'Chapter ID is required', 400)
      }

      // Admins have access to everything
      if (request.admin?.adminId) {
        return
      }

      const access = await AccessControlService.checkVivaAccess(userId, chapterId)

      if (!access.hasAccess) {
        return errorResponse(reply, 'Access denied. Viva Q&A requires a subscription or purchase. Preview access is not sufficient.', 403)
      }

      // Add access info to request for use in controllers
      request.vivaAccess = access
    } catch (error) {
      console.error('Access control error:', error)
      return errorResponse(reply, 'Access control check failed', 500)
    }
  }
}

/**
 * Middleware to check admin access
 */
export function requireAdminAccess() {
  return async (request, reply) => {
    try {
      const userId = request.user?.userId || request.admin?.adminId

      if (!userId) {
        return errorResponse(reply, 'Authentication required', 401)
      }

      const isAdmin = await AccessControlService.isAdmin(userId)

      if (!isAdmin) {
        return errorResponse(reply, 'Admin access required', 403)
      }
    } catch (error) {
      console.error('Admin access control error:', error)
      return errorResponse(reply, 'Admin access check failed', 500)
    }
  }
}

/**
 * Optional access control - adds access info to request without blocking
 * @param {string} subjectIdParam - Parameter name for subject ID (default: 'subjectId')
 */
export function optionalSubjectAccess(subjectIdParam = 'subjectId') {
  return async (request, reply) => {
    try {
      const userId = request.user?.userId || request.admin?.adminId
      const subjectId = request.params[subjectIdParam] || request.body[subjectIdParam] || request.query[subjectIdParam]

      if (!subjectId || !userId) {
        request.subjectAccess = { hasAccess: false, accessType: null, source: null }
        return
      }

      // Admins have access to everything
      if (request.admin?.adminId) {
        request.subjectAccess = { hasAccess: true, accessType: 'ADMIN', source: 'admin' }
        return
      }

      const access = await AccessControlService.checkSubjectAccess(userId, subjectId)
      request.subjectAccess = access
    } catch (error) {
      console.error('Optional access control error:', error)
      request.subjectAccess = { hasAccess: false, accessType: null, source: null }
    }
  }
}
