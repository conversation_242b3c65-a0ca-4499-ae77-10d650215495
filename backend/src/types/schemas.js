import { z } from 'zod'

// Auth Schemas
export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters')
})

export const createAdminSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters')
})

// Domain Schemas
export const createDomainSchema = z.object({
  name: z.string().min(1, 'Domain name is required'),
  description: z.string().optional(),
  order: z.number().int().min(0).default(0)
})

export const updateDomainSchema = z.object({
  name: z.string().min(1, 'Domain name is required').optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  order: z.number().int().min(0).optional()
})

// Subject Schemas
export const createSubjectSchema = z.object({
  name: z.string().min(1, 'Subject name is required'),
  description: z.string().optional(),
  domainId: z.string().cuid('Invalid domain ID'),
  order: z.number().int().min(0).default(0)
})

export const updateSubjectSchema = z.object({
  name: z.string().min(1, 'Subject name is required').optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  order: z.number().int().min(0).optional()
})

// Chapter Schemas
export const createChapterSchema = z.object({
  title: z.string().min(1, 'Chapter title is required'),
  description: z.string().optional(),
  content: z.string().optional(),
  subjectId: z.string().cuid('Invalid subject ID'),
  isActive: z.boolean().default(true),
  isDraft: z.boolean().default(false),
  isAIGenerated: z.boolean().default(false),
  order: z.number().int().min(0).default(0),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional()
})

export const updateChapterSchema = z.object({
  title: z.string().min(1, 'Chapter title is required').optional(),
  description: z.string().optional(),
  content: z.string().optional(),
  isActive: z.boolean().optional(),
  isDraft: z.boolean().optional(),
  isAIGenerated: z.boolean().optional(),
  order: z.number().int().min(0).optional(),
  updatedBy: z.string().optional()
})

// MCQ Schemas
export const createMCQSchema = z.object({
  question: z.string().min(1, 'Question is required'),
  optionA: z.string().min(1, 'Option A is required'),
  optionB: z.string().min(1, 'Option B is required'),
  optionC: z.string().min(1, 'Option C is required'),
  optionD: z.string().min(1, 'Option D is required'),
  correctAnswer: z.enum(['A', 'B', 'C', 'D'], {
    errorMap: () => ({ message: 'Correct answer must be A, B, C, or D' })
  }),
  explanation: z.string().optional(),
  chapterId: z.string().cuid('Invalid chapter ID'),
  isActive: z.boolean().default(true),
  isDraft: z.boolean().default(false),
  isAIGenerated: z.boolean().default(false),
  order: z.number().int().min(0).default(0),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional()
})

export const updateMCQSchema = z.object({
  question: z.string().min(1, 'Question is required').optional(),
  optionA: z.string().min(1, 'Option A is required').optional(),
  optionB: z.string().min(1, 'Option B is required').optional(),
  optionC: z.string().min(1, 'Option C is required').optional(),
  optionD: z.string().min(1, 'Option D is required').optional(),
  correctAnswer: z.enum(['A', 'B', 'C', 'D']).optional(),
  explanation: z.string().optional(),
  isActive: z.boolean().optional(),
  isDraft: z.boolean().optional(),
  isAIGenerated: z.boolean().optional(),
  order: z.number().int().min(0).optional(),
  updatedBy: z.string().optional()
})

// Viva Q&A Schemas
export const createVivaQASchema = z.object({
  question: z.string().min(1, 'Question is required'),
  answer: z.string().min(1, 'Answer is required'),
  chapterId: z.string().cuid('Invalid chapter ID'),
  isActive: z.boolean().default(true),
  isDraft: z.boolean().default(false),
  isAIGenerated: z.boolean().default(false),
  order: z.number().int().min(0).default(0),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional()
})

export const updateVivaQASchema = z.object({
  question: z.string().min(1, 'Question is required').optional(),
  answer: z.string().min(1, 'Answer is required').optional(),
  isActive: z.boolean().optional(),
  isDraft: z.boolean().optional(),
  isAIGenerated: z.boolean().optional(),
  order: z.number().int().min(0).optional(),
  updatedBy: z.string().optional()
})

// Subscription Plan Schemas
export const createSubscriptionPlanSchema = z.object({
  name: z.string().min(1, 'Plan name is required'),
  description: z.string().optional(),
  price: z.number().positive('Price must be positive'),
  duration: z.number().int().positive('Duration must be positive (in days)'),
  features: z.array(z.string()).optional()
})

export const updateSubscriptionPlanSchema = z.object({
  name: z.string().min(1, 'Plan name is required').optional(),
  description: z.string().optional(),
  price: z.number().positive('Price must be positive').optional(),
  duration: z.number().int().positive('Duration must be positive (in days)').optional(),
  features: z.array(z.string()).optional(),
  isActive: z.boolean().optional()
})

// User Schemas
export const createUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().optional()
})

export const updateUserSchema = z.object({
  email: z.string().email('Invalid email format').optional(),
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  phone: z.string().optional(),
  isActive: z.boolean().optional()
})

// User Subscription Schemas
export const createUserSubscriptionSchema = z.object({
  userId: z.string().cuid('Invalid user ID'),
  planId: z.string().cuid('Invalid plan ID'),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
})

export const updateUserSubscriptionSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  isActive: z.boolean().optional()
})

// Query parameter schemas
export const paginationSchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 100))
})

export const searchSchema = z.object({
  search: z.string().optional(),
  isActive: z.string().transform(val => val === 'true' ? true : val === 'false' ? false : undefined).optional()
})
