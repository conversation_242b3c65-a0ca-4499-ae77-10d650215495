import { z } from 'zod'

// Auth Schemas
export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters')
})

export const createAdminSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters')
})

// Domain Schemas
export const createDomainSchema = z.object({
  name: z.string().min(1, 'Domain name is required'),
  description: z.string().optional(),
  order: z.number().int().min(0).default(0)
})

export const updateDomainSchema = z.object({
  name: z.string().min(1, 'Domain name is required').optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  order: z.number().int().min(0).optional()
})

// Subject Schemas
export const createSubjectSchema = z.object({
  name: z.string().min(1, 'Subject name is required'),
  description: z.string().optional(),
  domainId: z.string().cuid('Invalid domain ID'),
  order: z.number().int().min(0).default(0)
})

export const updateSubjectSchema = z.object({
  name: z.string().min(1, 'Subject name is required').optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  order: z.number().int().min(0).optional()
})

// Chapter Schemas
export const createChapterSchema = z.object({
  title: z.string().min(1, 'Chapter title is required'),
  description: z.string().optional(),
  content: z.string().optional(),
  subjectId: z.string().cuid('Invalid subject ID'),
  isActive: z.boolean().default(true),
  isDraft: z.boolean().default(false),
  isAIGenerated: z.boolean().default(false),
  order: z.number().int().min(0).default(0),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional()
})

export const updateChapterSchema = z.object({
  title: z.string().min(1, 'Chapter title is required').optional(),
  description: z.string().optional(),
  content: z.string().optional(),
  isActive: z.boolean().optional(),
  isDraft: z.boolean().optional(),
  isAIGenerated: z.boolean().optional(),
  order: z.number().int().min(0).optional(),
  updatedBy: z.string().optional()
})

// MCQ Schemas
export const createMCQSchema = z.object({
  question: z.string().min(1, 'Question is required'),
  optionA: z.string().min(1, 'Option A is required'),
  optionB: z.string().min(1, 'Option B is required'),
  optionC: z.string().min(1, 'Option C is required'),
  optionD: z.string().min(1, 'Option D is required'),
  correctAnswer: z.enum(['A', 'B', 'C', 'D'], {
    errorMap: () => ({ message: 'Correct answer must be A, B, C, or D' })
  }),
  explanation: z.string().optional(),
  reference: z.string().optional(),
  chapterId: z.string().cuid('Invalid chapter ID'),
  isActive: z.boolean().default(true),
  isDraft: z.boolean().default(false),
  isAIGenerated: z.boolean().default(false),
  order: z.number().int().min(0).default(0),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional()
})

export const updateMCQSchema = z.object({
  question: z.string().min(1, 'Question is required').optional(),
  optionA: z.string().min(1, 'Option A is required').optional(),
  optionB: z.string().min(1, 'Option B is required').optional(),
  optionC: z.string().min(1, 'Option C is required').optional(),
  optionD: z.string().min(1, 'Option D is required').optional(),
  correctAnswer: z.enum(['A', 'B', 'C', 'D']).optional(),
  explanation: z.string().optional(),
  reference: z.string().optional(),
  isActive: z.boolean().optional(),
  isDraft: z.boolean().optional(),
  isAIGenerated: z.boolean().optional(),
  order: z.number().int().min(0).optional(),
  updatedBy: z.string().optional()
})

// Viva Q&A Schemas
export const createVivaQASchema = z.object({
  question: z.string().min(1, 'Question is required'),
  answer: z.string().min(1, 'Answer is required'),
  reference: z.string().optional(),
  chapterId: z.string().cuid('Invalid chapter ID'),
  isActive: z.boolean().default(true),
  isDraft: z.boolean().default(false),
  isAIGenerated: z.boolean().default(false),
  order: z.number().int().min(0).default(0),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional()
})

export const updateVivaQASchema = z.object({
  question: z.string().min(1, 'Question is required').optional(),
  answer: z.string().min(1, 'Answer is required').optional(),
  reference: z.string().optional(),
  isActive: z.boolean().optional(),
  isDraft: z.boolean().optional(),
  isAIGenerated: z.boolean().optional(),
  order: z.number().int().min(0).optional(),
  updatedBy: z.string().optional()
})

// Enhanced Subscription Plan Schemas
export const createSubscriptionPlanSchema = z.object({
  name: z.string().min(1, 'Plan name is required'),
  description: z.string().optional(),
  price: z.number().positive('Price must be positive'),
  duration: z.number().int().positive('Duration must be positive (in days)'),
  discountPercentage: z.number().min(0).max(100).optional(),
  subjectIds: z.array(z.string().cuid('Invalid subject ID')).min(1, 'At least one subject is required')
})

export const updateSubscriptionPlanSchema = z.object({
  name: z.string().min(1, 'Plan name is required').optional(),
  description: z.string().optional(),
  price: z.number().positive('Price must be positive').optional(),
  duration: z.number().int().positive('Duration must be positive (in days)').optional(),
  discountPercentage: z.number().min(0).max(100).optional(),
  isActive: z.boolean().optional(),
  subjectIds: z.array(z.string().cuid('Invalid subject ID')).optional()
})

// Subject Pricing Schemas
export const createSubjectPricingSchema = z.object({
  subjectId: z.string().cuid('Invalid subject ID'),
  price: z.number().positive('Price must be positive')
})

export const updateSubjectPricingSchema = z.object({
  price: z.number().positive('Price must be positive').optional(),
  isActive: z.boolean().optional()
})

// User Subscription Schemas
export const createUserSubscriptionSchema = z.object({
  userId: z.string().cuid('Invalid user ID'),
  planId: z.string().cuid('Invalid plan ID'),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime(),
  createdBy: z.string().optional()
})

export const updateUserSubscriptionSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  isActive: z.boolean().optional()
})

// User Subject Access Schemas
export const createUserSubjectAccessSchema = z.object({
  userId: z.string().cuid('Invalid user ID'),
  subjectId: z.string().cuid('Invalid subject ID'),
  accessType: z.enum(['PURCHASED', 'GRANTED', 'SUBSCRIPTION']),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  createdBy: z.string().optional()
})

export const updateUserSubjectAccessSchema = z.object({
  accessType: z.enum(['PURCHASED', 'GRANTED', 'SUBSCRIPTION']).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  isActive: z.boolean().optional()
})

// User Schemas
export const createUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional()
})

export const updateUserSchema = z.object({
  email: z.string().email('Invalid email format').optional(),
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  nickname: z.string().min(2, 'Nickname must be at least 2 characters').optional(),
  phone: z.string().optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
  isActive: z.boolean().optional()
})

// ===== LMS FEATURE SCHEMAS =====

// Chapter Progress Schemas
export const updateChapterProgressSchema = z.object({
  readingProgress: z.number().min(0).max(100),
  timeSpent: z.number().int().min(0),
  lastPosition: z.number().int().min(0),
  isCompleted: z.boolean().optional()
})

// MCQ Attempt Schemas
export const createMCQAttemptSchema = z.object({
  mcqId: z.string().cuid('Invalid MCQ ID'),
  examId: z.string().cuid('Invalid exam ID').optional(),
  selectedAnswer: z.enum(['A', 'B', 'C', 'D'], 'Answer must be A, B, C, or D'),
  timeSpent: z.number().int().min(0).default(0)
})

// MCQ Exam Schemas
export const createMCQExamSchema = z.object({
  chapterId: z.string().cuid('Invalid chapter ID').optional(),
  subjectId: z.string().cuid('Invalid subject ID').optional(),
  examType: z.enum(['chapter', 'random', 'subject'], 'Invalid exam type'),
  totalQuestions: z.number().int().min(1).max(50)
})

export const updateMCQExamSchema = z.object({
  correctAnswers: z.number().int().min(0),
  score: z.number().min(0).max(100),
  timeSpent: z.number().int().min(0),
  isCompleted: z.boolean()
})

// Viva Attempt Schemas
export const createVivaAttemptSchema = z.object({
  vivaQAId: z.string().cuid('Invalid Viva Q&A ID'),
  answerText: z.string().optional(),
  audioUrl: z.string().url('Invalid audio URL').optional(),
  timeSpent: z.number().int().min(0).default(0)
})

export const updateVivaAttemptSchema = z.object({
  transcript: z.string().optional(),
  aiScore: z.number().min(0).max(10).optional(),
  aiFeedback: z.string().optional()
})

// Study Plan Schemas
export const createStudyPlanSchema = z.object({
  name: z.string().min(1, 'Study plan name is required'),
  description: z.string().optional(),
  targetDate: z.string().datetime('Invalid target date').optional(),
  chapterIds: z.array(z.string().cuid('Invalid chapter ID')).min(1, 'At least one chapter is required')
})

export const updateStudyPlanSchema = z.object({
  name: z.string().min(1, 'Study plan name is required').optional(),
  description: z.string().optional(),
  targetDate: z.string().datetime('Invalid target date').optional(),
  isActive: z.boolean().optional(),
  isCompleted: z.boolean().optional()
})

// Daily Goal Schemas
export const createDailyGoalSchema = z.object({
  date: z.string().date('Invalid date'),
  goalType: z.enum(['chapters', 'mcqs', 'time'], 'Invalid goal type'),
  targetValue: z.number().int().min(1, 'Target value must be positive')
})

export const updateDailyGoalSchema = z.object({
  currentValue: z.number().int().min(0),
  isCompleted: z.boolean().optional()
})

// Chat Schemas
export const createChatMessageSchema = z.object({
  chatRoomId: z.string().cuid('Invalid chat room ID'),
  message: z.string().min(1, 'Message cannot be empty').max(1000, 'Message too long'),
  messageType: z.enum(['text', 'file', 'image']).default('text'),
  fileUrl: z.string().url('Invalid file URL').optional()
})

export const updateChatParticipantSchema = z.object({
  role: z.enum(['member', 'moderator', 'admin']).optional(),
  canSend: z.boolean().optional()
})

// ===== POINTS & REWARDS SCHEMAS =====

export const createPointTransactionSchema = z.object({
  type: z.enum(['earned', 'spent', 'expired']),
  category: z.enum(['daily_login', 'study_time', 'achievement', 'referral', 'subscription_discount', 'subject_discount']),
  amount: z.number().int(),
  description: z.string().min(1).max(255),
  metadata: z.string().optional()
})

export const createReferralSchema = z.object({
  referralCode: z.string().min(6).max(20),
  signupReward: z.number().int().min(0).default(10),
  purchaseReward: z.number().min(0).max(1).optional() // Percentage (0-1)
})

export const redeemPointsSchema = z.object({
  pointsToUse: z.number().int().min(1),
  purchaseType: z.enum(['subscription', 'subject']),
  itemId: z.string().cuid() // planId or subjectId
})

export const generateReferralCodeSchema = z.object({
  customCode: z.string().min(6).max(20).regex(/^[A-Z0-9]+$/).optional()
})

export const applyReferralCodeSchema = z.object({
  referralCode: z.string().min(6).max(20)
})

export const dailyLoginSchema = z.object({
  loginDate: z.string().datetime().optional()
})

export const studyTimeRewardSchema = z.object({
  studyTimeMinutes: z.number().int().min(1),
  activityType: z.enum(['reading', 'mcq', 'viva'])
})

// Query parameter schemas
export const paginationSchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 100))
})

export const searchSchema = z.object({
  search: z.string().optional(),
  isActive: z.string().transform(val => val === 'true' ? true : val === 'false' ? false : undefined).optional()
})
