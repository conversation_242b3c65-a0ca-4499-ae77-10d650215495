export const handleError = (error, reply) => {
  console.error('Error:', error)
  
  // Handle Prisma errors
  if (error.code === 'P2002') {
    return reply.status(409).send({
      success: false,
      message: 'A record with this data already exists',
      error: 'DUPLICATE_ENTRY'
    })
  }
  
  if (error.code === 'P2025') {
    return reply.status(404).send({
      success: false,
      message: 'Record not found',
      error: 'NOT_FOUND'
    })
  }
  
  // Handle validation errors
  if (error.name === 'ZodError') {
    return reply.status(400).send({
      success: false,
      message: 'Validation failed',
      error: 'VALIDATION_ERROR',
      details: error.errors
    })
  }
  
  // Handle generic errors
  return reply.status(500).send({
    success: false,
    message: error.message || 'Internal server error',
    error: 'INTERNAL_ERROR'
  })
}

export const asyncHandler = (fn) => {
  return async (request, reply) => {
    try {
      await fn(request, reply)
    } catch (error) {
      handleError(error, reply)
    }
  }
}

// Import and re-export from response utils
import { handlePrismaError as _handlePrismaError } from './response.js'
export const handlePrismaError = _handlePrismaError
