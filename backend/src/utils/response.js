/**
 * Standard success response format
 * @param {any} data - Response data
 * @param {string} message - Success message
 * @param {object} meta - Additional metadata (pagination, etc.)
 * @returns {object} - Formatted response
 */
export const successResponse = (data, message = 'Success', meta = null) => {
  const response = {
    success: true,
    message,
    data
  }
  
  if (meta) {
    response.meta = meta
  }
  
  return response
}

/**
 * Standard error response format
 * @param {string} message - Error message
 * @param {string} error - Error type/code
 * @param {any} details - Additional error details
 * @returns {object} - Formatted error response
 */
export const errorResponse = (message, error = 'Error', details = null) => {
  const response = {
    success: false,
    error,
    message
  }
  
  if (details) {
    response.details = details
  }
  
  return response
}

/**
 * Validation error response format
 * @param {array} errors - Validation errors from Zod
 * @returns {object} - Formatted validation error response
 */
export const validationErrorResponse = (errors) => {
  return {
    success: false,
    error: 'Validation Error',
    message: 'Invalid input data',
    details: errors.map(err => ({
      field: err.path.join('.'),
      message: err.message
    }))
  }
}

/**
 * Pagination metadata
 * @param {number} page - Current page
 * @param {number} limit - Items per page
 * @param {number} total - Total items
 * @returns {object} - Pagination metadata
 */
export const paginationMeta = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit)
  
  return {
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }
}

/**
 * Get pagination metadata (alias for paginationMeta)
 * @param {number} page - Current page
 * @param {number} limit - Items per page
 * @param {number} total - Total items
 * @returns {object} - Pagination metadata
 */
export const getPaginationMeta = (page, limit, total) => {
  return paginationMeta(page, limit, total)
}

/**
 * Handle Prisma errors and convert to user-friendly messages
 * @param {Error} error - Prisma error
 * @returns {object} - Error response
 */
export const handlePrismaError = (error) => {
  if (error.code === 'P2002') {
    // Unique constraint violation
    const field = error.meta?.target?.[0] || 'field'
    return errorResponse(
      `A record with this ${field} already exists`,
      'Duplicate Entry',
      { field, code: error.code }
    )
  }

  if (error.code === 'P2025') {
    // Record not found
    return errorResponse(
      'Record not found',
      'Not Found',
      { code: error.code }
    )
  }

  if (error.code === 'P2003') {
    // Foreign key constraint violation
    return errorResponse(
      'Cannot perform this operation due to related records',
      'Constraint Violation',
      { code: error.code }
    )
  }

  // Generic database error
  return errorResponse(
    'Database operation failed',
    'Database Error',
    { code: error.code }
  )
}
