import axios from 'axios'

const API_BASE = 'http://localhost:3000/api'

async function testImageUploadSystem() {
  try {
    console.log('Testing image upload system...')

    // 1. Login to get auth token
    console.log('\n1. Logging in...')
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    })

    const token = loginResponse.data.data.token
    console.log('✅ Login successful')

    // Set up axios with auth header
    const authApi = axios.create({
      baseURL: API_BASE,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    // 2. Test slider endpoints
    console.log('\n2. Testing slider endpoints...')
    
    // Get all sliders (admin)
    const slidersResponse = await authApi.get('/sliders')
    console.log(`✅ Retrieved ${slidersResponse.data.data.length} sliders`)

    // Get active sliders (public)
    const activeSlidersResponse = await axios.get(`${API_BASE}/sliders/active`)
    console.log(`✅ Retrieved ${activeSlidersResponse.data.data.length} active sliders`)

    // 3. Test subjects with featured flag
    console.log('\n3. Testing featured subjects...')
    const subjectsResponse = await authApi.get('/subjects?page=1&limit=10')
    const featuredSubjects = subjectsResponse.data.data.filter(s => s.isFeatured)
    console.log(`✅ Found ${featuredSubjects.length} featured subjects`)

    // Show featured subjects details
    featuredSubjects.forEach(subject => {
      console.log(`   - ${subject.name} (slug: ${subject.slug}, image: ${subject.imageUrl})`)
    })

    // 4. Test image upload endpoints (without actual file)
    console.log('\n4. Testing upload endpoints structure...')
    
    // Test upload endpoint exists (will fail without file, but should not be 404)
    try {
      await authApi.post('/uploads/subject')
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Subject upload endpoint exists (400 = no file provided)')
      } else {
        console.log(`❌ Subject upload endpoint error: ${error.response?.status}`)
      }
    }

    try {
      await authApi.post('/uploads/slider')
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Slider upload endpoint exists (400 = no file provided)')
      } else {
        console.log(`❌ Slider upload endpoint error: ${error.response?.status}`)
      }
    }

    // 5. Test static file serving
    console.log('\n5. Testing static file serving...')
    try {
      const staticResponse = await axios.get('http://localhost:3000/uploads/slider/slider_sample_1.jpg')
      console.log('❌ Static file found (should be 404 for sample files)')
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('✅ Static file serving configured (404 for non-existent files)')
      } else {
        console.log(`❌ Static file serving error: ${error.response?.status}`)
      }
    }

    console.log('\n🎉 Image upload system test completed!')

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message)
  }
}

testImageUploadSystem()
