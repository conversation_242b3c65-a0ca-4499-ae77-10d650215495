{"name": "fast-json-stringify", "version": "6.0.1", "description": "Stringify your JSON at max speed", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"bench": "node ./benchmark/bench.js", "bench:cmp": "node ./benchmark/bench-cmp-branch.js", "bench:cmp:ci": "node ./benchmark/bench-cmp-branch.js --ci", "benchmark": "node ./benchmark/bench-cmp-lib.js", "lint": "eslint", "lint:fix": "eslint --fix", "test:typescript": "tsd", "test:unit": "c8 node --test", "test": "npm run test:unit && npm run test:typescript"}, "precommit": ["lint", "test"], "repository": {"type": "git", "url": "git+https://github.com/fastify/fast-json-stringify.git"}, "keywords": ["json", "stringify", "schema", "fast"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "url": "http://delved.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fast-json-stringify/issues"}, "homepage": "https://github.com/fastify/fast-json-stringify#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@sinclair/typebox": "^0.34.3", "benchmark": "^2.1.4", "c8": "^10.1.2", "cli-select": "^1.1.2", "compile-json-stringify": "^0.1.2", "eslint": "^9.17.0", "fast-json-stringify": ".", "is-my-json-valid": "^2.20.6", "neostandard": "^0.12.0", "simple-git": "^3.23.0", "tsd": "^0.31.0", "webpack": "^5.90.3"}, "dependencies": {"@fastify/merge-json-schemas": "^0.2.0", "ajv": "^8.12.0", "ajv-formats": "^3.0.1", "fast-uri": "^3.0.0", "json-schema-ref-resolver": "^2.0.0", "rfdc": "^1.2.0"}, "runkitExampleFilename": "./examples/example.js"}