'use strict'

// defined by Node.js http module, a snapshot from Node.js 22.9.0
const httpMethods = [
  'ACL', 'BIND', 'CHECKOUT', 'CONNECT', 'COPY', 'DELETE',
  'GET', 'HEAD', 'LINK', 'LOCK', '<PERSON>-<PERSON><PERSON><PERSON>', '<PERSON>R<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>VI<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'MOVE', 'NOTIFY', 'OPTIONS',
  'PATCH', 'POST', 'PROPFIND', 'PROPPATCH', 'PURGE', 'PUT', 'QUERY',
  'REBIND', 'REPORT', 'SEARCH', 'SOURCE', 'SUBSCRIBE', 'TRACE',
  '<PERSON>BIND', '<PERSON>LIN<PERSON>', 'UNLOCK', 'UNSUBSCRIBE'
]

module.exports = httpMethods
