name: Node CI

on:
  push:
    branches:
      - main
      - next
  pull_request:

permissions:
  contents: read

jobs:
  test:
    name: Test
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version:
          - 20
          - 22
        os: 
          - ubuntu-latest
          - windows-latest
          - macOS-latest

    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        persist-credentials: false

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@cdca7365b2dadb8aad0a33bc7601856ffabcc48e # v4.3.0
      with:
        check-latest: true
        node-version: ${{ matrix.node-version }}

    - name: Install
      run: |
        npm install --ignore-scripts

    - name: Lint
      run: |
        npm run test:lint

    - name: Test
      run: |
        npm test

    - name: Type Definitions
      run: |
        npm run test:typescript

  automerge:
    if: >
      github.event_name == 'pull_request' && github.event.pull_request.user.login == 'dependabot[bot]'
    needs: test
    runs-on: ubuntu-latest
    permissions:
      actions: write
      pull-requests: write
      contents: write
    steps:
    - uses: fastify/github-action-merge-dependabot@e820d631adb1d8ab16c3b93e5afe713450884a4a # v3.11.1
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
