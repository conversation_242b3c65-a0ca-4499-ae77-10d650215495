{"name": "json-schema-ref-resolver", "version": "2.0.1", "description": "JSON schema reference resolver", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "test:unit": "c8 --100 node --test", "test:typescript": "tsd", "test": "npm run lint && npm run test:unit && npm run test:typescript"}, "precommit": ["lint", "test"], "repository": {"type": "git", "url": "git+https://github.com/fastify/json-schema-ref-resolver.git"}, "bugs": {"url": "https://github.com/fastify/json-schema-ref-resolver/issues"}, "keywords": ["json", "schema", "reference", "openapi", "swagger"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://james.sumners.info"}, {"name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>", "url": "https://heyhey.to/G"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "homepage": "https://github.com/fastify/json-schema-ref-resolver#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "dependencies": {"dequal": "^2.0.3"}, "devDependencies": {"@fastify/pre-commit": "^2.1.0", "c8": "^10.1.3", "eslint": "^9.17.0", "neostandard": "^0.12.0", "tsd": "^0.31.0"}}