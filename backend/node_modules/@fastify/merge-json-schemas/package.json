{"name": "@fastify/merge-json-schemas", "version": "0.2.1", "description": "Builds a logical conjunction (AND) of multiple JSON schemas", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:types", "test:unit": "c8 --100 node --test", "test:types": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/merge-json-schemas.git"}, "keywords": ["json", "schema", "merge", "allOf"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}, {"name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>", "url": "https://heyhey.to/G"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/merge-json-schemas/issues"}, "homepage": "https://github.com/fastify/merge-json-schemas#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"@fastify/pre-commit": "^2.1.0", "c8": "^10.1.3", "eslint": "^9.17.0", "neostandard": "^0.12.0", "tsd": "^0.31.2"}, "dependencies": {"dequal": "^2.0.3"}}